import { createRouter, createWebHistory, type RouteLocationNormalized } from 'vue-router'
import routes from './routes'
// import setPageTitle from '@/utils/set-page-title'
import { unmountLoading } from '@/core/loading'
import { isTokenValid } from '@/api/common'

export const router = createRouter({
  history: createWebHistory(),
  routes,
})

router.beforeEach((to, from, next) => {
  const token = JSON.parse(localStorage.getItem('user'))?.token
  window.$loadingBar.start()

  if (token) {
    isTokenValid({ token: JSON.parse(localStorage.getItem('user') || '{}').token || '' }).then(res => {
      if (!res.data) {
        if (to.matched.some(record => record.meta.requiresAuth)) {
          next({
            path: '/login', // 验证失败要跳转的页面
            query: {
              ticket: to.query.ticket,
              redirect: from.fullPath, // 要传的参数
            },
          })
        } else {
          next()
        }
      }
    })
    if (to.path == '/login') {
      next({ path: '/' })
    } else {
      //   setPageTitle(to.meta.title);

      if (to.matched.length === 0) {
        next('/404')
      } else {
        next()
      }
    }
  } else {
    if (to.matched.some(record => record.meta.requiresAuth)) {
      next({
        path: '/login', // 验证失败要跳转的页面
        query: {
          ticket: to.query.ticket,
          redirect: from.fullPath, // 要传的参数
        },
      })
    } else {
      next()
    }
  }
})

router.afterEach(to => {
  if (to.fullPath !== '/') {
    unmountLoading()

    setTimeout(() => {
      window.$loadingBar.finish()
    }, 200)
  }
})

// 配置路由器
export async function setupRouter(app) {
  app.use(router)

  // await router.isReady() // https://router.vuejs.org/zh/api/index.html#isready
}
