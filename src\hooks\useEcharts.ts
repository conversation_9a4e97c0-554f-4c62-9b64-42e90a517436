import type {
  <PERSON><PERSON><PERSON>O<PERSON>,
  Gau<PERSON><PERSON><PERSON>Option,
  <PERSON><PERSON><PERSON>Option,
  PictorialBarSeriesOption,
  PieSeriesOption,
  RadarSeriesOption,
  ScatterSeriesOption,
} from 'echarts/charts'
import type {
  DatasetComponentOption,
  GridComponentOption,
  LegendComponentOption,
  TitleComponentOption,
  ToolboxComponentOption,
  TooltipComponentOption,
  AxisPointerComponentOption,
  DataZoomComponentOption,
} from 'echarts/components'

import {
  DatasetComponent,
  GridComponent,
  LegendComponent,
  LegendScrollComponent,
  TitleComponent,
  ToolboxComponent,
  TooltipComponent,
  TransformComponent,
  AxisPointerComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
  MarkLineComponent,
} from 'echarts/components'

import { computed, effectScope, nextTick, onScopeDispose, ref, watch } from 'vue'
import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, PictorialB<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'echarts/charts'

import { LabelLayout, UniversalTransition } from 'echarts/features'
import { CanvasRenderer } from 'echarts/renderers'
import { useElementSize } from '@vueuse/core'
import { appConfig } from '@/constants/appConfig.ts'
import { zhCN } from 'naive-ui'

export type ECOption = echarts.ComposeOption<
  | BarSeriesOption
  | LineSeriesOption
  | PieSeriesOption
  | ScatterSeriesOption
  | PictorialBarSeriesOption
  | RadarSeriesOption
  | GaugeSeriesOption
  | TitleComponentOption
  | LegendComponentOption
  | TooltipComponentOption
  | GridComponentOption
  | ToolboxComponentOption
  | DatasetComponentOption
  | DataZoomComponentOption
  | AxisPointerComponentOption
>

echarts.use([
  TitleComponent,
  LegendComponent,
  LegendScrollComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  BarChart,
  LineChart,
  PieChart,
  ScatterChart,
  PictorialBarChart,
  RadarChart,
  GaugeChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer,
  MarkLineComponent,

  AxisPointerComponent,
  DataZoomComponent,
  DataZoomInsideComponent,
  DataZoomSliderComponent,
])

interface ChartHooks {
  onRender?: (chart: echarts.ECharts) => void | Promise<void>
  onUpdated?: (chart: echarts.ECharts) => void | Promise<void>
  onDestroy?: (chart: echarts.ECharts) => void | Promise<void>
}

/**
 * use echarts
 *
 * @param optionsFactory echarts options factory function
 */
export function useEcharts<T extends ECOption>(optionsFactory: () => T, hooks: ChartHooks = {}) {
  const scope = effectScope()

  const domRef = ref<HTMLElement | null>(null)
  const initialSize = { width: 0, height: 0 }
  const { width, height } = useElementSize(domRef, initialSize)

  let chart: echarts.ECharts | null = null
  const chartIns = ref<echarts.ECharts | null>(null)
  const chartOptions: T = optionsFactory()

  const {
    onRender = instance => {
      const textColor = 'rgb(31, 31, 31)'
      const maskColor = 'rgba(0, 0, 0, 0.2)'

      instance.showLoading({
        text: '',
        color: appConfig.common.primaryColor,
        textColor,
        fontSize: 14,
        maskColor,
      })
    },
    onUpdated = instance => {
      instance.hideLoading()
    },
    onDestroy,
  } = hooks

  /**
   * whether can render chart
   *
   * when domRef is ready and initialSize is valid
   */
  function canRender() {
    return domRef.value && initialSize.width > 0 && initialSize.height > 0
  }

  /** is chart rendered */
  function isRendered() {
    return Boolean(domRef.value && chart)
  }

  /**
   * update chart options
   *
   * @param callback callback function
   */
  async function updateOptions(callback: (opts: T, optsFactory: () => T) => ECOption = () => chartOptions) {
    await nextTick()
    if (!isRendered()) return
    const updatedOpts = callback(chartOptions, optionsFactory)

    Object.assign(chartOptions, updatedOpts)

    if (isRendered()) {
      chart?.clear()
    }

    chart?.setOption({ ...updatedOpts, backgroundColor: 'transparent' })

    await onUpdated?.(chart!)
  }

  function setOptions(options: T) {
    chart?.setOption(options)
  }

  /** render chart */
  async function render() {
    if (!isRendered()) {
      const chartTheme = 'dark'

      await nextTick()

      chart = echarts.init(domRef.value, chartTheme)

      chart.setOption({ ...chartOptions, backgroundColor: 'transparent' })

      chartIns.value = chart

      chart?.on('mouseover', { componentType: 'series', seriesType: 'pie' }, params => {
        chart.setOption({
          title: {
            show: false,
          },
        })
      })
      // 鼠标移出数据时
      chart?.on('mouseout', { componentType: 'series', seriesType: 'pie' }, params => {
        chart.setOption({
          title: {
            show: true,
          },
        })
      })

      await onRender?.(chart)
    }
  }

  /** resize chart */
  function resize() {
    chart?.resize()
  }

  /** destroy chart */
  async function destroy() {
    if (!chart) return

    await onDestroy?.(chart)

    chartIns.value?.dispose()
    chartIns.value = null

    // chart?.dispose()
    chart = null
  }

  /** reset chart */
  async function resetChart() {
    await destroy()
    await render()
    await onUpdated?.(chart!)
  }

  /**
   * render chart by size
   *
   * @param w width
   * @param h height
   */
  async function renderChartBySize(w: number, h: number) {
    initialSize.width = w
    initialSize.height = h

    // size is abnormal, destroy chart
    if (!canRender()) {
      await destroy()

      return
    }

    // resize chart
    if (isRendered()) {
      resize()
    }

    // render chart
    await render()
  }

  scope.run(() => {
    watch([width, height], ([newWidth, newHeight]) => {
      isRendered() ? renderChartBySize(newWidth, newHeight) : resetChart()
    })
  })

  onMounted(() => {
    render()
    // simulateHover(chart, 0)
  })

  onScopeDispose(() => {
    destroy()
    scope.stop()
  })
  function simulateHover(charts, dataIndex) {
    const params = {
      componentType: 'series',
      dataIndex: dataIndex,
      dataId: dataIndex,
      dataDimensionIndex: 0,
      dataDimension: 'value',
      seriesIndex: 0,
      seriesType: 'pie',
      name: '', //option.series[0].data[dataIndex].name
    }
    charts?.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: dataIndex,
      tooltip: {
        showContent: true,
        position: [0, 0],
        transitionDuration: 0,
      },
    })
    charts?.dispatchAction({ type: 'updateTooltip', param: params })
  }

  return {
    domRef,
    chartIns,
    updateOptions,
    setOptions,
  }
}
