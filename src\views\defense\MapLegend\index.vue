<template>
  <!-- bg-[rgba(0,0,0,.2)] -->
  <div class="h-3.2rem relative">
    <!-- 水库建设 -->
    <div class="item flex flex-col h-2rem w-1rem mb-0.20rem">
      <div
        v-for="(item, key) in dataSource"
        :key="key"
        class="px-0.06rem w-0.86rem h-0.32rem text-0.14rem flex items-center justify-center m-0.1rem cursor-pointer"
        :style="{
          background:
            currentNum == key
              ? `url(${getImageUrl('map/legend/type-check.png')}) no-repeat center / 100% 100%`
              : `url(${getImageUrl('map/legend/type.png')}) no-repeat center / 100% 100%`,
          color: currentNum == key ? '#09C8FC' : '#fff',
        }"
        @click="typeHandleClick(item, key)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="absolute w-1.8rem h-2.5rem top-0.1rem left-1.1rem flex flex-col" v-if="currentNum == 0">
      <div
        class="w-1.62rem h-0.32rem text-0.14rem text-[#ffffff] font-bold text-center pt-0.08rem mb-0.1rem flex cursor-pointer"
        :style="{
          background: item?.isActive
            ? `url(${getImageUrl('map/legend/index-check.png')}) no-repeat center / 100% 100%`
            : `url(${getImageUrl('map/legend/index.png')}) no-repeat center / 100% 100%`,
        }"
        :class="item.isActive ? 'active' : ''"
        v-for="(item, index) in state.overview.reservoir"
        :key="index"
        @click="handleClick('reservoir', item)"
      >
        <div
          class="h-0.4rem w-0.4rem mt--0.08rem"
          :style="{ background: `url(${getImageUrl(`map/reservoir${item.code}.png`)}) no-repeat center / 100% 100%` }"
        ></div>
        <div class="flex ml-0.1rem text-[#B2DAEA]" :title="item.title">
          {{ item.title }}({{ item.count }})
          <!-- <div class="left-item  mb-0.1rem" ></div> -->
        </div>
      </div>
    </div>
    <div class="absolute w-1.8rem h-2.5rem top-0.1rem left-1.1rem flex flex-col overflow-y-auto" v-if="currentNum == 1">
      <div
        class="w-1.62rem h-0.32rem text-0.14rem text-[#ffffff] font-bold text-center pt-0.08rem mb-0.1rem flex cursor-pointer"
        :style="{
          background: item?.isActive
            ? `url(${getImageUrl('map/legend/index-check.png')}) no-repeat center / 100% 100%`
            : `url(${getImageUrl('map/legend/index.png')}) no-repeat center / 100% 100%`,
        }"
        :class="item.isActive ? 'active' : ''"
        v-for="(item, index) in state.overview.project"
        :key="index"
        @click="handleClick('project', item)"
      >
        <div
          class="h-0.4rem w-0.4rem mt--0.122rem"
          :style="{ background: `url(${getImageUrl(`map/project${item.code}.png`)}) no-repeat center / 68% 68%` }"
        ></div>
        <div class="flex ml-0.1rem text-[#B2DAEA]" :title="item.title">
          {{ item.title }}({{ item.count }})
          <!-- <div class="left-item  mb-0.1rem" ></div> -->
        </div>
      </div>
    </div>
    <div class="absolute w-1.8rem h-2.5rem top-0.1rem left-1.1rem flex flex-col" v-if="currentNum == 2">
      <div
        class="w-1.62rem h-0.32rem text-0.14rem text-[#ffffff] font-bold text-center pt-0.08rem mb-0.1rem flex cursor-pointer"
        :style="{
          background: item?.isActive
            ? `url(${getImageUrl('map/legend/index-check.png')}) no-repeat center / 100% 100%`
            : `url(${getImageUrl('map/legend/index.png')}) no-repeat center / 100% 100%`,
        }"
        :class="item.isActive ? 'active' : ''"
        v-for="(item, index) in state.overview.site"
        :key="index"
        @click="handleClick('site', item)"
      >
        <div
          class="h-0.4rem w-0.4rem mt--0.126rem"
          :style="{ background: `url(${getImageUrl(`map/site${item.code}.png`)}) no-repeat center / 68% 68%` }"
        ></div>
        <div class="flex ml-0.1rem text-[#B2DAEA]" :title="item.title">
          {{ item.title }}({{ item.count }})
          <!-- <div class="left-item  mb-0.1rem" ></div> -->
        </div>
      </div>
    </div>
    <div class="absolute w-1.8rem h-2.5rem top-0.1rem left-1.1rem flex flex-col" v-if="currentNum == 3">
      <div
        class="w-1.62rem h-0.32rem text-0.14rem text-[#ffffff] font-bold text-center pt-0.08rem mb-0.1rem flex cursor-pointer"
        :style="{
          background: item?.isActive
            ? `url(${getImageUrl('map/legend/index-check.png')}) no-repeat center / 100% 100%`
            : `url(${getImageUrl('map/legend/index.png')}) no-repeat center / 100% 100%`,
        }"
        :class="item.isActive ? 'active' : ''"
        v-for="(item, index) in state.overview.canal"
        :key="index"
        @click="handleClick('canal', item)"
      >
        <div
          class="h-0.4rem w-0.4rem mt--0.126rem"
          :style="{ background: `url(${getImageUrl(`map/canal${item.code}.png`)}) no-repeat center / 68% 68%` }"
        ></div>
        <div class="flex ml-0.1rem text-[#B2DAEA]" :title="item.title">
          {{ item.title }}({{ item.count }})
          <!-- <div class="left-item  mb-0.1rem" ></div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx" name="MapLegend">
  import { objectCategoryCountByFirstLevel, getAllCameraList } from '../services'
  import { overview } from '@/constants'
  import axios from 'axios'
  import { over } from 'lodash-es'

  const indicators = [
    {
      key: 1,
      field: 'reservoir',
      label: '水库建设',
    },
    {
      key: 2,
      field: 'project',
      label: '水利工程',
    },

    {
      key: 3,
      field: 'site',
      label: '监测站点',
    },
    {
      key: 4,
      field: 'canal',
      label: '灌区渠系',
    },
  ]

  const state = $ref({
    currentNum: null,
    cameraArr: [],
    typeActive: null,
    loading: false,
    overview: {
      project: [],
      site: [],
      reservoir: [],
      canal: [],
    },
    dataSource: [],
    allSource: {},
    tabLevel1: null,
  })
  // const overview = ref({})
  // overview.value = {
  //   project: [],
  //   site: [],
  //   reservoir: [],
  //   canal: [],
  // }

  const swiperInstance = ref(null)
  const selectSlide = index => {
    if (swiperInstance.value) {
      swiperInstance.value.slideTo(index)
    }
  }

  // const appStore = useAppStore()
  const dataSource = ref([])
  dataSource.value = indicators
  // 触发模块
  const activeItem = defineModel('activeItem')

  const allData = defineModel('allData')
  onMounted(() => {
    init()
  })
  const currentNum = ref(null)

  const typeHandleClick = (item, key) => {
    // currentNum.value = key
    if (currentNum.value == key) {
      // 如果当前菜单已选中，则取消选中状态
      currentNum.value = null
    } else {
      // 否则选中当前菜单
      currentNum.value = key
    }
  }
  const handleClick = (section, item) => {
    let flag = null
    state.overview[section] = state.overview[section].map(el => {
      if (el.code === item.code) {
        el.isActive = !el.isActive
        flag = el.isActive
      }
      return el
    })

    // item.code = item.code == 'HP003' ? 6 : item.code
    // let filterParam = `<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${item.code}</Literal></PropertyIsEqualTo></Or>`
    if (item) {
      let propertyIsEqualTos = null
      if (section == 'project') {
        propertyIsEqualTos = item.codes
          ?.map(
            value => `<PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${value}</Literal></PropertyIsEqualTo>`,
          )
          .join('')
      } else {
        propertyIsEqualTos = `<PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${item.code}</Literal></PropertyIsEqualTo>`
      }

      axios(
        `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=${item.gisLayer}&filter=<Or>${propertyIsEqualTos}</Or>`,
        // `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=${item.gisLayer}&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${item.code}</Literal></PropertyIsEqualTo></Or>`,
      ).then(res => {
        const data = res.data || null
        // activeItem.value = {
        //   ...data,
        //   isActive: flag,
        //   id: section + '-' + item.code,
        //   code: item.code,
        //   section,
        // }
        // if (section == 'site' && item.code == '6') {
        //   const geoJsonFeatures = state.cameraArr?.map(point => ({
        //     type: 'Feature',
        //     geometry: {
        //       type: 'Point',
        //       coordinates: [Number(point.longitude), Number(point.latitude)],
        //     },
        //     properties: {
        //       // 这里添加其他额外的视频信息，如果需要
        //       color: '#5C1CCA',
        //       icon: new URL(`/src/assets/images/map/site6.png`, import.meta.url).href,
        //       id: point.cameraId,
        //       map_class: 6,
        //       object_id: point.cameraId,
        //       object_name: point.cameraName,
        //       object_type: 'MS',
        //     },
        //   }))
        //   activeItem.value = {
        //     ...data,
        //     features: geoJsonFeatures,
        //     isActive: flag,
        //     id: section + '-' + item.code,
        //     code: item.code,
        //     section,
        //   }
        // } else {
        activeItem.value = {
          ...data,
          isActive: flag,
          id: section + '-' + item.code,
          code: item.code,
          section,
        }
        // }
      })
    }
  }

  const init = () => {
    state.loading = true
    const val = 3
    let objectCategory3 = JSON.parse(localStorage.getItem('objectCategory3'))
    let objectCategory4 = JSON.parse(localStorage.getItem('objectCategory4'))
    // objectCategoryCountByFirstLevel({ objectCategoryId: 3 }).then((res: any) => {
    // 存储目录信息
    state.dataSource = (objectCategory3 || []).map((el, idx) => ({
      ...el,
      checked: state.allSource?.[val]?.[idx]?.checked || false,
      indeterminate: state.allSource?.[val]?.[idx]?.indeterminate || false,
      items: el.items.map((ele, index) => ({
        ...ele,
        // color: getColor(tabLevel1Code.value, el.objectCategoryCode, index),
        // icon: getIcon(tabLevel1Code.value, el.objectCategoryCode, index, ele.code),
        checked: state.allSource?.[val]?.[idx]?.items?.[index]?.checked || false,
      })),
    }))

      state.allSource[val] = state.dataSource

    let res1 = objectCategory3
    const HP001Info = res1.find(item => item.objectCategoryCode == 'HP001')
    const HP005Info = res1.find(item => item.objectCategoryCode == 'HP005')
    const HP001List = HP001Info.items
    const result = HP001List?.reduce((acc, item) => {
      if (item.code === '3') {
        acc['3'] = {
          ...item,
          title: '中型',
          url: '/totalCanal',
          gisLayer: HP001Info.gisLayer,
          isActive: false,
          unit: '座',
          color: '#fff',
        }
      } else {
        if (!acc['4']) {
          acc['4'] = {
            code: '5',
            title: '小型',
            url: '/totalCanal',
            gisLayer: HP001Info.gisLayer,
            isActive: false,
            count: 0,
            unit: '座',
            color: '#fff',
          }
        }
        acc['4'].count += item.count
      }
      return acc
    }, {})
    state.overview.reservoir = Object.values(result)
    state.overview.canal = HP005Info?.items.map(item => ({
      code: item.code,
      title: item.name,
      url: '/totalCanal',
      count: item.count,
      gisLayer: HP005Info.gisLayer,
      isActive: false,
      unit: '公里',
      color: '#fff',
    }))
    setTimeout(() => {
      nextTick(() => {
        handleClick('reservoir', state.overview.reservoir[0])
        handleClick('canal', state.overview.canal[0])
        handleClick('canal', state.overview.canal[1])
      })
    }, 1000)
    // indicators.map(el => ({ ...el, value: res.data?.[el.field] }))
    // })
    // objectCategoryCountByFirstLevel({ objectCategoryId: 3, isDataMode: false }).then(res => {
    state.loading = false
    // let res1 = res.data
    const codesToFilter = [
      'HP006',
      'HP012',
      'HP013',
      'HP014',
      'HP016',
      'HP017',
      'HP018',
      'HP019',
      'HP020',
      'HP021',
      'HP034',
      'HP001',
      'HP005',
      'HP004',
    ]
    //   const codesToFilter = ['MS001']
    // let siteArr = res.data?.filter(item => codesToFilter.includes(item.objectCategoryCode)) || []

    const list = objectCategory3.filter(item => !codesToFilter.includes(item.objectCategoryCode))

    state.overview.project = list.map(item => {
      const codes = item.items?.filter(subItem => subItem.count != 0).map(subItem => parseInt(subItem.code, 10))
      return {
        codes: codes,
        code: item.objectCategoryCode,
        title: item.objectCategoryName,
        url: '/totalCanal',
        count: item.total,
        gisLayer: item.gisLayer,
        isActive: false,
        unit: '座',
        color: '#fff',
      }
    })
    // })

    // objectCategoryCountByFirstLevel({ objectCategoryId: 4 }).then(res => {
    // console.log('**** 监测站点 分类 res', res)
    const codesToFilter4 = ['MS001']
    let siteArr = objectCategory4?.filter(item => codesToFilter4.includes(item.objectCategoryCode)) || []
    // console.log('**** 监测站点 设备 data', siteArr)

    const siteCodes = ['ZQ', 'SL', 'EL', 'ZZ', 'SS', 'FL', 'SLJC']
    const titleMap = {
      ZQ: '水雨情监测',
      SL: '量测水监测',
      ZZ: '水位监测',
      SS: '墒情监测',
      EL: '安全监测',
      FL: '流量站',
      SLJC: '水量监测站',
    }
    state.overview.site = siteArr?.[0]?.items
      .filter(item => siteCodes.includes(item.code))
      .map(item => ({
        code: item.code,
        title: titleMap[item.code],
        url: '/totalCanal',
        count: item.count,
        gisLayer: siteArr?.[0]?.gisLayer,
        isActive: false,
        unit: '处',
        color: '#fff',
      }))
    // getAllCameraList().then(camera => {
    //   state.cameraArr = camera?.data
    //   state.overview.site.push({
    //     code: '6',
    //     title: '视频监测',
    //     url: '/totalCanal',
    //     count: state.cameraArr?.length,
    //     gisLayer: siteArr?.[0]?.gisLayer,
    //     isActive: false,
    //     unit: '处',
    //     color: '#fff',
    //   })
    // })
    // })
    // console.log('**** inte overview ****', state.overview)
  }
</script>
<style lang="scss" scoped>
  .content {
    // background: red;
  }

  .construction {
    width: 0.9rem;
    height: 0.5rem;
    padding: 0.05rem 0;
    border-radius: 4px;
    opacity: 1;

    background: linear-gradient(273deg, rgba(114, 204, 240, 0.1) 3%, rgba(98, 188, 219, 0) 99%);
  }
  .type-item {
  }
  .type-item:active {
  }
  .type-active {
    color: #09fcc7;
  }
  .row-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .row-item {
    display: flex;
    padding-top: 6px;
    cursor: pointer;
    // align-items: center;
    // width: calc(100% / 3 - 20px); /* 每行3个，减去gap */
    // border: 1px solid #ccc;
    // padding: 10px;
    // box-sizing: border-box;
  }
  .row-item:active {
    // background: #1b5e63;

    background: rgba(9, 200, 252, 0.1);

    box-shadow: inset 0px 0px 10px 0px #09c8fc;
  }
  .active {
    // background: #1b5e63;
    // background: rgba(9, 200, 252, 0.1);

    // box-shadow: inset 0px 0px 10px 0px #09c8fc;
  }
  .row-item-value {
    font-size: 16px;
    // font-size: 20px;
  }
  .icon {
    font-size: 24px;
    margin-right: 10px;
  }
  .content {
    font-size: 16px;
  }
  // .custom-navigation {
  //   z-index: 5;
  //   position: absolute;
  //   top: 0.02rem;
  //   right: 0.05rem;
  // }
  // .custom-navigation button {
  //   background-color: transparent;
  //   border: 1px solid transparent;
  //   padding: 5px;
  //   margin-left: 0.06rem;
  //   // margin-top: -0.4rem;
  //   pointer-events: none;
  //   cursor: pointer;
  // }
  // .custom-navigation button:hover {
  //   background-color: #f1f1f1;
  // }
  :deep(.swiper-button-prev, .swiper-button-next) {
    border: 1px solid #374147;
    color: #374147;
  }

  :deep(.swiper-button-prev) {
    width: 0.24rem;
    height: 0.24rem;
    left: 3.3rem !important;
    top: 0.25rem !important;
  }
  :deep(.swiper-button-next) {
    border: 1px solid #374147;
    width: 0.24rem;
    height: 0.24rem;
    top: 0.25rem !important;
    right: -0.05rem !important;
  }
  :deep(.swiper-button-prev:hover) {
    border: 1px solid #00d4ff;
    color: #00d4ff;
  }
  :deep(.swiper-button-next:hover) {
    border: 1px solid #00d4ff;
    color: #00d4ff;
  }
  :deep(.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after) {
    color: #00d4ff !important;
    position: absolute !important;
    // top: -0.76rem !important;
    // right: -0.1rem !important;
    font-size: 0.14rem !important;
    // z-index: 4 !important;
  }
  :deep(.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after) {
    color: #00d4ff !important;
    position: absolute !important;
    // top: -0.76rem !important;
    // right: -3.2rem !important;
    font-size: 0.14rem !important;
    // z-index: 4 !important;
  }
  .left-item {
    width: 0.6rem;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    // border: 1px solid #ccc; /* 可选：添加边框以便更好地看到效果 */
  }
</style>
