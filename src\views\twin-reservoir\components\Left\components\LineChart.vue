<template>
  <ScaleBox :style="{ width: '429px', height: '186px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="ts" name="LineChart">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  function getConfig(list) {
    return {
      tooltip: {
        show: true,
        trigger: 'axis',
        appendToBody: true,
        confine: true,
        className: 'echart-tooltip',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: 'rgba(152,224,255,0.15)',
          },
        },
        textStyle: {
          color: '#fff',
        },
      },
      grid: {
        left: 30,
        right: 15,
        top: 36,
        bottom: 5,
        containLabel: true,
      },
      // legend: {
      //   left: '20%',
      //   top: '0%',
      //   itemWidth: 5,
      //   itemHeight: 2,
      //   textStyle: {
      //     color: '#C0DDFF',
      //   },
      //   data: '水库来水预报',
      // },

      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#54608A',
          },
        },
        axisLabel: {
          color: '#8092AC',
        },
        data: list?.map(el => el.tm.substring(8)),
      },
      yAxis: {
        type: 'value',
        name: '流量:m³/s',
        // nameGap: 12,
        // minInterval: 1, //自动计算坐标轴最小间隔，例：设置成1，刻度没有小数
        nameTextStyle: {
          color: '#C0DDFF',
          padding: [0, 0, 0, 40],
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#54608A',
          },
        },
        axisLabel: {
          color: '#8092AC',
        },
        splitLine: {
          lineStyle: {
            color: '#363E5B',
          },
        },
        max: function (value) {
          return (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间
        },
      },
      dataZoom: [
        {
          show: false,
          type: 'inside',
          xAxisIndex: [0, 1],
        },
      ],
      series: [
        {
          type: 'line',
          data: list.map(el => el.inflow),
          name: '水库来水预报',
          smooth: true,
          lineStyle: {
            color: '#F6CC57',
          },
          itemStyle: {
            normal: {
              color: '#F6CC57',
              borderColor: '#F6CC57',
            },
          },
        },
      ],
    }
  }
</script>

<style lang="scss" scoped></style>
