<template>
  <n-date-picker v-bind="$attrs" class="my-custom-naive-date-picker">
    <!-- 通过遍历实现插槽透传 -->
    <template v-for="(item, key, index) in $slots" :key="index" v-slot:[key]>
      <slot :name="key"></slot>
    </template>
  </n-date-picker>
</template>
<script setup lang="tsx" name="NaDatePicker"></script>
<style lang="scss">
  .my-custom-naive-date-picker.n-date-picker {
    .v-binder-follower-content {
      transform: translateX(0px) translateY(32px) !important;
    }
    .n-input {
      border: 1px solid rgba(152, 224, 255, 0.3);
      background: rgba(9, 26, 44, 0.8);
      border-radius: 0.06rem;
    }
  }
</style>
