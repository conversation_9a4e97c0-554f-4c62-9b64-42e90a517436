<template></template>

<script setup lang="jsx">
  import { ref, reactive, onMounted, computed, watch, nextTick, useAttrs } from 'vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import { GeoJsonLayer } from '@deck.gl/layers'
  import { hexToRgb } from '@/utils/map/utils/toDeckglRgb.js'
  // import { hexToRgb } from '@/utils/utils/toDeckglRgb.js'
  // import { mapBoundGeo } from '@/utils/mapBounds.js'

  const props = defineProps(['geojson', 'mapIns', 'id'])
  const stateRef = ref({
    deckOverlay: null,
  })

  stateRef.value.deckOverlay = new MapboxOverlay({
    id: 'deck-geojson-layer-overlay',
    layers: [],
  })
  props.mapIns.addControl(stateRef.value.deckOverlay)

  const updateProps = () => {
    stateRef.value.deckOverlay.setProps({
      layers: [
        new GeoJsonLayer({
          id: 'geojson-layer-polygon-' + props.id,
          data: JSON.parse(JSON.stringify(props.geojson)),
          filled: true,
          pickable: false,
          stroked: false,
          getFillColor: d => {
            const lower = d.properties.lower

            if (lower < 0.1) {
              return [...hexToRgb('#ffffff'), 0]
            }
            if (lower <= 4.9) {
              return [...hexToRgb('#A6F28E'), 255]
            }
            if (lower <= 14.9) {
              return [...hexToRgb('#3DB93D'), 255]
            }
            if (lower <= 29.9) {
              return [...hexToRgb('#61B8FF'), 255]
            }
            if (lower <= 69.9) {
              return [...hexToRgb('#0000FE'), 255]
            }
            if (lower <= 139.9) {
              return [...hexToRgb('#F900FD'), 255]
            }
            if (lower > 140) {
              return [...hexToRgb('#A80000'), 255]
            }
          },
        }),
      ],
    })
  }

  watch(
    () => props.geojson,
    newVal => {
      nextTick(() => {
        updateProps()
      })
    },
  )
</script>

<style scoped lang="scss"></style>
