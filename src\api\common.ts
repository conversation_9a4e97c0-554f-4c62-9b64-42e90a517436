// 获取字典下拉选项
export function getOptions(dictCode) {
  return request({
    url: '/sys/dict/getOptions',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { dictCode },
  })
}

// 获取参数配置值
export function getValueByKey(configKey) {
  return request({
    url: '/sys/config/getValueByKey',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params: { configKey },
  })
}
// 获取天气预报
export function getWeatherForecast(data: { locationCode: string }) {
  return request({
    url: '/external/weather/forecast',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//当天
export function getTodayWeather(data: { locationCode: string }) {
  return request({
    url: '/external/weather/today',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 弹窗 渠首水位监测\水雨情过程线\量测水监测\流量
export function getWaterRainList(params) {
  return request({
    url: '/view/home/<USER>',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 水利对象分类-根据对象统计监控指标
export function listByIds(data) {
  return request({
    url: '/base/objectCategory/object/listByIds',
    method: 'post',
    data,
  })
}

//弹窗 视频列表
export function getCameraList(params) {
  return request({
    url: '/view/home/<USER>/getCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//获取友情链接
export function getLink() {
  return request({
    url: '/custom/link/linkList',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
}
//获取二维码
export function getQR() {
  return request({
    url: '/custom/qr/get',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
}
// 列表分页查询
export function getNewsPage(data) {
  return request({ url: '/custom/news/page', method: 'post', data })
}
// 详情
export function getNews(params) {
  return request({
    url: '/custom/news/get',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    params,
  })
}

// 获取预警信息
export function getWarnMessageList() {
  return request({
    url: '/view/home/<USER>',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  })
}
//获取预警监控记录
export function getWarnMessageRecord(data) {
  return request({
    url: '/warn/event/latest/page',
    method: 'post',
    data,
  })
}

//token是否有效
export function isTokenValid(data) {
  return request({
    url: '/sys/isTokenValid',
    method: 'post',
    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    data,
  })
}