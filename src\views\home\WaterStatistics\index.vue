<template>
  <MyCard name="灌区水雨情">
    <!-- mb-0.2rem -->
    <div class="page-content pt-0.1rem h-6.5rem">
      <div class="item flex flex-col h-1.26rem relative mt-0.3rem">
        <div
          class="absolute h-0.32rem w-1.35rem top--0.15rem pt-0.1rem px-0.14rem left-0.16rem text-0.15rem font-bold text-[#ffffff]"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">桃花江水库</span>
        </div>
        <div class="text-0.14rem mt-0.24rem w-[98%] h-1rem p-0.06rem ml-0.1rem flex">
          <div class="flex flex-1 mr-0.1rem my-0.1rem w-0.5rem">
            <div
              class="h-0.4rem w-0.4rem"
              :style="{ background: `url(${getImageUrl('right/current-water.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.6rem">
              <div class="text-[#B2DAEA] mb-0.1rem w-0.8rem">当前水位(m)</div>
              <div class="text-[#ffffff] ml-0rem text-0.2rem">
                {{ filterNum(state.tankWaterLevel?.waterLevel) }}
                <!-- <span class="text-[#699DB2]">m</span> -->
              </div>
            </div>
          </div>

          <div class="flex mr-0.1rem my-0.1rem">
            <div
              class="h-0.4rem w-0.4rem"
              :style="{ background: `url(${getImageUrl('right/current-storage.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.05rem w-0.8rem">
              <div class="text-[#B2DAEA] mb-0.1rem w-1.16rem">当前库容(百万m³)</div>
              <div class="text-[#ffffff] ml-0rem text-0.2rem w-0.9rem">
                41.35
                <!-- {{ filterNum(state.storageCapacity) }} -->
                <!-- <span class="text-[#699DB2]">m³</span> -->
              </div>
            </div>
          </div>

          <div class="flex flex-1 mr-0.1rem my-0.1rem ml-0.2rem">
            <div
              class="h-0.4rem w-0.4rem"
              :style="{ background: `url(${getImageUrl('right/current-drainage.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.6rem">
              <div class="text-[#B2DAEA] mb-0.1rem">泄水状态</div>
              <div
                class="ml-0.1rem text-0.15rem"
                :style="{
                  color: thjDrainageStatuses[state?.thjDrainageStatus]?.color,
                }"
              >
                {{ thjDrainageStatuses[state?.thjDrainageStatus]?.name }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="item flex flex-col items-center h-1.9rem relative">
        <div
          class="absolute h-0.32rem w-1.35rem top--0.15rem pt-0.1rem px-0.14rem left-0.16rem text-0.15rem font-bold text-[#ffffff]"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">渠首水深</span>
        </div>
        <div class="mt-0.24rem h-1rem ml-0rem flex flex-col">
          <div class="mt-0.06rem justify-between w-[100%] pt-0.06rem flex">
            <div class="flex flex-1 mr-0.1rem my-0.1rem cursor-pointer" @click="cancelClick(0)">
              <div
                class="h-0.4rem w-0.4rem"
                :style="{ background: `url(${getImageUrl('home/zxgq.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="flex flex-col ml-0.1rem w-0.66rem">
                <div class="text-overflow1 text-[#B2DAEA] mb-0.1rem text-0.14rem" title="沾溪干渠">沾溪干渠</div>
                <div class="text-[#ffffff] ml-0.1rem text-0.2rem w-0.88rem">
                  {{ filterNum(headWaterLevel[0]?.waterLevel) }}
                  <span class="text-[#699DB2] ml-0.05rem text-0.14rem">m</span>
                </div>
              </div>
            </div>
            <div class="flex flex-1 mr-0.1rem my-0.1rem cursor-pointer" @click="cancelClick(1)">
              <div
                class="h-0.4rem w-0.4rem"
                :style="{ background: `url(${getImageUrl('home/dgqs.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="flex flex-col ml-0.1rem w-0.66rem">
                <div class="text-overflow1 text-[#B2DAEA] mb-0.1rem text-0.14rem" title="东干渠首">东干渠首</div>
                <div class="text-[#ffffff] ml-0.1rem text-0.2rem w-0.88rem">
                  {{ filterNum(headWaterLevel[1]?.waterLevel) }}
                  <span class="text-[#699DB2] ml-0.05rem text-0.14rem">m</span>
                </div>
              </div>
            </div>
            <div class="flex flex-1 mr-0.1rem my-0.1rem cursor-pointer" @click="cancelClick(2)">
              <div
                class="h-0.4rem w-0.4rem"
                :style="{ background: `url(${getImageUrl('home/xgqs.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="flex flex-col ml-0.1rem w-0.66rem">
                <div class="text-overflow1 text-[#B2DAEA] mb-0.1rem text-0.14rem" title="西干渠首">西干渠首</div>
                <div class="text-[#ffffff] ml-0.1rem text-0.2rem w-0.88rem">
                  {{ filterNum(headWaterLevel[2]?.waterLevel) }}
                  <span class="text-[#699DB2] ml-0.05rem text-0.14rem">m</span>
                </div>
              </div>
            </div>
          </div>
          <div class="mt-0.06rem justify-between w-[100%] pt-0.06rem flex">
            <div class="flex flex-1 mr-0.1rem my-0.1rem cursor-pointer" @click="cancelClick(3)">
              <div
                class="h-0.4rem w-0.4rem"
                :style="{ background: `url(${getImageUrl('home/xgq.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="flex flex-col ml-0.1rem w-1.34rem">
                <div class="text-overflow1 text-[#B2DAEA] mb-0.1rem text-0.14rem" title="西干渠土门倒虹吸">西干渠土门倒虹吸</div>
                <div class="text-[#ffffff] ml-0.1rem text-0.2rem">
                  {{ filterNum(headWaterLevel[3]?.waterLevel) }}
                  <span class="text-[#699DB2] ml-0.05rem text-0.14rem">m</span>
                </div>
              </div>
            </div>
            <div class="flex flex-1 mr-0.1rem my-0.1rem cursor-pointer" @click="cancelClick(4)">
              <div
                class="h-0.4rem w-0.4rem"
                :style="{ background: `url(${getImageUrl('home/zxgqdgq.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="flex flex-col ml-0.1rem w-1.2rem">
                <div class="text-overflow1 text-[#B2DAEA] mb-0.1rem text-0.14rem" title="沾溪干渠代河桥">沾溪干渠代河桥</div>
                <div class="text-[#ffffff] ml-0.1rem text-0.2rem">
                  {{ filterNum(headWaterLevel[4]?.waterLevel) }}
                  <span class="text-[#699DB2] ml-0.05rem text-0.14rem">m</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="item flex flex-col items-center h-2.5rem mt-0.24rem relative">
        <div
          class="absolute h-0.32rem w-1.35rem top--0.15rem pt-0.1rem px-0.14rem left-0.16rem text-0.15rem font-bold text-[#ffffff]"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">降雨分布</span>
        </div>
        <div
          class="mt-0.46rem h-1.63rem w-3.7rem flex"
          :style="{ background: `url(${getImageUrl('right/content-chart-bg.png')}) no-repeat center / 100% 100%` }"
        >
          <!-- -->
          <div class="flex flex-col items-center mr-0.1rem ml-0.15rem mt-0.1rem">
            <div
              v-for="(item, key) in rainData"
              :key="key"
              class="h-0.32rem w-0.68rem px-0.06rem text-0.12rem flex items-center justify-center mt-0.1rem cursor-pointer"
              :style="{
                background:
                  currentNum == key
                    ? `url(${getImageUrl('right/content-btn2.png')}) no-repeat center / 100% 100%`
                    : `url(${getImageUrl('right/content-btn1.png')}) no-repeat center / 100% 100%`,
                color: currentNum == key ? '#09C8FC' : '#fff',
              }"
              @click="handleRainClick(item, key)"
            >
              {{ item.label }}
            </div>
          </div>
          <div
            class="w-2.5rem h-1.52rem ml-0.22rem mt-0.1rem"
            :style="{ background: `url(${getImageUrl('right/current-rain.png')}) no-repeat center / 100% 100%` }"
          ></div>
        </div>
      </div>
    </div>

    <div class="process-box" v-if="state.isShow && !!state.data">
      <div class="absolute size-full c-#ffffff z-999">
        <div class="header uno-bg_/modal-header.png">
          <div class="name mt-0.05rem mx-0.06rem">{{ state.info?.projectName }}</div>
          <!-- <div class="icon">{{ state.activeProcess?.chProjectName?.slice(0, 1) }}</div> -->
          <div class="title">({{ state.info.siteName }})</div>
          <div class="absolute right-0rem cursor-pointer w-0.26rem h-0.26rem pt-0.04rem" @click="closeClick">
            <MyIcon class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer" />
          </div>

          <div class="absolute right-0.3rem top-0.05rem z-999 w-0.92rem h-0.2rem flex">
            <NaSelect style="width: 0.94rem" v-model:value="day" type="side" :show-checkmark="false" :options="state.dayList">
              <template #arrow>
                <MyIcon
                  class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.1rem"
                />
              </template>
            </NaSelect>
          </div>
        </div>
        <div class="h-3rem w-5.2rem ml-0.01rem">
          <LineChart :dataSource="state.data" />
        </div>
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="Operation">
  import { getHeadWaterLevel, getTankWaterLevel } from '../services'
  import { thjDrainageStatuses } from '@/constants'
  import { getValueByKey, getWaterRainList } from '@/api'
  import LineChart from './LineChart.vue'
  const dataSource = ref([])
  const headWaterLevel = ref([])
  const rainData = ref([])
  const currentNum = ref([])
  const day = ref(1)
  rainData.value = [
    { label: '今日', value: 1 },
    { label: '昨日', value: 2 },
    { label: '未来24h', value: 3 },
  ]
  const handleRainClick = (item, key) => {
    currentNum.value = key
  }
  const state = $ref({
    thjDrainageStatus: 0,
    tankWaterLevel: {},
    isShow: false,
    info: {},
    data: [],
    dayList: [
      { label: '今天', value: 1 },
      { label: '近三天', value: 2 },
      // { label: '近七天', value: 3 },
    ],
  })
  onMounted(() => {
    init()
  })
  watch(
    () => day.value,
    newVal => {
      if (newVal) {
        getData()
      }
    },
    { deep: true },
  )
  const init = async () => {
    const headWaterLevelData = await getHeadWaterLevel()
    headWaterLevel.value = headWaterLevelData?.data
    const tankWaterLevelData = await getTankWaterLevel()
    state.tankWaterLevel = tankWaterLevelData?.data
    const storageCapacity = await getValueByKey('thjCapacity')
    state.storageCapacity = storageCapacity.data ? Number(storageCapacity.data) : state.tankWaterLevel?.storageCapacity
    const thjDrainageStatusData = await getValueByKey('thjDrainageStatus')
    state.thjDrainageStatus = thjDrainageStatusData.data
  }
  const cancelClick = index => {
    // headWaterLevel[index]
    day.value = 1

    state.info = headWaterLevel.value[index]
    if (state.info?.siteId) {
      state.isShow = true
      getData()
    } else {
      state.isShow = false
    }
  }
  const closeClick = () => {
    state.isShow = false
  }
  const getData = () => {
    let params = { siteId: state.info?.siteId, time: day.value }
    getWaterRainList(params).then(res => {
      state.data = { ...state.info, chartData: res.data }
    })
  }
</script>
<style lang="scss" scoped>
  .page-content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  :deep(.slick-slide) {
    height: 1.6rem;
    overflow: hidden;
    color: #ffffff;
  }
  .info-item {
    border-radius: 0.04rem;
    background: linear-gradient(94deg, rgba(63, 94, 144, 0.32) 12%, rgba(64, 88, 124, 0.25) 86%);
    box-shadow: inset 0px -1px 0.02rem 0px rgba(255, 255, 255, 0.2);
  }
  .header-title {
    font-family: SourceHanSansCN-Medium;
    font-size: 0.15rem;
    font-weight: 500;
    line-height: 121.86%;
    letter-spacing: 0em;

    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
    background: linear-gradient(179deg, #ffffff 49%, rgba(9, 252, 199, 0.36) 90%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }

  .process-box {
    position: fixed;
    z-index: 11;
    right: 5rem;
    bottom: 5.2rem;
    width: 5.4rem;
    height: 3rem;
    // backdrop-filter: blur(0.05rem);
    // box-shadow: inset 0px 0.04rem 0.2rem 0px #009bff;
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%);
    background: linear-gradient(180deg, rgba(12, 32, 41, 0.9) 0%, rgba(31, 83, 106, 0.9) 100%);

    overflow: hidden;
    padding: 1px;
    border-radius: 0.06rem;
    &::before {
      content: '';
      position: absolute;
      z-index: 0;
      width: calc(100% - 1px);
      height: calc(100% - 1px);
      border-radius: 0.06rem;
      background: conic-gradient(#009bff, rgba(255, 255, 255, 0.9), #009bff);
      padding: 1px;

      -webkit-mask:
        linear-gradient(#fff 0 100%) content-box,
        linear-gradient(#fff 0 100%);
      -webkit-mask-composite: xor;
    }

    .header {
      // background: linear-gradient(78deg, #0acaff 0%, rgba(10, 202, 255, 0.15) 100%);
      border-radius: 0.04rem 0.04rem 0px 0px;
      display: flex;
      // align-items: center;
      // justify-content: space-between;

      font-family: AlimamaShuHeiTi;
      font-size: 0.16rem;
      letter-spacing: 1px;
      text-shadow: 0px 0.02rem 0.04rem rgba(0, 0, 0, 0.5);
      padding: 0.02rem 0.12rem;
      .name {
        font-size: 0.18rem;
        font-weight: bold;
      }
      .icon {
        width: 20px;
        height: 20px;
        margin: 2px 8px 0 8px;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        line-height: 20px;
      }
      .title {
        font-size: 0.14rem;
        line-height: 0.3rem;
        // margin-top: 0.1rem;
      }
    }
  }
  :deep(.my-custom-naive-select-side.n-select .n-base-selection-input__content) {
    // background: green !important;
    width: 0.8rem;
  }
  :deep(.n-base-selection .n-base-suffix .n-base-suffix__arrow) {
    // background: red !important;
    margin-left: 0.1rem;
  }
</style>
