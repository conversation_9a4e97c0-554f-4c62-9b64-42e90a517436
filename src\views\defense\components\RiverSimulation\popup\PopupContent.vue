<template>
  <div class="container">
    <div class="header">
      <div class="icon">{{ item?.ditch?.projectName.slice(0, 1) }}</div>
      <div class="name">{{ item?.projectName }}</div>
      <MyIcon class="i-icon-park-outline:close-small text-0.18rem cursor-pointer c-#B2DAEA" @click="item.onPopupClose(item)" />
    </div>

    <div class="indicator">
      <div class="label">{{ indicator.label }}:</div>
      &nbsp;
      <div class="value">{{ indicator.value }}m3/s</div>
    </div>
  </div>
</template>
<script setup lang="tsx" name="PopupContent">
  import { ref, onMounted, computed, defineProps, useAttrs, watch } from 'vue'

  const item = useAttrs()

  const indicator = computed(() => {
    // 	有闸有泵(0闸1泵)2单闸3无闸无泵4单泵
    if (item.type === 0 || item.type === 2) {
      return { label: '过闸流量', value: item.outFlow }
    }
    if (item.type === 1 || item.type === 4) {
      return { label: '抽水流量', value: item.inFlow }
    }
  })
</script>
<style lang="scss" scoped>
  .container {
    width: 1.5rem;
    height: 0.76rem;
    position: relative;
    display: flex;
    flex-direction: column;

    background: rgba(37, 89, 103, 0.58);
    backdrop-filter: blur(2px);
    border-radius: 0.1rem;

    .header {
      font-weight: 600;
      line-height: 0.2rem;
      margin-top: 0.02rem;
      padding: 0.06rem 0.08rem;
      display: flex;
      align-items: center;
      .icon {
        width: 0.2rem;
        height: 0.2rem;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        font-size: 0.12rem;
        font-weight: 600;
      }
      .name {
        flex: 1;
        margin: 0 0.05rem 0 0.04rem;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 0.14rem;
        font-weight: 600;
      }
    }
    .indicator {
      display: flex;
      font-size: 0.14rem;
      padding: 0.02rem 0.08rem;
      .label {
        color: #b2daea;
      }
      .value {
        color: #09c8fc;
      }
    }
  }
</style>
