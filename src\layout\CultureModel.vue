<template>
  <ThjModal ref="$modal" :showFooter="false">
    <div class="w-96% h-96% ml-2% mr-2% mt-1% mb-3% flex relative">
      <div class="legend ml-0.3rem">
        <n-space>
          <!-- :type="activeTab === 'intro' ? 'primary' : 'default'" -->
          <n-button
            :class="activeTab === 'intro' ? 'btnactive' : 'btnstyle'"
            @click="handleTabClick('intro')"
            @mouseenter="handleTabHover('intro')"
          >
            灌区简介
          </n-button>
          <!-- :type="activeTab === 'style' ? 'primary' : 'default'" -->
          <n-button
            :class="activeTab === 'style' ? 'btnactive' : 'btnstyle'"
            @click="handleTabClick('style')"
            @mouseenter="handleTabHover('style')"
          >
            灌区风采
          </n-button>
        </n-space>
      </div>

      <div class="absolute w-[34%] h-[90%] ml-0.3rem top-0.9rem flex" v-if="activeTab === 'intro'">
        <MyCard class="w-[100%]" name="灌区简介">
          <!-- <ScaleBox :style="{ width: '276px', height: '20px', right: '-30px', top: '6px', zIndex: 999 }"></ScaleBox> -->
          <div class="title">
            <!-- 桃花江灌区 -->
            {{ state.cultureInfo?.[0].title }}
          </div>
          <div class="content">
            {{ state.cultureInfo?.[0].remark }}
          </div>
          <div class="title">
            <!-- 桃花净竹文化 -->
            {{ state.cultureInfo?.[1].title }}
          </div>
          <div class="content">
            {{ state.cultureInfo?.[1].remark }}
          </div>
          <div class="title">
            <!-- 桃花江水库 -->
            {{ state.cultureInfo?.[2].title }}
          </div>
          <div class="content">
            {{ state.cultureInfo?.[2].remark }}
          </div>
        </MyCard>
      </div>
      <div class="absolute w-[34%] h-[90%] ml-0.3rem top-0.9rem flex" v-if="activeTab === 'style'">
        <MyCard class="w-[100%]" name="灌区风采">
          <div class="title">
            <!-- 灌区管理简介 -->
            {{ state.cultureInfo?.[3].title }}
          </div>
          <div class="content">
            {{ state.cultureInfo?.[3].remark }}
          </div>
          <div class="title">信息展示区域</div>
          <div class="list">
            <div
              class="list-item cursor-pointer text-0.14rem text-[#B2DAEA]"
              v-for="(item, index) in state.list"
              :key="index"
              @click="handleNews(item)"
              :class="{ active: item.newsId === state.activeNewsId }"
            >
              {{ item.newsName }}
            </div>
          </div>
          <div class="link flex w-[100%] h-1.26rem uno-bg_model/link-bg.png mt-0.3rem">
            <div class="link-item pl-0.18rem">
              <div class="link-title mt-0.08rem">友情链接</div>
              <div class="link-content mt-0.06rem">
                <div class="link-content-item" v-for="(item, index) in state.linkList" :key="index" @click="handleLink(item)">
                  <a :href="item.linkUrl" target="_blank">{{ item.linkName }}</a>
                </div>
              </div>
            </div>
            <div class="link-qr">
              <div class="link-qr-title">
                <div class="vertical-text" v-for="(i, index) in state.qrData?.qrName" :key="index">{{ i }}</div>
              </div>
              <!-- <div class="link-qr-title">{{ state.qrData?.qrName }}</div> -->
              <div class="link-qr-content flex w-1.1rem h-1.1rem uno-bg_model/qr-bg.png">
                <img :src="state.qrData?.qrUrl" class="w-0.96rem h-0.96rem ml-0.07rem mt-0.07rem" alt="" />
              </div>
            </div>
          </div>
          <!-- <ScaleBox :style="{ width: '276px', height: '20px', right: '-30px', bottom: '6px', zIndex: 999 }"></ScaleBox> -->
        </MyCard>
      </div>
      <!-- v-if="activeTab === 'history'" 右侧视频  uno-bg_model/video-2.png-->
      <div
        class="video-player absolute w-[58%] h-[90%] right-0.3rem top-0.7rem flex"
        v-if="!state.activeNewsId || (state.activeNewsId && state.activeNews?.publishType == 2)"
      >
        <video
          ref="videoPlayer"
          :src="!state.videoUrl ? state.videoUrl : getImageUrl('model/culture.mp4')"
          controls
          class="video-element w-[98%] h-[96%] block border-rd-0.1rem"
        ></video>
      </div>
      <div
        class="absolute w-[58%] h-[90%] right-0.3rem top-0.7rem flex border-rd-0.1rem"
        v-if="state.activeNewsId && state.activeNews?.publishType == 1"
      >
        <div class="content-preview">
          <div class="closed" @click="state.activeNewsId = undefined">关闭信息详情</div>
          <!-- 预览内容标题两行显示 -->
          <div class="title text-overflow2" :title="state.activeNews?.newsName">{{ state.activeNews?.newsName }}</div>
          <!-- <div class="title text-overflow2">
            桃花江新闻测试标题桃花江新闻测试标题桃花江新闻测试标题桃花江新闻测试标题桃花江新闻测试标题桃花江新闻测试标题题桃花江新闻测试标题题桃花江新闻测试标题题桃花江新江新闻测试标题闻测试标题
          </div> -->
          <div class="time">发表时间：{{ state.activeNews?.pubdate }}</div>
          <!-- v-html="state.activeNews?.context" -->
          <div class="content">
            <!-- <div v-html="state.activeNews?.context"></div> -->
            <n-text>
              <div class="rich-text-container" v-html="state.activeNews?.context"></div>
            </n-text>
            <!-- <a-typography>
              <div v-html="state.richText"></div>
            </a-typography> -->
          </div>
        </div>
      </div>
    </div>
  </ThjModal>
</template>
<script setup lang="ts" name="CultureModel">
  // import { getDispatchList, futureRainList, futureRainConvert } from '../services'
  const [$modal, okLoading, loading, title] = useModal()
  import { getOptions, getValueByKey, getLink, getQR, getNewsPage, getNews } from '@/api'
  import { ref, onMounted, onUnmounted } from 'vue'

  import dayjs from 'dayjs'

  const attrs = useAttrs()
  const state = reactive({
    richText: '',
    qrName: '水利文化馆',
    cultureInfo: null,
    videoUrl: null,
    activeNews: null,
    activeNewsId: undefined,
    list: [],
    qrData: null,
    linkList: null,
    total: 0,
    pageNum: 1,
    resetTime: 0,
    times: [],
    mapIns: null,
    layers: {
      MultiPolygon: [],
      MultiLineString: [],
      Point: [],
    },
    rainLevelOptions: [],
    lineChartData: [],
    rainList: [],
    newRange: [],
    rainSum: 0,
  })
  const openModal = async () => {
    getList()

    $modal.value?.open({
      loading: false,
      contentStyle: { width: '18rem', height: '9rem' },
      headerStyle: { display: 'none' },
      title: '水利文化馆',
      onClose: () => {
        // 此处重置状态
        state.activeNewsId = undefined
        attrs.onClose && attrs.onClose()
      },
    })
  }
  defineExpose({ openModal })

  const activeTab = ref('intro') // 当前选中的标签
  let timer = null // 定时器
  let isPaused = false // 是否暂停切换

  // 切换标签
  const switchTab = () => {
    activeTab.value = activeTab.value === 'intro' ? 'style' : 'intro'
  }

  // 启动自动切换 5分钟切换一次
  const startAutoSwitch = () => {
    timer = setInterval(() => {
      if (!isPaused) {
        switchTab()
      }
    }, 300000) // 30秒切换一次 30000
  }

  // 处理按钮点击
  const handleTabClick = tab => {
    activeTab.value = tab
    pauseAutoSwitch()
  }

  // 处理按钮移入
  const handleTabHover = tab => {
    activeTab.value = tab
    pauseAutoSwitch()
  }

  // 暂停自动切换 暂停15分钟再切换
  const pauseAutoSwitch = () => {
    if (!isPaused) {
      isPaused = true
      clearInterval(timer) // 清除定时器
      setTimeout(() => {
        isPaused = false
        startAutoSwitch() // 1分钟后恢复自动切换
      }, 900000) // 暂停1分钟 60000
    }
  }

  // 组件挂载时启动自动切换
  onMounted(() => {
    startAutoSwitch()
  })

  // 组件卸载时清除定时器
  onUnmounted(() => {
    clearInterval(timer)
  })

  const getList = async () => {
    const linkList = await getLink()
    state.linkList = linkList?.data || []
    const qrList = await getQR()
    state.qrData = qrList?.data

    const newsList = await getNewsPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER })

    state.list = newsList?.data?.data || []
    const url = await getValueByKey('thj.culture.url')
    state.videoUrl = url?.data
    const cultureInfo = await getValueByKey('thj.culture.info')
    /** 参数中json数据不能有空格 */
    state.cultureInfo = JSON.parse(cultureInfo?.data)

    // state.activeNewsId = state.list[0]?.newsId
  }
  const handleLink = item => {
    window.open(item.linkUrl, '_blank')
  }
  const handleNews = item => {
    state.activeNewsId = item.newsId
    getNews({ newsId: item.newsId }).then(res => {
      console.log('212 news res', res)
      state.activeNews = res?.data
      state.richText = `<p style="font-size: 24px;">${state.activeNews?.context}</p>`

      if (state.activeNews?.publishType == 2) {
        window.open(state.activeNews?.externalLink, '_blank')
      }
    })
  }
</script>
<style lang="scss" scoped>
  .btnstyle {
    width: 0.93rem;
    height: 0.32rem;
    // transform: rotate(180deg);
    border-radius: 0.06rem;
    opacity: 1;

    background: linear-gradient(180deg, #0c2029 0%, #1f536a 100%);

    box-sizing: border-box;
    border: 1px solid rgba(255, 255, 255, 0.48);
    // border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%) 1;

    font-size: 0.14rem;
    font-weight: 350;
    line-height: normal;
    text-align: center;
    color: #ffffff;
  }
  .btnactive {
    width: 0.93rem;
    height: 0.32rem;
    border-radius: 0.06rem;
    opacity: 1;

    background:
      radial-gradient(74% 74% at 44% 100%, #00c8ff 0%, rgba(9, 200, 252, 0) 99%),
      linear-gradient(180deg, #0c2029 0%, #1f536a 100%), rgba(146, 183, 202, 0.302);

    box-sizing: border-box;
    border: 0.02rem solid #09c8fc;
    // border-image: linear-gradient(180deg, #09c8fc 0%, #09c8fc 100%) 2;

    font-size: 0.15rem;
    font-weight: 500;
    line-height: normal;
    text-align: center;
    color: #09c8fc;

    // box-shadow: 0px 0.04rem 0.1rem 0px #000000;
  }
  .title {
    //   position: absolute;
    // left: 236px;
    // top: 361px;
    width: 4.3rem;
    height: 0.2rem;
    opacity: 1;

    font-family: Source Han Sans;
    font-size: 0.16rem;
    font-weight: 500;
    line-height: 121.86%;
    letter-spacing: 0em;
    margin: 0.2rem 0;
    // font-variation-settings: 'opsz' auto;
    // font-feature-settings: 'kern' on;
    /* 新的/主色1 */
    color: #09fcc7;
  }

  .content {
    // width: 475px;
    // height: 120px;
    opacity: 1;
    margin-bottom: 0.2rem;
    font-family: Source Han Sans;
    font-size: 0.14rem;
    font-weight: normal;
    line-height: 121.86%;
    letter-spacing: 0em;

    // font-variation-settings: 'opsz' auto;
    // font-feature-settings: 'kern' on;
    /* 文本/一级 */
    color: #ffffff;
  }
  .list {
    display: flex;
    flex-direction: column;
    height: 2.2rem;
    overflow-y: scroll;
    .list-item {
      // width: 4.75rem;
      width: 100%;
      height: 0.28rem;
      border-radius: 0.04rem;
      margin-bottom: 0.08rem;
      opacity: 1;

      background: rgba(85, 130, 148, 0.2);

      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(90deg, rgba(85, 130, 148, 0) 0%, rgba(85, 130, 148, 0.5512) 44%, rgba(85, 130, 148, 0) 99%) 1;
      padding: 0 0.1rem;

      &.active {
        border-radius: 0.04rem;
        background: #158ca0;
        box-sizing: border-box;
        border: 0.02rem solid #09fcc7;
        position: relative;
        color: #fff;

        .check {
          color: #fff;
          border-radius: 0.02rem;
          background: #09fcc7;
        }
      }
    }
  }
  .link {
    display: flex;

    .link-item {
      width: 4.2rem;

      // background: red;
      .link-title {
        width: 4.3rem;
        height: 0.2rem;
        opacity: 1;

        font-family: Source Han Sans;
        font-size: 0.16rem;
        font-weight: 500;
        line-height: 121.86%;
        letter-spacing: 0em;

        font-variation-settings: 'opsz' auto;
        font-feature-settings: 'kern' on;
        /* 新的/主色1 */
        color: #09fcc7;
      }
      .link-content {
        width: 100%;
        height: 0.95rem;
        // background: red;
        padding: 0.05rem 0; /* 内边距 */
        overflow-y: auto; /* 纵向滚动条 */
        overflow-x: hidden; /* 隐藏横向滚动条 */
        display: flex;
        flex-wrap: wrap; /* 换行显示 */
        gap: 0.05rem 0.2rem; /* 链接之间的间距 */
        .link-content-item {
          background-color: transparent;
          white-space: nowrap; /* 防止链接文字换行 */
          height: 0.15rem;
          opacity: 1;
          font-family: Source Han Sans;
          font-size: 0.12rem;
          font-weight: normal;
          letter-spacing: 0em;
          color: #b2daea;
        }

        /* 链接文字的样式 */
        .link-content-item a {
          text-decoration: none;
          color: #b2daea;
        }

        .link-content-item a:hover {
          color: #007bff;
        }
      }
    }
    .link-qr {
      // width: 1.6rem;
      flex: 1;
      // background: blueviolet;
      display: flex;
      margin: 0.08rem 0 0 0.1rem;
      .link-qr-title {
        width: 0.2rem;
        height: 1.1rem;
        color: #699db2;

        display: flex;
        flex-direction: column; /* 纵向排列 */
        justify-content: center; /* 垂直居中 */
        align-items: center; /* 水平居中 */
        .vertical-text {
          writing-mode: vertical-rl; /* 文字纵向排列 */
          text-orientation: upright; /* 文字方向为正立 */
          font-size: 0.12rem; /* 字体大小 */
        }
      }
      .link-qr-content {
        margin-left: 0.08rem;
      }
    }
  }
  .content-preview {
    width: 9.4rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    // margin-left: 40px;
    // background: green;
    position: relative;
    .closed {
      position: absolute;
      top: -0.36rem;
      // top: 0.2rem;
      right: 0;
      cursor: pointer;
      width: 1rem;
      height: 0.32rem;
      border-radius: 0.02rem;
      opacity: 1;

      /* 自动布局 */
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 0.04rem 0.08rem;
      // gap: 4px;
      background: #df2f2f;

      font-size: 0.14rem;
      font-weight: 500;
      line-height: normal;
      text-align: center;
      letter-spacing: 0em;

      color: #ffffff;
    }
    .title {
      width: 88%;
      width: 100%;
      height: auto;
      color: #fff;
      font-family: PingFangBold;
      font-size: 0.24rem;
      text-align: left;
      font-weight: 500;
      line-height: auto;
      padding-bottom: 0.08rem;
      // margin-top: 0.3rem;
    }
    .time {
      width: 100%;
      // text-align: center;
      text-align: left;
      font-size: 0.14rem;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 400;
      color: #699db2 !important;
    }
    .content {
      margin-top: 0.14rem;
      padding-top: 0.1rem;
      width: 100%;
      height: 580px;
      height: 5.6rem;
      overflow-y: auto;
      color: #fff !important;
    }
  }

  .ant-typography {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
  }
  /* 设置富文本容器的整体样式 */
  .rich-text-container {
    line-height: 1.5; /* 设置行间距为 2 倍行高 */
    font-size: 14pt; /* 确保字体大小一致 */
    font-family: '宋体', serif; /* 确保字体一致 */
  }

  /* 强制覆盖富文本中的段落样式 */
  .rich-text-container p {
    line-height: 1.5 !important; /* 强制设置段落的行间距 */
    margin-bottom: 1em; /* 设置段落之间的间距 */
  }
</style>
