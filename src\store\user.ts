import { defineStore, acceptHMRUpdate } from 'pinia'

export const useUserStore = defineStore(
  'user',
  () => {
    const user = ref(null)
    const token = ref(null)
    const routes = ref(null)
    const serveErr = ref(null)

    function clearToken() {
      token.value = null
    }

    return { user, token, routes, serveErr, clearToken }
  },
  { persist: true },
)

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useUserStore, import.meta.hot))
}
