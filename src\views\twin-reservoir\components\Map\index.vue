<template>
  <div class="w-100vw h-100vh" id="cesiumContainer"></div>
  <n-button
    class="w-0.92rem h-0.34rem text-0.15rem fixed top-1.6rem right-0.3rem c-#fff border-rd-0.08rem z-88 btn-bg"
    @click="toHouse"
  >
    跳转视角
  </n-button>
  <ReservoirToolbar
    @change-dot="onChangeDot"
    @change-photography="onChangePhotography"
    @change-water-level="onChangeWaterLevel"
    @change-roof="onChangeRoof"
    @change-simulation="onChangeSimulation"
  />
  <ReservoirRight
    v-if="(state.simulationType == 'water-level-simulation' && props.waterLevel) || state.simulationType == 'gate-simulation'"
    :simulationType="state.simulationType"
    :waterLevel="props.waterLevel"
    @change-river="onChangeRiver"
    @change-gate="onChangeGate"
    @simulation-river="simulationRiver"
  />
  <div class="fixed z-9999" ref="popupRef">
    <Popup :popupCode="state.popupCode" :popupName="state.popupName" :popupType="state.popupType" @close-popup="closePopup" />
  </div>
</template>
<script setup lang="ts" name="CesiumMap">
  import * as Cesium from 'cesium'
  import { cesiumMapConfig } from '@/utils/cesiumMapConfig'
  import {
    addPhotography,
    addBuilding,
    addWaterLevelLabel,
    addDots,
    remove3DTile,
    addRoof,
    addRiverSystem,
    removeRiverSystem,
  } from './addTiles'

  import ReservoirToolbar from '../ReservoirToolbar.vue'
  import ReservoirRight from '../Right/index.vue'
  import Popup from './components/Popup.vue'
  import { dotList } from '../dot'
  import { closeGateMatrix } from './tiles'

  const popupRef = ref(null)
  const props = defineProps({
    waterLevel: Number,
    isBack: Boolean,
  })

  onMounted(() => {
    mapInit()
  })

  watch(
    () => props.isBack,
    newVal => {
      clearAll()
    },
  )

  const state = reactive({
    waterTimer: null,
    simulationType: undefined,
    riverHeight: props?.waterLevel,
    popupCode: '',
    popupName: '',
    popupType: '',

    gate1Angle: 0,
    gate2Angle: 0,
    gate1Timer: null,
    gate2Timer: null,
  })
  let viewer

  const mapInit = () => {
    viewer = cesiumMapConfig('cesiumContainer')
    viewer._cesiumWidget._creditContainer.style.display = 'none'
    viewer.scene.globe.depthTestAgainstTerrain = true
    viewer.clock.shouldAnimate = true

    viewer.imageryLayers.addImageryProvider(
      new Cesium.WebMapTileServiceImageryProvider({
        url: `${import.meta.env.VITE_TIANDI_BASE}/img_w/wmts?service=wmts&request=GetTile&version=1.0.0&LAYER=img&tileMatrixSet=w&TileMatrix={TileMatrix}&TileRow={TileRow}&TileCol={TileCol}&style=default&format=tiles&tk=${import.meta.env.VITE_TIANDI_TK}`,
        layer: 'tdtBasicLayer',
        style: 'default',
        format: 'image/jpeg',
        tileMatrixSetID: 'GoogleMapsCompatible',
        maximumLevel: 18,
      }),
    )

    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(112.06598156471505, 28.274802024914585, 2300.59),
      orientation: {
        heading: Cesium.Math.toRadians(19),
        pitch: Cesium.Math.toRadians(-21),
        roll: 0.0,
      },
    })

    addPhotography(viewer)
    addBuilding(viewer)

    watch(
      () => props.waterLevel,
      newVal => {
        setTimeout(() => {
          if (props.waterLevel) {
            state.riverHeight = Number(props.waterLevel)
            addRiverSystem(viewer, props.waterLevel)
            addWaterLevelLabel(viewer, props.waterLevel)
            initEvent()
          }
        }, 2000)
      },
    )
    addTerrain()
    viewer.scene.renderError.addEventListener(reload)
  }

  const addTerrain = async () => {
    let terrainLayer = await Cesium.CesiumTerrainProvider.fromUrl(import.meta.env.VITE_MODEL_URL + '/thj/', {
      requestVertexNormals: true,
    })
    viewer.terrainProvider = terrainLayer
  }

  const reload = () => {
    window.location.reload()
  }

  const closePopup = () => {
    popupRef.value.style.display = 'none'
  }

  const toHouse = () => {
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(112.1019494183137, 28.326405614355306, 266.59),
      orientation: {
        heading: Cesium.Math.toRadians(127.62),
        pitch: Cesium.Math.toRadians(-36.48),
        roll: 0.0,
      },
    })
  }

  const onChangeDot = (arr: any) => {
    viewer?.entities?.removeAll()
    if (arr?.length) {
      arr.forEach(el => addDots(viewer, el))
    }
  }
  const onChangePhotography = (checked: boolean) => {
    if (checked) {
      addPhotography(viewer)
    } else {
      remove3DTile(viewer, 'photography')
    }
  }

  const onChangeWaterLevel = (checked: boolean) => {
    if (checked) {
      addWaterLevelLabel(viewer, props.waterLevel)
    } else {
      viewer.entities.removeById('reservoir-water-level')
    }
  }
  const onChangeRoof = (checked: boolean) => {
    const roots = ['THJSDZ001']
    if (checked) {
      roots.forEach(el => addRoof(viewer, el))
    } else {
      roots.forEach(el => remove3DTile(viewer, el))
      //屋顶
      let generatorCartesian = Cesium.SceneTransforms.worldToWindowCoordinates(
        viewer.scene,
        Cesium.Cartesian3.fromDegrees(112.10501609552207, 28.3248974687185, 50),
      )
      state.popupCode = 'generator-set'
      state.popupName = '发电机组'
      popupRef.value.style.display = 'block'
      popupRef.value.style.left = generatorCartesian?.x - 120 / 2 + 'px'
      popupRef.value.style.top = generatorCartesian?.y - 150 + 'px'
    }
  }

  const onChangeSimulation = (val: string) => {
    state.simulationType = val
    if (val === 'gate-simulation') {
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(112.10439617373706, 28.323862904251122, 226.59),
        orientation: {
          heading: Cesium.Math.toRadians(310.62),
          pitch: Cesium.Math.toRadians(-41.48),
          roll: 0.0,
        },
      })
    }
  }
  const simulationRiver = (num: number, type) => {
    let currentHeight = num
    if (type == 'init') {
      currentHeight = props?.waterLevel
    }
    viewer.entities.removeById('reservoir-water-level')
    addWaterLevelLabel(viewer, currentHeight)

    removeRiverSystem(viewer, 'river-system')
    addRiverSystem(viewer, currentHeight)
  }

  const onChangeRiver = (num: number, type: string) => {
    clearInterval(state.waterTimer)
    if (type === 'init') {
      removeRiverSystem(viewer, 'river-system')
      addRiverSystem(viewer, num)
      viewer.entities.removeById('reservoir-water-level')
      addWaterLevelLabel(viewer, num.toFixed(1))
      return
    }
    const isAdd = num > state.riverHeight ? true : false
    state.waterTimer = setInterval(() => {
      if (isAdd) {
        if (num - 0.2 >= state.riverHeight) {
          state.riverHeight += 0.2
          addRiverSystem(viewer, state.riverHeight)

          viewer.entities.removeById('reservoir-water-level')
          addWaterLevelLabel(viewer, state.riverHeight.toFixed(1))
        } else {
          clearInterval(state.waterTimer)
        }
      } else {
        if (num + 0.2 <= state.riverHeight) {
          state.riverHeight -= 0.2
          removeRiverSystem(viewer, 'river-system')
          addRiverSystem(viewer, state.riverHeight)

          viewer.entities.removeById('reservoir-water-level')
          addWaterLevelLabel(viewer, state.riverHeight.toFixed(1))
        } else {
          clearInterval(state.waterTimer)
        }
      }
    }, 500)
  }

  //
  const onChangeGate = (gateVal1: number, gateVal2: number, type: string, checkedGate1: boolean, checkedGate2: boolean) => {
    clearInterval(state.gate1Timer)
    clearInterval(state.gate2Timer)
    let allTile = viewer.scene.primitives._primitives
    let SZZM001 = allTile.find(t => t?.id == 'SZZM001')
    let SZZM002 = allTile.find(t => t?.id == 'SZZM002')

    if (type === 'init') {
      state.gate1Angle = 0
      state.gate2Angle = 0
      if (checkedGate1) SZZM001.modelMatrix = closeGateMatrix
      if (checkedGate2) SZZM002.modelMatrix = closeGateMatrix
      return
    }

    if (gateVal1 == 0) {
      state.gate1Angle = 0
      if (checkedGate1) SZZM001.modelMatrix = closeGateMatrix
    } else {
      if (checkedGate1) {
        const gate1Num = gateVal1 > 0 ? gateVal1 * -0.03333333333333333 : gateVal1 * -0.03333333333333333
        if (gate1Num.toFixed(0) == state.gate1Angle.toFixed(0)) return
        const isAdd = gate1Num < state.gate1Angle ? true : false

        state.gate1Timer = setInterval(() => {
          if (isAdd) {
            if (gate1Num < state.gate1Angle) {
              state.gate1Angle -= Math.abs(gate1Num) / 20

              setModelMatrix(SZZM001, state.gate1Angle)
            } else {
              clearInterval(state.gate1Timer)
            }
          } else {
            if (gate1Num > state.gate1Angle) {
              state.gate1Angle += Math.abs(gate1Num) / 20
              setModelMatrix(SZZM001, state.gate1Angle)
            } else {
              clearInterval(state.gate1Timer)
            }
          }
        }, 500)
      }
    }

    if (gateVal2 == 0) {
      state.gate2Angle = 0
      if (checkedGate2) SZZM002.modelMatrix = closeGateMatrix
    } else {
      if (checkedGate2) {
        const gate2Num = gateVal2 > 0 ? gateVal2 * -0.03333333333333333 : gateVal2 * -0.03333333333333333
        if (Math.floor(gate2Num) == Math.floor(state.gate2Angle)) return
        const isAdd = gate2Num < state.gate2Angle ? true : false
        state.gate2Timer = setInterval(() => {
          if (isAdd) {
            if (gate2Num < state.gate2Angle) {
              state.gate2Angle -= Math.abs(gate2Num) / 20
              setModelMatrix(SZZM002, state.gate2Angle)
            } else {
              clearInterval(state.gate2Timer)
            }
          } else {
            if (gate2Num > state.gate2Angle) {
              state.gate2Angle += Math.abs(gate2Num) / 20
              setModelMatrix(SZZM002, state.gate2Angle)
            } else {
              clearInterval(state.gate2Timer)
            }
          }
        }, 500)
      }
    }
  }

  const setModelMatrix = (smItem: any, num: number) => {
    const origin = smItem.boundingSphere.center
    const toWorldMatrix = Cesium.Transforms.eastNorthUpToFixedFrame(origin)
    const toLocalMatrix = Cesium.Matrix4.inverse(toWorldMatrix, new Cesium.Matrix4())
    const rotateMatrix = Cesium.Matrix4.clone(Cesium.Matrix4.IDENTITY)
    const rotateYMatrix = Cesium.Matrix4.fromRotation(Cesium.Matrix3.fromRotationY(Cesium.Math.toRadians(num - 0.022)))
    const rotateZMatrix = Cesium.Matrix4.fromRotation(Cesium.Matrix3.fromRotationX(Cesium.Math.toRadians(num)))
    Cesium.Matrix4.multiply(rotateZMatrix, rotateMatrix, rotateMatrix)
    Cesium.Matrix4.multiply(rotateZMatrix, rotateYMatrix, rotateMatrix)
    const localResultMatrix = Cesium.Matrix4.multiply(rotateMatrix, toLocalMatrix, new Cesium.Matrix4())
    const worldResultMatrix = Cesium.Matrix4.multiply(toWorldMatrix, localResultMatrix, new Cesium.Matrix4())
    smItem.modelMatrix = Cesium.Matrix4.multiply(worldResultMatrix, smItem.modelMatrix, new Cesium.Matrix4())
  }

  //初始化事件
  function initEvent() {
    const eventHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas)

    eventHandler.setInputAction(e => {
      let pickEntity = viewer.scene.pick(e?.position)
      let pickedCartesian = viewer?.scene?.globe?.pick(viewer.camera?.getPickRay(e.position), viewer.scene)
      let windowPosition = Cesium.SceneTransforms?.worldToWindowCoordinates(viewer?.scene, pickedCartesian)
      if (Cesium.defined(pickEntity) && pickEntity?.id?.id) {
        if (pickEntity?.id?.id != 'house-gate') {
          state.popupCode = pickEntity?.id?.id
          state.popupName = pickEntity?.id?.name
          state.popupType = pickEntity?.id?.type
          popupRef.value.style.display = 'block'
          popupRef.value.style.left = windowPosition?.x - 220 / 2 + 'px'
          popupRef.value.style.top = windowPosition?.y + 50 + 'px'
        }
      }

      let cartesian = viewer.camera.pickEllipsoid(e?.position, viewer.scene.globe.ellipsoid)
      if (cartesian) {
        let cartographic = Cesium.Cartographic.fromCartesian(cartesian)
        let longitudeString = Cesium.Math.toDegrees(cartographic.longitude)
        let latitudeString = Cesium.Math.toDegrees(cartographic.latitude)
        // 输出经纬度
        console.log('经纬度: ' + longitudeString + ',' + latitudeString)
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK)
  }

  const clearAll = () => {
    clearInterval(state.gate1Timer)
    clearInterval(state.gate2Timer)
    clearInterval(state.waterTimer)
    viewer.entities.removeAll()
    viewer.scene.primitives.removeAll()
  }
  onUnmounted(() => {
    clearAll()
  })
</script>
<style lang="scss" scoped>
  .btn-bg {
    background: radial-gradient(75% 65% at 47% 100%, #09fc4a 0%, rgba(9, 252, 220, 0) 94%),
      linear-gradient(180deg, #098ffc 0%, rgba(85, 130, 148, 0) 100%);
  }
</style>
