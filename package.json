{"name": "vite-vue-ts", "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build:test": "vite build --mode test", "build:prod": "vite build --mode prod"}, "dependencies": {"@deck.gl/layers": "^9.0.29", "@deck.gl/mapbox": "^9.0.29", "@easydarwin/easyplayer": "^5.1.1", "@esri/calcite-ui-icons": "3.29.2", "@fingerprintjs/fingerprintjs": "^4.5.0", "@mapbox/mapbox-gl-draw": "^1.4.3", "@supermapgis/iclient-mapboxgl": "^11.2.0", "@turf/turf": "^7.1.0", "@vueuse/core": "^10.11.1", "ant-design-vue": "~4.2.5", "axios": "^1.7.7", "cesium": "^1.124.0", "dayjs": "^1.11.13", "deck.gl": "^9.0.29", "echarts": "^5.5.1", "gcoord": "^1.0.6", "lodash-es": "^4.17.21", "mapbox-gl": "^3.6.0", "naive-ui": "^2.39.0", "pinia": "^2.2.2", "pinia-plugin-persistedstate": "^3.2.3", "qweather-icons": "^1.6.0", "swiper": "^11.1.14", "video.js": "^8.17.4", "vite-plugin-cesium": "^1.2.23", "vue": "^3.5.8", "vue-echarts": "^6.5.5", "vue-router": "^4.4.5", "vue3-seamless-scroll": "^2.0.1", "xlsx": "https://cdn.sheetjs.com/xlsx-0.20.3/xlsx-0.20.3.tgz", "xlsx-js-style": "^1.2.0"}, "devDependencies": {"@iconify/json": "^2.2.252", "@iconify/utils": "^2.1.33", "@iconify/vue": "^4.1.2", "@types/node": "^20.16.5", "@unocss/preset-rem-to-px": "^0.59.4", "@unocss/transformer-attributify-jsx": "^0.59.4", "@unocss/transformer-directives": "0.59.2", "@vitejs/plugin-vue": "^5.1.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue-macros/reactivity-transform": "^0.4.8", "glob": "^10.4.5", "prettier": "^3.3.3", "sass": "^1.79.3", "terser": "^5.33.0", "tsx": "^4.19.1", "typescript": "^5.6.2", "unocss": "^0.60.4", "unplugin-auto-import": "^0.17.8", "unplugin-icons": "^0.18.5", "unplugin-vue-components": "^0.26.0", "vite": "^5.4.7", "vite-plugin-compression": "^0.5.1", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}