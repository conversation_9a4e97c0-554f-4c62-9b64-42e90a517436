<template>
  <div class="h-full w-full reactive">
    <div class="w-4.3rem absolute top-0.8rem left-0.2rem bottom-0.2rem flex-col z-2">
      <div
        class="flex-container absolute top-0.24rem right--0.32rem w-0.35rem h-1.01rem flex flex-col items-center justify-center cursor-pointer"
        :style="{ background: `url(${getImageUrl('home/reservoir-3d.png')}) no-repeat center / 100% 100%` }"
        @click="toTwinReservoir"
      >
        <div class="title">孪生水库</div>
        <!-- <p>数字孪生</p> -->
      </div>
      <DeviceInfo class="mb-0.2rem flex-1" v-model:activeItem="activeItem" v-model:allData="allData" />
    </div>
    <div class="w-4.3rem absolute top-0.8rem right-0.2rem bottom-0.2rem flex-col z-6">
      <WaterStatistics class="mb-0.2rem" />
      <WaterResource class="mb-0.2rem flex-1" />
    </div>

    <Map
      :isShow="isShow"
      :allData="allData"
      v-model:activeItem="activeItem"
      v-model:activeTabLevel2="activeTabLevel2"
      :currentDistrict="currentDistrict"
      :isDataMode="isDataMode"
    />
  </div>
</template>

<script setup lang="tsx" name="Home">
  import { unmountLoading } from '@/core/loading'

  import Map from './Map'
  import DeviceInfo from './DeviceInfo'
  import AreaAndToolControl from './AreaAndToolControl'
  import WaterStatistics from './WaterStatistics'
  import WaterResource from './WaterResource'

  import { objectCategoryFirstLevelList } from './services'

  const router = useRouter()

  const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
  })

  let activeTabLevel2 = $ref(null)
  //objectCategoryCode
  let activeTabLevel1 = $ref(null)

  let tabLevelOptions1 = $ref([])

  // 所有数据--右侧选中变化
  let allData = $ref(null)

  let activeItem = $ref(null)
  onMounted(() => {
    // unmountLoading()
  })
  let currentDistrict = $ref(null)

  //孪生水库
  const toTwinReservoir = () => {
    router.push('/twin-reservoir')
  }

  // 是否打开数据模式
  // let isDataMode = $ref(false)
  // let isExpand = $ref(false)

  // watch(
  //   () => isDataMode,
  //   newVal => {
  //     if (newVal === true) {
  //       isExpand = true
  //     }
  //     if (newVal === false) {
  //       isExpand = false
  //     }
  //   },
  // )

  // objectCategoryFirstLevelList().then((res: any) => {
  //   tabLevelOptions1 = res.data || []
  // })
</script>

<style lang="scss" scoped>
  .flex-container {
    .title {
      width: 50%;
      // background: red;
      // margin: 0 0 0 0.08rem;
      font-family: Source Han Sans;
      font-size: 0.15rem;
      font-weight: bold;
      line-height: 121.86%;
      letter-spacing: 0em;

      font-variation-settings: 'opsz' auto;
      font-feature-settings: 'kern' on;
      /* 新的/主色1 */
      color: #09fcc7;
    }
  }
</style>
