<template>
  <div class="h-full w-full reactive">
    reservoir
    <Map
      :isShow="isShow"
      :allData="allData"
      v-model:activeItem="activeItem"
      v-model:activeTabLevel2="activeTabLevel2"
      :currentDistrict="currentDistrict"
      :isDataMode="isDataMode"
    />
  </div>
</template>
<script setup lang="tsx" name="Reservoir">
  import { reactive, ref, onBeforeMount, onMounted } from 'vue'
  import { unmountLoading } from '@/core/loading'

  import Map from './Map'
  const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
  })

  let activeTabLevel2 = $ref(null)

  let activeItem = $ref(null)
  onMounted(() => {
    // unmountLoading()
  })
  let currentDistrict = $ref(null)
</script>
<style lang="scss" scoped></style>
