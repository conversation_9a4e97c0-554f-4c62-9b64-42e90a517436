<script lang="jsx">
  import { defineComponent, watch, ref, reactive, toRef, toRefs } from 'vue'
  import { useSizeStore } from '@/store'
  import { storeToRefs } from 'pinia'

  export default defineComponent({
    name: 'ScaleBox',
    props: {
      translate: { default: '' },
      transformOrigin: { default: 'left top' },
      style: { default: {} },
    },
    setup(props, ctx) {
      const { slots } = ctx
      const sizeStore = useSizeStore()
      // const { scaleNum } = toRefs(sizeStore) // 不生效
      // watch(
      //   () => sizeStore.scaleNum,
      //   () => {
      //   },
      // )

      return () => (
        <div
          style={{
            position: 'absolute',
            transformOrigin: props.transformOrigin,
            transform: `scale(${sizeStore.scaleNum}) ${props.translate}`,
            width: props?.width || 'auto',
            ...props.style,
          }}
        >
          {slots.default && slots.default()}
        </div>
      )
    },
  })
</script>

<style lang="scss" scoped>
  .scale-box {
    position: absolute;
    pointer-events: auto;
  }
</style>
