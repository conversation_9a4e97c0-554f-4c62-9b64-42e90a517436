<template>
  <div style="height: 100%; display: flex; position: relative">
    <div style="flex: 1; display: flex; flex-direction: column">
      <div style="flex: 1" class="map-content">
        <!-- <div class="m-1px size-[calc(100%-2px)] border-rd-0.08rem overflow-hidden"></div> -->
        <div class="header">
          <div class="title">推演概化图</div>
          <div class="unit">流量单位：m³/s</div>
        </div>
        <div class="content">
          <div
            class="flow-map w-4.06rem h-2.8rem mx-0.1rem relative text-0.14rem"
            :style="{ background: `url(${getImageUrl('water-resource/flow-map-bg2.png')}) no-repeat center / 100% 100%` }"
          >
            <!-- left-branch1 -->
            <!-- <div class="left-branch1" v-if="state.projects['430922203000_CJCJZZ']?.open > 0">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div> -->
            <div
              class="left-branch1-top"
              v-if="state.itemOption?.find(item => item.projectCode === '430922103000_XSDFSZ')?.open > 0"
            >
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- state.projects['430922103000_LTSXHZ'] -->
            <div
              class="left-branch1-center"
              v-if="state.itemOption?.find(item => item.projectCode === '430922103000_LTSXHZ')?.open > 0"
            >
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- state.projects['430922203000_CJCJZZ'] -->
            <div
              class="left-branch1-bottom"
              v-if="state.itemOption?.find(item => item.projectCode === '430922203000_CJCJZZ')?.open > 0"
            >
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- left-branch2 -->
            <!-- <div class="left-branch2" v-if="state.projects['430922203000_GQJZZ']?.open > 0">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div> -->
            <!-- state.projects['430922212000_LJAFSZ'] -->
            <div
              class="left-branch2-top"
              v-if="state.itemOption?.find(item => item.projectCode === '430922212000_LJAFSZ')?.open > 0"
            >
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- state.projects['430922212000_WJXHZ'] -->
            <div
              class="left-branch2-center"
              v-if="state.itemOption?.find(item => item.projectCode === '430922212000_WJXHZ')?.open > 0"
            >
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- state.projects['430922203000_GQJZZ'] -->
            <div
              class="left-branch2-bottom"
              v-if="state.itemOption?.find(item => item.projectCode === '430922203000_GQJZZ')?.open > 0"
            >
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- state.projects['430922203000_ZLYCFSZ'] -->
            <div class="left-bottom" v-if="state.itemOption?.find(item => item.projectCode === '430922203000_ZLYCFSZ')?.open > 0">
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- <div class="right">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div> -->
            <!-- v-if="state.projects['430922108000_GLCDHX']?.isOpen == 1" -->
            <div class="right-top" v-if="state.itemOption?.find(item => item.projectCode === '430922108000_GLCDHX')?.open > 0">
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- v-if="state.projects['430922108000_DFLDH']?.isOpen == 1" -->
            <div class="right-middle" v-if="state.itemOption?.find(item => item.projectCode === '430922108000_DFLDH')?.open > 0">
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <!-- v-if="state.projects['430922108000_SSWFSZ']?.isOpen == 1" -->
            <div class="right-bottom" v-if="state.itemOption?.find(item => item.projectCode === '430922108000_SSWFSZ')?.open > 0">
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>
            <div class="center">
              <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
            </div>

            <div class="absolute w-0.8rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '1.23rem', left: '0.02rem' }">
              沾溪干渠
            </div>
            <!-- 小石洞 -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.24rem', left: '0.46rem' }">
              小石洞
              <!-- {{ state.projects['430922103000_XSDFSZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '0.04rem', left: '0.5rem' }">
              分水闸
              <!-- {{ state.projects['430922103000_XSDFSZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.26rem', left: '1.02rem' }">
              {{ state.itemOption?.find(item => item.projectCode == '430922103000_XSDFSZ')?.outFlow }}
            </div>
            <!-- 龙头山 -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.76rem', left: '0.48rem' }">
              龙头山
              <!-- {{ state.projects['430922103000_LTSXHZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '0.56rem', left: '0.5rem' }">
              泄洪闸
              <!-- {{ state.projects['430922103000_LTSXHZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.76rem', left: '1.02rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922103000_LTSXHZ')?.outFlow }}
            </div>
            <!-- 陈家冲 -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.42rem', left: '0.57rem' }">
              陈家冲
              <!-- {{ state.projects['430922203000_CJCJZZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '1.64rem', left: '0.62rem' }">
              节制闸
              <!-- {{ state.projects['430922203000_CJCJZZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '1.42rem', left: '-0.02rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922203000_CJCJZZ')?.outFlow }}
            </div>

            <div class="absolute w-0.8rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '2rem', left: '0.66rem' }">
              西干渠
            </div>

            <!-- 李家坳分水闸（西） -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.26rem', left: '2.22rem' }">
              李家坳
              <!-- {{ state.projects['430922212000_LJAFSZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '0.052rem', left: '2.2rem' }">
              分水闸
              <!-- {{ state.projects['430922212000_LJAFSZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.26rem', left: '2.74rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922212000_LJAFSZ')?.outFlow }}
            </div>
            <!-- 万金泄洪闸  （西） -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.87rem', left: '2.28rem' }">
              万金
              <!-- {{ state.projects['430922212000_WJXHZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '0.66rem', left: '2.2rem' }">
              泄洪闸
              <!-- {{ state.projects['430922212000_WJXHZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.86rem', left: '2.74rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922212000_WJXHZ')?.outFlow }}
            </div>
            <!-- 高桥节制闸 -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.42rem', left: '1.36rem' }">
              高桥
              <!-- {{ state.projects['430922203000_GQJZZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '1.64rem', left: '1.34rem' }">
              节制闸
              <!-- {{ state.projects['430922203000_GQJZZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '1.42rem', left: '1.86rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922203000_GQJZZ')?.outFlow }}
            </div>
            <!-- 子良岩村分水闸 -->
            <div class="absolute w-0.6rem h-0.2rem text-[#ffffff]" :style="{ top: '2.25rem', left: '1.35rem' }">
              子良岩村
              <!-- {{ state.projects['430922203000_ZLYCFSZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '2.48rem', left: '1.44rem' }">
              分水闸
              <!-- {{ state.projects['430922203000_ZLYCFSZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '2.26rem', left: '0.72rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922203000_ZLYCFSZ')?.outFlow }}
            </div>

            <!-- 狮山湾分水闸（东） -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.8rem', left: '2.48rem' }">
              狮山湾
              <!-- {{ state.projects['430922108000_SSWFSZ']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '1.56rem', left: '2.52rem' }">
              分水闸
              <!-- {{ state.projects['430922108000_SSWFSZ']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '1.78rem', left: '3rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922108000_SSWFSZ')?.outFlow }}
            </div>
            <!-- 大伏岭倒虹（东） -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.46rem', left: '3.55rem' }">
              大伏岭
              <!-- {{ state.projects['430922108000_DFLDH']?.name }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '1.24rem', left: '3.64rem' }">
              倒虹吸
              <!-- {{ state.projects['430922108000_DFLDH']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '1.66rem', left: '3.53rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922108000_DFLDH')?.outFlow }}
            </div>
            <!-- 郭里冲倒虹吸（东） -->
            <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.05rem', left: '3.54rem' }">
              郭里冲
              <!-- {{ state.projects['430922108000_GLCDHX']?.name }} -->
            </div>
            <div
              class="absolute w-0.5rem h-0.2rem text-[#B2DAEA] text-0.12rem text-right"
              :style="{ top: '0.3rem', left: '3.52rem' }"
            >
              倒虹吸
              <!-- {{ state.projects['430922108000_GLCDHX']?.type }} -->
            </div>
            <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '0.46rem', left: '3.5rem' }">
              {{ state.itemOption?.find(item => item.projectCode === '430922108000_GLCDHX')?.outFlow }}
            </div>
            <div class="absolute w-0.8rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '2rem', left: '3.54rem' }">
              东干渠
            </div>

            <div class="absolute w-0.8rem h-0.2rem text-[#B2DAEA] text-0.12rem" :style="{ top: '2.5rem', left: '2.26rem' }">
              总干渠
            </div>
          </div>
        </div>
        <div class="footer">
          <div class="map"></div>
          <div class="icon"></div>
          <div class="title"></div>
        </div>
      </div>
      <div class="h-0.34rem mt-0.2rem">
        <TimePlaySlider v-if="state.times.length" :times="state.times" @onTimeChange="onTimeChange" />
      </div>
    </div>
    <!-- 表格 -->
    <div style="width: 8rem; margin-left: 0.12rem">
      <n-data-table :columns="columns" :data="state.tableData" max-height="420px" />
    </div>

    <!-- <ResultChartModel ref="showChartModel" :dataSource="state.chartData" @close="state.showChart = false" /> -->
    <div class="process-box" v-if="!!state.activeProcess">
      <div class="absolute size-full c-#ffffff z-999">
        <div class="header uno-bg_/modal-header.png">
          <div class="name">渠系水动力仿真过程</div>
          <div class="icon">{{ state.activeProcess?.chProjectName?.slice(0, 1) }}</div>
          <div class="title">{{ state.activeProcess.projectName }}</div>
          <MyIcon
            class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer ml-auto mt-0.06rem"
            @click="state.activeProcess = null"
          />
        </div>
        <div class="h-2rem w-4.8rem">
          <ResultChart :dataSource="state.activeProcess" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx" name="概化图">
  import { reactive, ref, onBeforeMount, onMounted } from 'vue'
  import { getOptions, getValueByKey } from '@/api/common.ts'
  import TimePlaySlider from '@/components/TimeSlider/index.vue'
  // import ResultChartModel from './ResultChartModel.vue'
  import ResultChart from './ResultChart.vue'
  import { getResourceConsumptionList, getChSimPage, getInferRes, getScaleResDetails, getChSimResList } from '../../services.ts'
  import dayjs from 'dayjs'

  const { chSimId, dataSource, mapData } = defineProps(['chSimId', 'dataSource', 'mapData'])
  const showChartModel = ref(false)
  const state = reactive({
    itemOption: [],
    times: [], //[...new Set(mapData[0].resVOS.map(el => el.tm))],
    mapData: null,
    codes: '',
    projects: {},
    itemInfo: [],
    chartData: [],
    tableData: [],
    showChart: false,
    activeProcess: null,
  })
  const columns = [
    // { type: 'seq', title: '序号', width: 50 },

    {
      title: '调度对象',
      key: 'projectName',
      minWidth: 140,
      ellipsis: {
        tooltip: true,
      },
      render: (row, idx) => (
        <div class='c-#07D1FA cursor-pointer' onClick={() => nameClick(row)}>
          <n-performant-ellipsis>{row.projectName}</n-performant-ellipsis>
        </div>
      ),
    },
    {
      key: 'chProjectName',
      title: '所属干渠',
      minWidth: 70,
      ellipsis: {
        tooltip: true,
      },
    },
    {
      key: 'startUpWlv',
      title: '初始上游水位(m)',
      minWidth: 80,
    },
    {
      key: 'startDownWlv',
      title: '初始下游水位(m)',
      minWidth: 80,
    },
    {
      key: 'endUpWlv',
      title: '末期上游水位(m)',
      minWidth: 80,
    },
    {
      key: 'endDownWlv',
      title: '末期下游水位(m)',
      minWidth: 80,
    },
    {
      key: 'sumOutFlow',
      title: '累计过闸流量(万m³)',
      minWidth: 90,
    },
  ]
  onMounted(() => {
    // unmountLoading()

    init()
  })

  const init = () => {
    getValueByKey('thj.map.codes').then(res => {
      state.codes = res.data
      getList()
    })
    getOptions('scaleProjectCode').then(res => {
      state.itemInfo = res.data.map(el => ({
        projectCode: el.key,
        projectName: el.value,
        projectType: el.option1,
      }))
    })
  }

  const onTimeChange = time => {
    let arr = []
    state.mapData?.forEach(el => {
      arr = arr.concat(el.resVOS?.find(ele => ele.tm == time).records)
    })
    const factArr = arr.filter(el => !!+el.longitude && !!+el.latitude)
    state.itemInfo?.forEach(el => {
      if (factArr.some(ele => ele.projectCode === el.projectCode)) {
        dealPopup(factArr.find(ele => ele.projectCode === el.projectCode))
      }
    })
    state.itemOption = [...state.itemInfo]
  }
  const dealPopup = curr => {
    let index = state.itemInfo?.findIndex(el => el.projectCode === curr.projectCode)
    if (index === -1) {
      state.itemInfo.push({ projectCode: curr.projectCode })
    } else {
      state.itemInfo[index] = { ...state.itemInfo[index], ...curr }
    }
  }
  const getList = () => {
    let param = { projectCodes: state.codes }

    getResourceConsumptionList(param).then(res => {
      // state.projects = transferData(res.data)
      // let newObj = {};
      let list = res.data
      list.forEach(item => {
        let type, name
        if (item.projectName.includes('分水闸')) {
          type = '分水闸'
          name = item.projectName.replace('分水闸', '')
        } else if (item.projectName.includes('倒虹吸')) {
          type = '倒虹吸'
          name = item.projectName.replace('倒虹吸', '')
          // name = item.projectName
        } else if (item.projectName.includes('倒虹')) {
          type = '倒虹吸'
          name = item.projectName.replace('倒虹', '')
          // name = item.projectName
        } else if (item.projectName.includes('节制闸')) {
          type = '节制闸'
          name = item.projectName.replace('节制闸', '')
        } else if (item.projectName.includes('泄洪闸')) {
          type = '泄洪闸'
          name = item.projectName.replace('泄洪闸', '')
        } else {
          type = '未知类型' // 默认类型
        }
        state.projects[item.projectCode] = {
          code: item.projectCode,
          name: name,
          isOpen: item.isOpen,
          flow: item.flow,
          type: type, // 这里假设所有项目都是分水闸类型，如果需要动态判断可以根据item的其他属性
        }
      })
    })
    if (chSimId) {
      getScaleResDetails({ chSimId: chSimId }).then(res => {
        state.tableData = res.data
      })
      getInferRes({ chSimId: chSimId }).then(res => {
        state.mapData = res.data.map(el => ({
          ...el,
          resVOS: el.resVOS.map(item => ({
            ...item,
            records: item.records.map(element => ({ ...element, ditch: { ...el, projects: null, resVOS: null } })),
          })),
        }))
        state.times = [...new Set(state.mapData[0].resVOS.map(el => el.tm))]
        // state.itemOption = state.mapData[0].resVOS[0]
        onTimeChange(state.times[0])
      })
    }
  }

  const nameClick = item => {
    // showChartModel.value.openModal()
    getChSimResList({ chSimId: chSimId, projectId: item.projectId }).then(res => {
      console.log('名称点击 弹窗 res', res)
      let obj = res.data[0]
      state.activeProcess = {
        ...item,
        ...obj,
        chartData: res.data?.map(item => ({
          ...item, // 保留其他字段
          tm: dayjs(item.tm).format('MM-DD HH'), // 格式化时间
        })),
      }
      console.log('名称点击 弹窗', state.activeProcess)
    })
  }
</script>
<style lang="scss" scoped>
  .map-content {
    position: relative;
    border-radius: 0.09rem;
    overflow: hidden;
    // padding: 1px;
    display: flex;
    flex-direction: column;
    &::before {
      content: '';
      position: absolute;
      z-index: 0;
      width: calc(100% - 2px);
      height: calc(100% - 2px);
      border-radius: 0.07rem;
      background: conic-gradient(#09c8fc, rgba(9, 200, 252, 0.2), #09c8fc);
      padding: 1px;
      -webkit-mask:
        linear-gradient(#fff 0 100%) content-box,
        linear-gradient(#fff 0 100%);
      -webkit-mask-composite: xor;
    }

    .header {
      display: flex;
      justify-content: space-between;
      //
      margin: 0.2rem;
      .title {
        // font-weight: bold;
        font-size: 0.16rem;
        font-weight: 500;
        line-height: 0.32rem;
        letter-spacing: 0px;
        align-items: center;
        color: #fff;
      }
      .unit {
        display: flex;
        font-size: 0.14rem;
        font-weight: normal;
        line-height: normal;
        letter-spacing: 0em;
        color: #fff;
        align-items: center;
      }
    }
    .content {
      opacity: 1;
      margin-left: 0.6rem;
      // background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
      // background: red;
      box-sizing: border-box;
      // border: 1px solid rgba(54, 96, 103, 0.5);
      z-index: 999;

      .flow-map {
        img {
          width: 4.06rem;
          height: 2.8rem;
          position: absolute;
          top: 0;
          left: 0;
        }
        .left-branch1 img {
          // margin-top: 0.01rem;
          margin-left: -0.02rem;
          /* 左上  右上 右下 左下*/
          clip-path: polygon(1% 1%, 27% 1%, 27% 47%, 1% 47%);
          /* background-color: red; */
        }
        /* 显示左边 左分支上面部分 */
        .left-branch1-top img {
          margin-top: -0.07rem;
          margin-left: -0.02rem;
          /* 左上  右上 右下 左下*/
          clip-path: polygon(0% 6%, 8% 6%, 8% 12%, 0% 12%);
          // background-color: red;
        }

        /* 显示左边 左分支中间部分 */
        .left-branch1-center img {
          margin-left: -0.02rem;
          clip-path: polygon(1% 16%, 14% 16%, 14% 27%, 1% 27%);
          // background-color: red;
        }
        /* 显示左边 左分支下面部分 */
        .left-branch1-bottom img {
          margin-top: -0.01rem;
          margin-left: -0.02rem;
          /* clip-path: polygon(1% 30%, 49% 30%, 49% 45%, 1% 45%); */
          clip-path: polygon(1% 34%, 22% 34%, 22% 45%, 1% 45%);
          // background-color: red;
        }
        /* 显示左边 右分支 */
        .left-branch2 img {
          // margin-top: 0.01rem;
          margin-left: -0.01rem;
          /* 左上  右上 右下 左下*/
          clip-path: polygon(28% 6%, 52% 6%, 52% 47%, 28% 47%);
          /* background-color: red; */
        }
        /* 显示左边 右分支上面部分 */
        .left-branch2-top img {
          margin-top: -0.08rem;
          margin-left: -0.01rem;
          /* 左上  右上 右下 左下*/
          clip-path: polygon(46% 6%, 49% 6%, 49% 14%, 46% 14%);
          // background-color: red;
        }

        /* 显示左边 右分支中间部分 */
        .left-branch2-center img {
          margin-left: -0.01rem;
          clip-path: polygon(46% 16%, 51.5% 16%, 51.5% 32%, 46% 32%);
          // background-color: red;
        }
        /* 显示左边 右分支下面部分 */
        .left-branch2-bottom img {
          margin-top: 4px;
          margin-left: -0.01rem;
          /* clip-path: polygon(1% 30%, 49% 30%, 49% 45%, 1% 45%); */
          clip-path: polygon(35% 38%, 48.6% 37%, 48.6% 45%, 35% 45%);
          // background-color: red;
        }

        /* 显示左边下面部分 */
        .left-bottom img {
          /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
          margin-top: 0.08rem;
          margin-left: -0.02rem;
          clip-path: polygon(1% 45%, 43% 45%, 44% 78%, 1% 78%);
          // background-color: red;
        }

        /* 显示右上部分 */
        .right img {
          // margin-top: 8.5px;
          margin-left: 1px;
          clip-path: polygon(52% 0%, 100% 0%, 100% 78%, 52% 78%);
          background-color: aqua;
        }
        /* 显示右上部分 */
        .right-top img {
          margin-left: 1px;
          clip-path: polygon(50% 0, 100% 0, 100% 12%, 50% 12%);
          // background-color: aqua;
        }

        /* 显示右边中间部分 */
        .right-middle img {
          margin-left: 1px;
          /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
          clip-path: polygon(54% 18%, 100% 18%, 100% 40%, 54% 40%);
          // background-color: red;
        }
        /* 显示右边下面部分 */
        .right-bottom img {
          /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
          margin-top: 8.2px;
          margin-left: 1px;
          clip-path: polygon(59% 46%, 100% 46%, 100% 78%, 58.8% 78%);
          // background-color: red;
        }
        .center img {
          /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
          margin-top: 12px;
          margin-left: -0.5px;
          clip-path: polygon(46% 73.4%, 60% 73.4%, 60% 98%, 46% 98%);
          /* background-color: red; */
        }
      }
    }
    .footer {
      display: flex;
      position: relative;
      height: 2rem;
      margin-top: 0.3rem;
      // background: red;
      .map {
        position: absolute;
        right: 0.4rem;
        top: 0;

        width: 5rem;
        height: 1.4rem;
        background: url('@/assets/images/water-resource/reservior-map.png') no-repeat;
        background-size: 100% 100%;
      }
      .icon {
        position: absolute;
        right: 2.28rem;
        top: 0.2rem;
        width: 0.3rem;
        height: 0.3rem;
        background: url('@/assets/images/water-resource/water-resource-icon.png') no-repeat;
        background-size: 100% 100%;
      }
      .title {
        position: absolute;
        right: 1.2rem;
        top: 0.18rem;
        width: 1.02rem;
        height: 0.32rem;
        background: url('@/assets/images/water-resource/water-resource-title.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .process-box {
    position: fixed;
    z-index: 11;
    right: 5.4rem;
    bottom: 4rem;
    width: 4.8rem;
    height: 2.34rem;
    backdrop-filter: blur(0.05rem);
    // box-shadow: inset 0px 0.04rem 0.2rem 0px #009bff;
    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%);
    background: linear-gradient(180deg, rgba(12, 32, 41, 0.9) 0%, rgba(31, 83, 106, 0.9) 100%);

    overflow: hidden;
    padding: 1px;
    border-radius: 0.06rem;
    &::before {
      content: '';
      position: absolute;
      z-index: 0;
      width: calc(100% - 1px);
      height: calc(100% - 1px);
      border-radius: 0.06rem;
      background: conic-gradient(#009bff, rgba(255, 255, 255, 0.9), #009bff);
      padding: 1px;

      -webkit-mask:
        linear-gradient(#fff 0 100%) content-box,
        linear-gradient(#fff 0 100%);
      -webkit-mask-composite: xor;
    }

    .header {
      // background: linear-gradient(78deg, #0acaff 0%, rgba(10, 202, 255, 0.15) 100%);
      border-radius: 0.04rem 0.04rem 0px 0px;
      display: flex;
      // align-items: center;
      // justify-content: space-between;

      font-family: AlimamaShuHeiTi;
      font-size: 0.16rem;
      letter-spacing: 1px;
      text-shadow: 0px 0.02rem 0.04rem rgba(0, 0, 0, 0.5);
      padding: 0.02rem 0.12rem;
      .name {
        font-size: 0.18rem;
        font-weight: bold;
      }
      .icon {
        width: 0.2rem;
        height: 0.2rem;
        margin: 0.05rem 0.08rem 0 0.08rem;
        // padding-bottom: 0.04rem;
        border-radius: 50%;
        background: #0d9c47;
        color: #fff;
        display: inline-block;
        text-align: center;
        font-size: 0.14rem;
        line-height: 0.2rem;
      }
      .title {
        font-size: 0.14rem;
        line-height: 0.3rem;
        // margin-top: 0.1rem;
      }
    }
  }

  :deep(.n-data-table.n-data-table--bordered .n-data-table-wrapper) {
    border-color: rgba(105, 157, 178, 0.6) !important;
  }
  :deep(.n-data-table .n-data-table-thead) {
    border-color: rgba(105, 157, 178, 0.5) !important;
  }
  :deep(.n-data-table .n-data-table-th, .n-data-table .n-data-table-td) {
    border-color: rgba(105, 157, 178, 0.4) !important;
  }
  :deep(.n-data-table .n-data-table-tr, .n-data-table .n-data-table-td) {
    border-color: rgba(105, 157, 178, 0.2) !important;
  }
  :deep(.n-data-table .n-data-table-td) {
    border-color: rgba(105, 157, 178, 0.2) !important;
  }
</style>
