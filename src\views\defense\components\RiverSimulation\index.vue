<template>
  <ThjModal ref="$modal" :showFooter="false">
    <!-- class="h-94% pt-2% flex relative" -->
    <div class="w-96% h-96% ml-2% mr-2% mt-1% mb-3% flex relative border-radius-0.16rem">
      <i class="light block absolute bottom--0.26rem"></i>
      <div class="list-card w-3.47rem p-0.15rem">
        <div
          class="row w-3.15rem h-0.39rem lh-0.35rem flex mb-0.08rem px-0.08rem cursor-pointer border-radius-0.1rem"
          v-for="el in state.list"
          :key="el.chSimId"
          :class="{ active: state.baseInfo?.chSimId === el.chSimId }"
          @click="changeDispatch(el)"
        >
          <label class="text-0.16rem font-500">{{ el.caseName }}</label>
          <a class="w-0.64rem h-0.21rem lh-0.21rem text-center mt-0.07rem ml-auto c-#09FCC7 text-0.12rem check">查看结果</a>
        </div>

        <n-pagination
          class="mt-0.3rem"
          size="small"
          v-model:page="state.pageNum"
          @update:page="changePageNum"
          :item-count="state.total"
        />
      </div>
      <div class="flex-1 ml-0.15rem flex-col">
        <div class="header" v-if="!!state.baseInfo">
          <div class="item">
            <div class="label">方案名称:&nbsp;</div>
            <div class="value">
              <a-tooltip>
                <template slot="title">{{ state.baseInfo.caseName }}</template>
                {{ state.baseInfo.caseName }}
              </a-tooltip>
            </div>
          </div>
          <div class="item" style="width: 20%">
            <div class="label">仿真类型:&nbsp;</div>
            <div class="value">{{ simulateTypeOptions.find(ele => ele.value == state.baseInfo.simulateType)?.label }}</div>
          </div>
          <div class="item" style="width: 20%">
            <div class="label">预报范围:&nbsp;</div>
            <div class="value">{{ state.fcstRangeOptions.find(ele => ele.value == state.baseInfo.fcstRange)?.label }}</div>
          </div>
          <div class="item" style="width: 33%">
            <div class="label">仿真时段:&nbsp;</div>
            <div class="value">
              {{ `${state.baseInfo.startTime} - ${state.baseInfo.endTime}` }}
            </div>
          </div>
          <div class="item">
            <div class="label">方案编号:&nbsp;</div>
            <div class="value">{{ state.baseInfo.caseCode }}</div>
          </div>
          <div class="item" style="width: 20%">
            <div class="label">调度类型:&nbsp;</div>
            <div class="value">{{ dispatchTypeOptions.find(ele => ele.value == state.baseInfo.dispathType)?.label }}</div>
          </div>
          <div class="item" style="width: 20%">
            <div class="label">发起人:&nbsp;</div>
            <div class="value">{{ state.baseInfo.createdUserName }}</div>
          </div>
          <div class="item" style="width: 33%">
            <div class="label">仿真生成时间:&nbsp;</div>
            <div class="value">{{ state.baseInfo.saveTime }}</div>
          </div>
        </div>
        <NaSegmentTabs
          class="w-1.6rem my-0.16rem"
          v-model:value="state.activeTab"
          :options="[
            { key: 1, title: '推演仿真' },
            { key: 2, title: '推演概化图' },
          ]"
          @update:value="changeTab"
        ></NaSegmentTabs>
        <div class="flex-1">
          <Simulation
            v-if="state.activeTab === 1 && !!state.data2 && !!state.data1"
            :chSimId="state.baseInfo?.chSimId"
            :mapData="state.data1"
            :dataSource="state.data2"
          />
          <Gaihuatu
            v-if="state.activeTab === 2"
            :chSimId="state.baseInfo?.chSimId"
            :mapData="state.data1"
            :dataSource="state.data3"
          />
        </div>
      </div>
    </div>
  </ThjModal>
</template>
<script setup lang="ts" name="DispatchModel">
  import { getOptions } from '@/api/common.ts'
  import { getChSimPage, getInferRes, getChSimRange, getScaleResDetails } from '../../services'
  import Simulation from './Simulation.vue'
  import Gaihuatu from './Gaihuatu.vue'
  import axios from 'axios'

  const dispatchTypeOptions = [
    { label: '流量调度', value: 1 },
    { label: '开度调度', value: 2 },
  ]

  const simulateTypeOptions = [{ label: '人工模拟', value: 2 }]

  const [$modal, okLoading, loading, title] = useModal()
  const attrs = useAttrs()

  const state = reactive({
    list: [],
    total: 0,
    pageNum: 1,
    pageSize: 16,
    baseInfo: null,
    fcstRangeOptions: [],
    activeTab: 1,

    data1: null,
    data2: null,
    data3: null,
  })

  getOptions('fcstRange').then(res => {
    state.fcstRangeOptions = res.data.map(el => ({ label: el.value, value: el.key }))
  })

  const openModal = async () => {
    getList()

    $modal.value?.open({
      loading: false,
      contentStyle: { width: '18rem', height: '10rem' },
      headerStyle: { display: 'none' },
      title: '渠系水动力仿真',
      onClose: () => {
        // 此处重置状态
        state.baseInfo = null
        attrs.onClose && attrs.onClose()
      },
    })
  }
  defineExpose({ openModal })

  const getList = () => {
    getChSimPage({ pageNum: state.pageNum, pageSize: state.pageSize }).then(res => {
      state.list = res?.data?.data
      state.total = Math.ceil(res?.data?.total / state.pageSize)
      state.baseInfo = state.list[0]

      getData()
    })
  }

  const changeDispatch = row => {
    state.baseInfo = row
    changeTab()
  }

  const changePageNum = val => {
    state.pageNum = val
    getList()
  }

  const changeTab = val => {
    state.data1 = null
    state.data2 = null
    state.data3 = null
    nextTick(() => getData())
  }

  const getData = async () => {
    if (state.activeTab === 1) {
      getInferRes({ chSimId: state.baseInfo.chSimId }).then(res => {
        state.data1 = res.data.map(el => ({
          ...el,
          resVOS: el.resVOS.map(item => ({
            ...item,
            records: item.records.map(element => ({
              ...element,
              ditch: { ...el, projects: null, resVOS: null },
            })),
          })),
        }))
      })

      getChSimRange({ type: 1 }).then(resp => {
        axios
          .get('http://zjhhzkhz.cn:9091/thjgq/projectCover/2jW5uDF8z9-uuid-ff438966-031d-463e-993f-b324d3e89344.json')
          .then(res => {
            nextTick(() => {
              resp.data[0].projects.forEach(el => {
                res.data[el.projectCode] = { ...el, ...res.data[el.projectCode] }
              })
              state.data2 = res.data
            })
          })
      })
    }
    if (state.activeTab === 2) {
      getInferRes({ chSimId: state.baseInfo.chSimId }).then(res => {
        state.data1 = res.data.map(el => ({
          ...el,
          resVOS: el.resVOS.map(item => ({
            ...item,
            records: item.records.map(element => ({ ...element, ditch: { ...el, projects: null, resVOS: null } })),
          })),
        }))
      })
      getScaleResDetails({ chSimId: state.baseInfo.chSimId }).then(res => {
        state.data3 = res.data
      })
    }
  }
</script>
<style lang="scss" scoped>
  .light {
    width: 3.69rem;
    height: 0.58rem;
    background: radial-gradient(37% 36% at 50% 50%, #5defff 0%, rgba(42, 190, 206, 0) 100%);
  }
  .list-card {
    background:
      linear-gradient(180deg, rgba(6, 55, 50, 0) 27%, rgba(12, 164, 149, 0.52) 98%),
      linear-gradient(180deg, rgba(2, 109, 143, 0.6) -5%, rgba(4, 79, 120, 0.6) 98%);
    border: 1px solid rgba(42, 190, 206, 0.62);
  }
  .row {
    border-radius: 0.04rem;
    background: rgba(9, 200, 252, 0.2);
    border: 1px solid rgba(105, 157, 178, 0.5);
    &.active {
      border-radius: 4px;
      background: #158ca0;
      box-sizing: border-box;
      border: 2px solid #09fcc7;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 0.06rem;
        left: 0.01rem;
        width: 0.1rem;
        height: 0.24rem;
        background: url('@/assets/images/dispatch-row-dot.png') no-repeat center / 100% 100%;
      }
      &::after {
        content: '';
        position: absolute;
        transform: rotate(180deg);
        top: 0.06rem;
        right: 0.01rem;
        width: 0.1rem;
        height: 0.24rem;
        background: url('@/assets/images/dispatch-row-dot.png') no-repeat center / 100% 100%;
      }
      .check {
        color: #fff;
        border-radius: 2px;
        background: #09fcc7;
      }
    }
  }
  .n-pagination {
    justify-content: flex-end;
  }

  .header {
    padding: 0.08rem 0.16rem;
    background: url('/src/assets/images//dispatch-info-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    border-radius: 0.04rem;
    display: flex;
    flex-wrap: wrap;
    font-size: 0.14rem;
    .item {
      display: flex;
      width: 25%;
      height: 0.3rem;
      line-height: 0.3rem;
      .label {
        color: #b2daea;
        white-space: nowrap;
      }
      .value {
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
</style>
