<template>
  <ScaleBox :style="{ width: '420px', height: '196px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="<PERSON><PERSON><PERSON>">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()

  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    let type = data[0].type
    let xData = data[0].xData // [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    let seriesData = data[0].data //[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    // let colors = ['rgba(108,230,103,1)', 'rgba(30,89,115,0.5)', 'transparent']
    let colors = ['rgba(108,230,103,1)', 'rgba(30,89,115,0.5)', 'rgba(146, 183, 202, 0.3)']
    return {
      tooltip: {
        show: true,
        // trigger: 'axis',
        // appendToBody: true,
        // confine: true,
        // // alwaysShowContent: true,
        // className: 'echart-tooltip',
        // backgroundColor: 'rgba(22,45,72,0.84)',
        // axisPointer: {
        //   type: 'shadow', // shadow cross
        //   label: {
        //     backgroundColor: 'rgba(152,224,255,0.15)',
        //   },
        // },
        trigger: 'axis',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '-') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              function getColor(seriesName) {
                switch (seriesName) {
                  case '需水量':
                    return '#09FCC7'
                  case '实际用水量':
                    return '#F6CC57'
                  default:
                    return
                }
              }
              let color = getColor(seriesName) //图例颜色

              htmlStr += `
                    <div style="border: 1px solid rgba(152,224,255,0.3) border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} 万m³
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
        // formatter: params => {
        //   let dataStr = `
        //     <div style="line-height:1.2">
        //       <span style="font-size:0.12rem;color:var(--text-md);">${params[0].axisValue}</span>
        //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[0].seriesName}:<span style="color:var(--primary-color)">${params[0].value[1]}台时</span></div>
        //     </div>
        //   `
        //   return dataStr
        // },
      },
      grid: {
        left: 15,
        right: 10,
        top: 30,
        bottom: 5,
        containLabel: true,
      },

      legend: {
        show: true,
        textStyle: {
          color: 'rgba(222, 242, 252, 1)',
        },
        itemWidth: 12,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        axisLabel: {
          interval: 0, // 强制显示所有标签
          color: 'rgba(222, 242, 252, 1)',
        },
        data: xData,
      },
      yAxis: {
        type: 'value',
        name: '单位: 万m³',
        // nameGap: 12,
        minInterval: 1, //自动计算坐标轴最小间隔，例：设置成1，刻度没有小数
        // maxInterval: '', //自动计算坐标轴最大间隔
        nameTextStyle: {
          color: 'rgba(222, 242, 252, 1)',
          padding: [0, 0, 0, 20],
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: 'rgba(105, 157, 178, 1)',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        max: function (value) {
          return (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间
        },
      },
      series: seriesData.map((ele, idx) => ({
        type: 'bar',
        barMaxWidth: 8,
        name: ele.name,
        data: ele.data,
        itemStyle: {
          color:
            idx === 0
              ? new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(9, 252, 199,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(9, 252, 199,0.4)',
                  },
                ])
              : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: 'rgba(246, 204, 87,1)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(246, 204, 87,0.4)',
                  },
                ]),
        },
      })),
    }
  }
</script>

<style lang="scss" scoped></style>
