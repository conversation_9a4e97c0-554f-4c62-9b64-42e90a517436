import { createVNode, render } from 'vue'
import ComMarkerPopup from './PopupContent.vue'
// import ComMarkerPopup from '../layers/PopupContent.vue'
import mapboxgl from 'mapbox-gl'

export function mapboxPopup(map, item) {
  let elPopup = document.createElement('div')
  //ComMarkerPopup为组件名称
  let vNodePopup = createVNode(ComMarkerPopup, item)
  render(vNodePopup, elPopup)
  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: item.map_class == 'EL' ? [0, -26] : item.map_class == 'ZQ' ? [0, -50] : [0, -36],
    // offset: [0, -36],
  }
  // console.log('******** 16 16 16', item)

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(elPopup).addTo(map)
}
