<template>
  <div class="w-4.29rem fixed left-0.26rem top-1.13rem z-39">
    <div class="duty-card">
      <div class="w-full duty-person-card h-0.61rem relative uno-bg_twin-reservoir/duty-person-bg1.png">
        <i
          class="w-0.05rem h-0.14rem block rotate-(y-180) absolute left-0.06rem top-0.05rem uno-bg_twin-reservoir/duty-arrow.png"
        ></i>
        <i
          class="w-0.05rem h-0.14rem block rotate-(y-180) absolute left-0.06rem bottom-0.08rem uno-bg_twin-reservoir/duty-arrow.png"
        ></i>
        <i class="w-0.05rem h-0.14rem block absolute right-0.06rem top-0.08rem uno-bg_twin-reservoir/duty-arrow.png"></i>
        <i class="w-0.05rem h-0.14rem block absolute right-0.06rem bottom-0.08rem uno-bg_twin-reservoir/duty-arrow.png"></i>

        <div class="w-full h-0.27rem lh-0.27rem text-0.18rem c-#fff text-center uno-bg_twin-reservoir/duty-title-bg.png">
          今日值班人员
        </div>
        <div class="w-full h-0.27rem lh-0.29rem text-0.18rem c-#09FCC7 text-center text-overflow1">{{ personnel || '--' }}</div>
      </div>

      <div class="flex pt-0.12rem">
        <i class="w-0.4rem h-0.4rem block uno-bg_twin-reservoir/water-level.png"></i>
        <hgroup>
          <h6 class="font-350 text-0.14rem c-#B2DAEA">
            当前水位
            <span class="text-0.12rem c-#699DB2">m</span>
          </h6>
          <h3 class="text-0.2rem c-#fff mt-0.06rem">{{ attrs?.waterLevel }}</h3>
        </hgroup>
        <i class="w-0.4rem h-0.4rem ml-0.06rem block uno-bg_twin-reservoir/storage-capacity.png"></i>
        <hgroup>
          <h6 class="font-350 text-0.14rem c-#B2DAEA">
            当前库容
            <span class="text-0.12rem c-#699DB2">百万m³</span>
          </h6>
          <h3 class="text-0.2rem c-#fff mt-0.06rem">
            {{ state.storageCapacity }}
          </h3>
        </hgroup>
        <i class="w-0.4rem h-0.4rem block ml-0.06rem uno-bg_twin-reservoir/water-area.png"></i>
        <hgroup>
          <h6 class="font-350 text-0.14rem c-#B2DAEA">
            水面面积
            <span class="text-0.12rem c-#699DB2">万m²</span>
          </h6>
          <h3 class="text-0.2rem c-#fff mt-0.06rem">{{ attrs?.are }}</h3>
        </hgroup>
      </div>
    </div>

    <WaterForecast />

    <MyCard name="水库放水统计" class="relative">
      <template #headerRight>
        <div class="absolute w-0.98rem right-0.1rem top-0.05rem flex items-center">
          <NaSelect
            style="width: 0.98rem"
            class="mt-0.05rem"
            v-model:value="year"
            type="side"
            :show-checkmark="false"
            :options="state.yearList"
          >
            <template #arrow>
              <MyIcon
                class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
              />
            </template>
          </NaSelect>
        </div>
      </template>
      <div class="w-4.29rem h-2.3rem chart-bg mb-0.1rem">
        <LineChart v-if="state.lineChartData" :dataSource="state.lineChartData" :year="year" class="mt-0.2rem ml-0.14rem" />
        <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
      </div>
    </MyCard>

    <PowerGenerationStatistics />
  </div>
</template>
<script setup lang="ts" name="ReservoirLeft">
  import WaterForecast from './components/WaterForecast.vue'
  import { getDutyOfficer } from '../../services'

  import { getDrawCount } from '@/views/water-resource/services'
  import LineChart from '@/views/water-resource/ReservoirInformation/LineChart.vue'
  import PowerGenerationStatistics from '@/views/water-resource/PowerGenerationStatistics/index.vue'
  import { getValueByKey } from '@/api'

  const attrs = useAttrs()

  const personnel = ref(null)
  const year = ref(new Date().getFullYear())

  const state = reactive({
    storageCapacity: null,
    yearList: [],
    lineChartData: [],
  })

  onMounted(() => {
    getValueByKey('thjCapacity').then(res => {
      state.storageCapacity = res.data ? Number(res.data) : attrs?.storageCapacity
    })

    getDutyOfficer().then(res => {
      personnel.value = res?.data
    })
    generateYearList()
  })
  const generateYearList = () => {
    const currentYear = new Date().getFullYear()
    const startYear = 2023 // 生成从当前年份到前6年的年份数组
    for (let year = currentYear; year >= startYear; year--) {
      state.yearList.push({ label: String(year), value: String(year) })
    }
    //
    let param = { year: year.value }
    getDrawCount(param).then(res => {
      state.lineChartData = [
        {
          xData: res.data?.map(el => el.time),
          data: res.data?.map(el => [el.time, el.value]), // [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
        },
      ]
    })
  }
</script>
<style lang="scss" scoped>
  .duty-card {
    width: 4.29rem;
    height: 1.28rem;
    border-radius: 0.04rem;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
  }
  .duty-person-card {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      width: 4.29rem;
      height: 0.61rem;
      background: url('@/assets/images/twin-reservoir/duty-person-bg2.png') no-repeat center / 100% 100%;
    }
  }
  .chart-bg {
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    border: 1px solid rgba(54, 96, 103, 0.5);
  }
</style>
