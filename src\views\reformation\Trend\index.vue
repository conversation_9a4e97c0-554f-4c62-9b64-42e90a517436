<template>
  <MyCard name="用水量趋势图" class="relative">
    <template #headerRight>
      <!-- style="position: absolute; width: 0.98rem; right: 0.1rem; top: 0.08rem; display: flex" -->
      <NaSelect class="absolute w-0.8rem flex right-0.1rem top-0.1rem" v-model:value="state.year" type="side"
        :show-checkmark="false" :options="state.yearList">
        <template #arrow>
          <MyIcon
            class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.23rem c-[#fff] mt--0.08rem mr-0.08rem" />
        </template>
      </NaSelect>
      <NaSelect class="absolute w-1rem flex right-1rem top-0.1rem" v-model:value="state.districtCode" type="side"
        :show-checkmark="false" :options="state.districtList">
        <template #arrow>
          <MyIcon
            class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.23rem c-[#fff] mt--0.08rem mr-0.08rem" />
        </template>
      </NaSelect>
    </template>
    <!-- bg-[rgba(0,0,0,.2)] 'horizontal'（水平）或 'vertical'（垂直）-->
    <div class="content pt-0.05rem h-6.5rem flex flex-col">
      <div class="mt-0.16rem mx-0.16rem h-1.86rem text-[#fff] text-0.14rem flex">
        <MultiLineChart :dataSource="state.lineChartData" />
      </div>
      <!-- h-4rem -->
      <div class="mt-0.25rem ml-0.16rem h-4rem ">
        <div class="text-0.12rem  text-[#fff] font-[350] h-0.17rem w-[100%]">
          <div class="float-left flex flex-row h-0.17rem">
            <div class="h-0.17rem w-0.17rem"
              :style="{ background: `url(${getImageUrl('reformation/type1.png')}) no-repeat center / 100% 100%` }">
            </div>
            <div class="w-0.48rem">用水正常</div>
          </div>
          <div class="float-left ml-0.16rem flex flex-row h-0.17rem">
            <div class="h-0.17rem w-0.17rem"
              :style="{ background: `url(${getImageUrl('reformation/type2.png')}) no-repeat center / 100% 100%` }">
            </div>
            <div class="w-0.72rem">用水临界预警</div>
          </div>
          <div class="float-left ml-0.16rem flex flex-row h-0.17rem">
            <div class="h-0.17rem w-0.17rem"
              :style="{ background: `url(${getImageUrl('reformation/type3.png')}) no-repeat center / 100% 100%` }">
            </div>
            <div class="w-0.72rem">用水超限预警</div>
          </div>
        </div>
        <SwiperList v-if="state.listData.length" :key="tableKey" :dataSource="state.listData" :columns="columns"
          class="mt-0.12rem" />
        <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
      </div>
    </div>
  </MyCard>
</template>

<script setup lang="tsx" name="Trend">
import MultiLineChart from './MultiLineChart.vue'
import { getUserWaterList, getBaseDistricts, getMonthWaterList } from '../services'
// import { waterSourceTypes } from '@/constants'
// import { getProjectDeviceList } from '../services'

// import { Swiper, SwiperSlide } from 'swiper/vue'

// import { Navigation, Pagination, A11y, Autoplay } from 'swiper/modules'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/autoplay'

const state = reactive({
  year: new Date().getFullYear(),
  districtCode: null,
  districtList: [],
  lineChartData: {},
  listData: [],
  yearList: []
})

// 初始化年份列表
const generateYearList = () => {
  state.year = new Date().getFullYear()
  const startYear = 2023 // 生成从当前年份到前6年的年份数组
  for (let year = state.year; year >= startYear; year--) {
    state.yearList.push({ label: String(year), value: String(year) })
  }
}
generateYearList()
// 获取行政区划
getBaseDistricts().then(res => {
  state.districtList = res?.data ? res.data.map(ele => {
    return { label: ele.districtName, value: ele.districtCode }
  }) : []
  state.districtCode = state.districtList[0]?.value
})

watch(
  () => [state.year, state.districtCode],
  (newVal) => {
    const [newYear, newDistrictCode] = newVal;
    if (newVal) {
      // 获取用水趋势
      getUserWaterList({ year: newYear, districtCode: newDistrictCode }).then(res => {
        state.listData = res?.data ? res?.data.map(item => {
          return {
            name: item.useWaterName, // 用水户名名称
            district: item.districtName, // 行政区划
            allowWaterValue: +item.waterWithdrawal, // 许可用水量
            waterValue: +item.waterValue, // 许可用水量
          }
        }) : []
      })
      // 获取用水趋势折线图
      getMonthWaterList({ year: newYear, districtCode: newDistrictCode }).then(res => {
        let tempOdj = {}
        let temp1 = []
        let temp2 = []
        tempOdj["name"]  = res?.data.length ? res.data[0].districtName : ''
        if (res?.data.length) {
          res?.data[0].monthWaters.forEach(ele => {
            temp1.push( +ele.waterValue)  // "+" 用于将字符串转换为数字，确保数据类型正确，例如："10" 转换为 10
            temp2.push(ele.dateTime ? ele.dateTime : '-' )
          }) 
        }
        tempOdj["listData"] = temp1
        tempOdj["labelData"] = temp2
        state.lineChartData = tempOdj
      })
    }
  },
  { immediate: true },
)
const tableKey = ref(0)
const columns = [
  {
    title: '用水户名称',
    width: '1.7rem',
    align: 'left',
    render: (row, idx) => <n-ellipsis>{row.name}</n-ellipsis>,
  },
  {
    title: '行政区划',
    width: '1.22rem',
    align: 'left',
    render: (row, idx) => (
      // <NSpace>{row.projectType == 1 ? <NTag type='success'>分水闸</NTag> : <NTag type='info'>排水闸</NTag>}</NSpace>
      <NSpace>{row.district}</NSpace>
    ),
    // return row.isOpen === 1 ? <NTag type="success">开启</NTag> : <NTag type="error">关闭</NTag>
  },
  {
    title: '用水量(万m³)',
    width: '1rem',
    align: 'left',
    render: (row, idx) => <div class='flex '>{renderLevel(row)}</div>,
  },
]
const renderLevel = (row: Object | null) => {
  console.log("row = ",row)
  if (row?.waterValue < 0) {
    return '-'
  }
  if (row?.waterValue <= row?.allowWaterValue * 0.9) {
    return <div class="flex flex-row"><img src={getImageUrl('reformation/type1.png')} alt={"暂无"} class='w-0.15rem h-0.15rem mt-0.05rem' /> &nbsp;<div class="w-0.45rem h-0.15rem font-[350] mt-0.05rem">{row?.waterValue}</div></div>
  } else if (row?.waterValue <= row?.allowWaterValue) {
    return <div class="flex flex-row"><img src={getImageUrl('reformation/type2.png')} alt={"暂无"} class='w-0.15rem h-0.15rem mt-0.05rem' /> &nbsp;<div class="w-0.45rem h-0.15rem font-[350]  mt-0.05rem">{row?.waterValue}</div></div>
  } else {
    return <div class="flex flex-row"><img src={getImageUrl('reformation/type3.png')} alt={"暂无"} class='w-0.15rem h-0.15rem mt-0.05rem' /> &nbsp;<div class="w-0.45rem h-0.15rem font-[350]  mt-0.05rem">{row?.waterValue}</div></div>
  }
}


</script>
<style lang="scss" scoped>
.content {
  opacity: 1;
  background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
  // background: red;
  box-sizing: border-box;
  border: 1px solid rgba(54, 96, 103, 0.5);
  z-index: 999;
}
</style>