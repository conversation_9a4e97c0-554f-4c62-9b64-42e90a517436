1. 灌区用水户信息统计，数据内容及格式
```json
[
  {
    "name": "修山镇",
    "value": "1000"
  }......
]
```
2. 用水量趋势图
2.1 灌区用水量趋势图(1 - 12月份)，数据内容及格式
```json
[
  {
    "name": "修山镇",
    "value": [100, 200, 300, 400, 500, 600, 700, 800, 900, 1000, 1100, 1200],
  }......
]
```
2.2 灌区用水户用水量列表，数据内容及格式
```json
[
  {
   name: 'XXX用水户1',
      district: "修山镇",
      value: 100,
  } ......    
]
```

3. 农业用水实时监测
3.1 监测区域列表，数据内容及格式
```json
[
 { label: '鸬鹚渡镇' }......
]
```
3.2 监测区域监测站实时数据，数据内容及格式
```json
[
  {
    "name": 'N1号测站GGQ01',
    "time": '2024-08-01 12:00:00',
    "flowKey": '瞬时流量',
    "flowValue": '0.105m3/s',
    "riverKey": '瞬时河道水位',
    "riverValue": '0.105m',
    "electricKey": '电源电压',
    "electricValue": '13.6V',
  }
  ......
]
```
4. 异常用水预警列表，数据内容及格式
```json
[
  {
    "taskStatus": 1,
    "taskName": '测站名称XXX - XXX',
    "stationTime": '2024-10-11 10:00:00',  // 巡检任务编码
    "stationArea": '鸬鹚渡镇', // 巡检范围
    "stationCode": 'YSDD-1070-2024063001', // 巡检人
    "stationContent": '用水量已达许可值90%', // 计划开始时间
  }
  ......
]
```