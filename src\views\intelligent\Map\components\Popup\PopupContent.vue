<template>
  <div
    class="relative"
    :class="
      state.detail?.map_class == 'EL'
        ? 'w-3rem h-1.74rem'
        : state.detail?.map_class == 'ZQ'
          ? 'w-3rem h-2.99rem'
          : 'w-4.5rem h-2.5rem'
    "
  >
    <!-- <div class="absolute w-full h-[calc(100%-0.17rem)] z--1" style="backdrop-filter: blur(0.1rem)">666</div> -->
    <!-- :style="{
        background: `url(${getImageUrl(`popup-bg.png`)}) no-repeat center / 100% 100%`,
      }" -->
    <!-- 安全站 EL-->
    <div class="content size-full flex-col" v-if="state.detail?.map_class == 'EL'">
      <!-- flex-center-between -->
      <div class="pl-0.14rem pr-0.08rem uno-bg_modal-header.png relative">
        <span class="text-0.18rem text-[#fff] font-normal lh-normal">{{ attrs.object_name }}</span>

        <!-- <MyIcon name="close" class="text-0.2rem cursor-pointer z-9999" @click="() => attrs.onPopupClose()" /> -->
        <span class="absolute right-0.1rem text-0.18rem c-#fff font-500 cursor-pointer" @click="() => attrs.onPopupClose()">
          <MyIcon class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer" />
        </span>
      </div>

      <div class="mx-0.14rem mt-0.1rem mb-0.06rem overflow-hidden flex-1 flex gap-0.1rem">
        <!-- object_id:{{ attrs.object_id }} object_name:{{ attrs.detailInfo }} -->
        <div class="flex-1 text-0.14rem font-bold text-[#fff] flex flex-col pt-0.06rem">
          <!-- v-show="state.SSData.length > 0" bg-[rgba(104, 150, 173, 1)]-->
          <div class="px-0.1rem flex-center-between w-2.68rem h-0.28rem uno-bg_model/el-bg.png">
            <span>X轴位移(mm)：</span>
            <span class="text-[#09FCC7]">{{ state.ELInfo?.XValue ? state.ELInfo?.XValue : '-' }}</span>
          </div>
          <div class="px-0.1rem flex-center-between w-2.68rem h-0.28rem uno-bg_model/el-bg.png mt-0.05rem">
            <span>Y轴位移(mm)：</span>
            <span class="text-[#09FCC7]">{{ state.ELInfo?.YValue ? state.ELInfo?.YValue : '-' }}</span>
          </div>
          <div class="px-0.1rem flex-center-between w-2.68rem h-0.28rem uno-bg_model/el-bg.png mt-0.05rem">
            <span>Z轴位移(mm)：</span>
            <span class="text-[#09FCC7]">{{ state.ELInfo?.ZValue ? state.ELInfo?.ZValue : '-' }}</span>
          </div>
          <div class="h-0.28rem w-[full] text-0.12rem ml-0.1rem mt-0.06rem">
            监测时间：{{ state.ELInfo?.time ? state.ELInfo?.time : dayjs().format('YYYY-MM-DD HH:00') }}
          </div>
        </div>
      </div>
    </div>
    <!-- 雨水情 ZQ-->
    <div class="content size-full flex-col w-5rem h-3rem" v-if="state.detail?.map_class == 'ZQ'">
      <div class="flex-center-between pl-0.14rem pr-0.08rem uno-bg_modal-header.png relative h-0.32rem">
        <span class="font-bold text-0.15rem text-[#fff]">{{ attrs.object_name }}</span>
        <span
          class="text-0.18rem c-#fff font-500 absolute right-0rem cursor-pointer w-0.26rem h-0.26rem pl-0.06rem"
          @click="() => attrs.onPopupClose()"
        >
          <MyIcon class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer" />
        </span>

        <div class="w-1.74rem h-0.24rem absolute right-0.3rem top-0.04rem flex">
          <ScaleBox :style="{ width: '68px', height: '24px', left: '8px', display: 'flex', zIndex: 999 }">
            <div
              class="w-68px h-24px cursor-pointer"
              :class="state.showVideo ? 'uno-bg_model/video-active.png' : 'uno-bg_model/video.png'"
              @click="videoClick()"
            ></div>
          </ScaleBox>

          <NaSelect
            class="w-0.89rem h-0.24rem ml-0.84rem"
            v-model:value="day"
            type="side"
            :show-checkmark="false"
            :options="state.dayList"
          >
            <template #arrow>
              <MyIcon
                class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
              />
            </template>
          </NaSelect>
        </div>
      </div>

      <div class="mx-0.14rem mt-0.1rem mb-0.14rem overflow-auto flex-1 flex-col gap-0.1rem">
        <!-- object_id:{{ attrs.object_id }} object_name:{{ attrs.detailInfo }} -->
        <!-- v-show="state.list?.length && state.list !== null"  -->
        <WaterRainChart :dataSource="state.chartData" />
      </div>
    </div>
    <!-- 量测水 SL-->
    <div
      class="content size-full flex-col w-5.1rem h-2.5rem"
      v-if="state.detail?.map_class == 'SL' || state.detail?.map_class == 'SLJC'"
    >
      <div class="flex-center-between pl-0.14rem pr-0.08rem uno-bg_modal-header.png relative h-0.32rem">
        <span class="font-bold text-0.15rem text-[#fff]">{{ attrs.object_name }}</span>
        <span
          class="text-0.18rem c-#fff font-500 absolute right-0rem cursor-pointer w-0.26rem h-0.26rem pl-0.06rem"
          @click="() => attrs.onPopupClose()"
        >
          <MyIcon class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer" />
        </span>
        <!-- <span class="text-0.18rem c-#fff font-500 ml-auto mr-0.1rem cursor-pointer" @click="close">×</span> -->
        <!-- <MyIcon name="close" class="text-0.2rem cursor-pointer z-9999" @click="() => attrs.onPopupClose()" /> -->
        <div class="w-1.74rem h-0.24rem absolute right-0.3rem top-0.04rem flex">
          <ScaleBox :style="{ width: '68px', height: '24px', left: '8px', display: 'flex', zIndex: 999 }">
            <div
              v-if="state.videoList.length > 0"
              class="w-68px h-24px cursor-pointer"
              :class="state.showVideo ? 'uno-bg_model/video-active.png' : 'uno-bg_model/video.png'"
              @click="videoClick()"
            ></div>
          </ScaleBox>
          <NaSelect
            class="w-0.89rem h-0.24rem ml-0.84rem"
            v-model:value="day"
            type="side"
            :show-checkmark="false"
            :options="state.dayList"
          >
            <template #arrow>
              <MyIcon
                class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
              />
            </template>
          </NaSelect>
        </div>

        <!-- <ScaleBox :style="{ width: '1.76rem', height: '0.2rem', right: '-0.5rem', top: '1px', display: 'flex', zIndex: 999 }">
          <div class="w-0.68rem h-0.24rem mr-0.08rem uno-bg_model/video.png cursor-pointer" @click="videoClick()"></div>
          
        </ScaleBox> -->
      </div>

      <div class="mx-0.14rem mt-0.1rem mb-0.14rem overflow-auto flex-1 flex-col gap-0.1rem">
        <ProcessChart v-if="!!state.list" :dataSource="state.data" />
        <n-empty v-else class="pt-0.6rem" description="暂无数据" />
      </div>
    </div>
    <!-- 墒情 SS-->
    <div class="content size-full flex-col" v-if="state.detail?.map_class == 'SS'">
      <!-- flex-center-between -->
      <div class="pl-0.14rem pr-0.08rem uno-bg_modal-header.png relative">
        <span class="text-0.18rem text-[#fff] font-normal lh-normal">{{ attrs.object_name }}</span>
        <span class="text-0.12rem font-350 text-[#fff] ml-0.1rem">
          监测时间： {{ dayjs().format('YYYY-MM-DD HH:mm:ss') }}
          <!-- {{ state.SSData?.filter(item => item.unit === '%')?.[0]?.dateTime || dayjs().format('YYYY.MM.DD HH:mm:ss') }} -->
        </span>
        <!-- <MyIcon name="close" class="text-0.2rem cursor-pointer z-9999" @click="() => attrs.onPopupClose()" /> -->
        <span class="absolute right-0.1rem text-0.18rem c-#fff font-500 cursor-pointer" @click="() => attrs.onPopupClose()">
          <MyIcon class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer" />
        </span>
      </div>

      <div class="mx-0.14rem mt-0.1rem mb-0.14rem overflow-auto flex-1 flex gap-0.1rem">
        <!-- object_id:{{ attrs.object_id }} object_name:{{ attrs.detailInfo }} -->
        <div class="flex-1 text-0.14rem font-bold text-[#fff] flex flex-col uno-bg_model/soil-moisture-bg.png pt-0.1rem">
          <div class="h-0.48rem w-0.48rem uno-bg_model/soil-moisture.png mx-auto"></div>
          <div
            v-show="state.SSData.length > 0"
            class="flex mx-0.16rem flex-center-between mt-0.1rem"
            v-for="(item, index) in state.SSData?.filter(item => item.unit === '%')"
            :key="index"
          >
            <span>{{ item?.fieldName }}：</span>
            <span class="text-[#09FCC7]">{{ item?.fieldValue || '-' }}{{ item?.unit }}</span>
          </div>
          <n-empty v-show="state.SSData.length == 0" class="pt-0.2rem" description="暂无数据" />
        </div>
        <div class="flex-1 text-0.14rem font-bold text-[#fff] flex flex-col uno-bg_model/soil-temperature-bg.png pt-0.1rem">
          <div class="h-0.48rem w-0.48rem uno-bg_model/soil-temperature.png mx-auto"></div>
          <div
            v-show="state.SSData.length > 0"
            class="flex mx-0.16rem flex-center-between mt-0.1rem"
            v-for="(item, index) in state.SSData?.filter(item => item.unit === '℃')"
            :key="index"
          >
            <span>{{ item?.fieldName }}：</span>
            <span class="text-[#F6CC57]">{{ item?.fieldValue || '-' }}{{ item?.unit }}</span>
          </div>
          <n-empty v-show="state.SSData.length == 0" class="pt-0.2rem" description="暂无数据" />
        </div>
      </div>
    </div>
    <!-- 流量 FL-->
    <div class="content size-full flex-col w-5.2rem h-2.5rem" v-if="state.detail?.map_class == 'FL'">
      <div class="flex-center-between pl-0.14rem pr-0.08rem uno-bg_modal-header.png relative">
        <span class="font-bold text-0.15rem text-[#fff]">{{ attrs.object_name }}</span>

        <span class="text-0.18rem c-#fff font-500 ml-auto mr-0.1rem cursor-pointer" @click="() => attrs.onPopupClose()">
          <MyIcon class="i-material-symbols:cancel-outline-rounded text-0.18rem cursor-pointer" />
        </span>
        <!-- <MyIcon name="close" class="text-0.2rem cursor-pointer z-9999" @click="() => attrs.onPopupClose()" /> -->

        <ScaleBox :style="{ width: '1.76rem', height: '0.2rem', right: '0.1rem', top: '1px', display: 'flex', zIndex: 999 }">
          <div
            class="w-0.68rem h-0.24rem mr-0.08rem cursor-pointer"
            :class="state.showVideo ? 'uno-bg_model/video-active.png' : 'uno-bg_model/video.png'"
            @click="videoClick()"
          ></div>
          <NaSelect style="width: 0.92rem" v-model:value="day" type="side" :show-checkmark="false" :options="state.dayList">
            <template #arrow>
              <MyIcon
                class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
              />
            </template>
          </NaSelect>
        </ScaleBox>
      </div>

      <div class="mx-0.14rem mt-0.1rem mb-0.14rem overflow-auto flex-1 flex-col gap-0.1rem">
        <!-- object_id:{{ attrs.object_id }} object_name:{{ attrs.detailInfo }} -->
        <div class="voltage flex flex-center-between">
          <img class="w-0.2rem h-0.2rem ml-0.06rem" :src="getImageUrl('model/voltage.png')" alt="Dynamic Image" />

          <span class="text-0.12rem text-[#DEF2FC] ml-0.06rem h-0.25rem flex-1">
            设备电压值：
            <span class="text-[#09C8FC]">12</span>
            V
          </span>
          <span class="text-0.12rem text-[#B2DAEA] ml-0.06rem flex-1">更新时间：20245-01-05 12:00:00</span>
        </div>

        <LineChart class="mt-0.15rem" :dataSource="state.data" />
      </div>
    </div>
    <!-- 雨水情视频 -->
    <div
      class="absolute right--5.5rem top-0 z-999999999 w-3.5rem h-3rem"
      v-if="state.detail?.map_class == 'ZQ' && state.showVideo"
    >
      <div class="absolute right--0.9rem top-0.1rem z-999 w-2rem h-0.2rem flex">
        <NaSelect style="width: 0.92rem" v-model:value="video" type="side" :show-checkmark="false" :options="state.videoList">
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
      </div>
      <!-- <ScaleBox :style="{ width: '2rem', height: '0.2rem', right: '-1.2rem', top: '0.1rem', display: 'flex', zIndex: 999 }">
        
      </ScaleBox> -->
      <videoEasyPlayer class="w-2.66rem h-1.94rem" :channel="1" :device="1" :url="state?.url"></videoEasyPlayer>
    </div>
    <!-- 量测水视频 -->
    <div
      class="absolute right--3.6rem top-0 z-999999999 w-3rem h-2.5rem"
      v-if="(state.detail?.map_class == 'SL' || state.detail?.map_class == 'SLJC') && state.showVideo"
    >
      <div class="absolute right--0.9rem top-0.1rem z-999 w-2rem h-0.2rem flex">
        <NaSelect style="width: 0.92rem" v-model:value="video" type="side" :show-checkmark="false" :options="state.videoList">
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
      </div>
      <videoEasyPlayer class="w-2.58rem h-1.94rem" :channel="1" :device="1" :url="state?.url"></videoEasyPlayer>
    </div>
    <!-- 流量水视频 -->
    <div
      class="absolute right--3.48rem top-0 z-999999999 w-3rem h-2.5rem"
      v-if="state.detail?.map_class == 'FL' && state.showVideo"
    >
      <ScaleBox :style="{ width: '200px', height: '20px', right: '-120px', top: '10px', display: 'flex', zIndex: 999 }">
        <NaSelect style="width: 0.92rem" v-model:value="video" type="side" :show-checkmark="false" :options="state.videoList">
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
      </ScaleBox>
      <videoEasyPlayer class="w-2.58rem h-1.94rem" :channel="1" :device="1" :url="state?.url"></videoEasyPlayer>
    </div>
  </div>
</template>
<script setup lang="tsx" name="PopupContent">
  import { useAppStore } from '@/store'
  import { getWaterRainList, listByIds, getCameraList } from '@/api'
  import LineChart from '../Chart/LineChart.vue'
  import ProcessChart from '../Chart/ProcessChart.vue'
  import WaterRainChart from '../Chart/WaterRainChart.vue'
  import dayjs from 'dayjs'
  // import videoEasyPlayer from '@/components/videoEasyPlayer/index.vue'
  import videoEasyPlayer from '@/components/videoEasyPlayer/site.vue'
  import { twoDecimalFilter } from '@/utils/dealNumber.js'

  const state = reactive({
    url: null,
    ELInfo: { XValue: 0, YValue: 0, ZValue: 0, time: '' },
    data: [],
    detail: {},
    SSData: [],
    ELData: [],
    list: [],
    chartData: [],
    dayList: [
      { label: '今天', value: 1 },
      { label: '近三天', value: 2 },
      // { label: '近七天', value: 3 },
    ],
    videoList: [],
    showVideo: false,
  })
  const day = ref(1)
  const video = ref(null)
  const attrs = useAttrs()
  const appStore = useAppStore()
  state.detail = attrs.detailInfo

  onMounted(() => {
    init()
  })
  watch(
    () => day.value,
    newVal => {
      if (newVal) {
        init()
      }
    },
    { deep: true },
  )
  watch(
    () => video.value,
    newVal => {
      state.url = state.videoList?.find(el => el.value == newVal)?.streamUrl
    },
  )
  const videoClick = () => {
    state.showVideo = !state.showVideo
  }
  const init = () => {
    // state.detail?.map_class MS监测站点 HP水利工程
    let cameraPram = { siteId: state.detail?.object_id, objectType: 'MS' }
    getCameraList(cameraPram).then(res => {
      let videoList = res.data?.length != 0 ? res.data : state.videoList
      state.videoList = videoList?.map(item => ({
        value: item.cameraId.toString(), // 将 cameraId 转化为字符串形式的 value
        label: item.cameraName, // 将 cameraName 转化为 label
        ...item, // 保留原对象的所有属性
      }))
      state.url = state.videoList[0]?.streamUrl
      video.value = state.videoList[0]?.value
    })
    if (state.detail?.map_class == 'SS') {
      let pram = [
        {
          objectId: state.detail?.object_id, //88, //state.detail?.object_id,
          objectType: 'MS',
        },
      ]
      listByIds(pram).then(res => {
        // const list = res1.filter(item => item.objectCategoryCode !== 'HP001')
        if (!res.data[0].indexFields || res.data[0].indexFields.length == 0) {
          state.SSData = []
        }
        state.SSData = res.data[0].indexFields?.map(item => {
          let depth
          if (item.fieldName.includes('湿度')) {
            depth = item.fieldName.match(/\d+/)[0] // 提取数字部分作为深度
            item.fieldName = `土壤湿度(${depth}0cm)` // 转换后的字段名
          } else if (item.fieldName.includes('温度')) {
            depth = item.fieldName.match(/\d+/)[0]
            item.fieldName = `土壤温度(${depth}0cm)`
          }
          return item
        })
      })
    } else if (state.detail?.map_class == 'ZQ') {
      let params = { siteId: state.detail?.object_id, time: day.value }
      getWaterRainList(params).then(res => {
        state.list = res.data
        if (state.list?.length == 0) {
          return
        }
        // state.data = { ...state.detail, chartData: res.data }
        const rainData = {
          name: '雨量',
          data: state.list?.map(el => [el.dateTime, el.rain]),
        }
        const sumRainData = {
          name: '累计雨量',
          data: state.list?.map(el => [el.dateTime, el.sumRain]),
        }
        state.chartData = [
          rainData,
          sumRainData,
          {
            name: '水位',
            data: state.list?.map(el => [el.dateTime, el.waterLevel]),
          },
        ]
      })
    } else if (state.detail?.map_class == 'EL') {
      let pram = [
        {
          objectId: state.detail?.object_id, //88, //state.detail?.object_id,
          objectType: 'MS',
        },
      ]
      listByIds(pram).then(res => {
        // const list = res1.filter(item => item.objectCategoryCode !== 'HP001')

        if (!res.data[0].indexFields || res.data[0].indexFields.length == 0) {
          state.ELData = []
          state.ELInfo = {
            XValue: null,
            YValue: null,
            ZValue: null,
            time: dayjs().format('YYYY-MM-DD HH:mm'),
          }
        }
        state.ELData = res.data[0].indexFields
        state.ELInfo = {
          XValue: state.ELData?.find(item => item.indexCode == 'xDisplacement')?.fieldValue || null,
          YValue: state.ELData?.find(item => item.indexCode == 'yDisplacement')?.fieldValue || null,
          ZValue: state.ELData?.find(item => item.indexCode == 'zDisplacement')?.fieldValue || null,
          time: state.ELData?.find(item => item.indexCode == 'xDisplacement')?.dateTime || null,
        }

        // state.ELData = res.data[0].indexFields?.map(item => {
        //   let depth
        //   if (item.fieldName?.includes('x轴位移')) {
        //     // depth = item.fieldName.match(/\d+/)[0] // 提取数字部分作为深度
        //     item.fieldName = `x轴位移:${item.fieldValue}` // 转换后的字段名
        //   } else if (item.fieldName?.includes('y轴位移')) {
        //     // depth = item.fieldName.match(/\d+/)[0]
        //     item.fieldName = `y轴位移:${item.fieldValue}`
        //   } else if (item.fieldName?.includes('z轴位移')) {
        //     // depth = item.fieldName.match(/\d+/)[0]
        //     item.fieldName = `z轴位移:${item.fieldValue}`
        //   }
        //   return item
        // })
        console.log('ELData 66666', state.ELData)
      })
    } else {
      let params = { siteId: state.detail?.object_id, time: day.value }
      getWaterRainList(params).then(res => {
        state.list = res.data
        state.data = { ...state.detail, chartData: res.data }
      })
    }
  }
</script>
<style lang="scss" scoped>
  .content {
    border-radius: 0.08rem;
    opacity: 1;

    background: linear-gradient(180deg, rgba(12, 32, 41, 0.8) 0%, rgba(31, 83, 106, 0.8) 100%);

    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%);
  }
  .voltage {
    width: 98.5%; //3.98rem;
    height: 0.25rem;
    border-radius: 0.02rem;
    opacity: 1;

    background: linear-gradient(1deg, #15799b -19%, rgba(21, 111, 141, 0) 91%);

    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.21) 99%) 1;
  }
  :deep(.my-custom-naive-select-side.n-select .n-base-selection-input__content) {
    // background: green !important;
    width: 0.8rem;
  }
  :deep(.n-base-selection .n-base-suffix .n-base-suffix__arrow) {
    // background: red !important;
    margin-left: 0.2rem;
  }
</style>
