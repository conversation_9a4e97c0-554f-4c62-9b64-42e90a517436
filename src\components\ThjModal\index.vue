<template>
  <n-modal
    v-if="isRender"
    v-model:show="show"
    class="modal-box"
    :style="{ width: modalOptions.width, ...modalOptions.modalStyle }"
    :content-class="`${modalOptions.contentClass}`"
    :preset="undefined"
    size="huge"
    :bordered="false"
    @after-leave="onAfterLeave"
    v-bind="modalOptions"
  >
    <n-spin :show="modalOptions.loading" content-class="h-full">
      <n-card class="relative" :style="modalOptions.contentStyle" :closable="modalOptions.closable" @close="close()">
        <div class="backdrop-filter-card absolute w-full h-full"></div>
        <i
          class="w-1.64rem h-1.64rem absolute top--0.06rem left--0.1rem block uno-bg_/thj-modal-line.png z-2 pointer-events-none"
        ></i>
        <i
          class="w-1.64rem h-1.69rem absolute top--0.1rem right--0.11rem rotate-(y-180) block uno-bg_/thj-modal-line.png z-2 pointer-events-none"
        ></i>
        <i
          class="w-1.64rem h-1.64rem absolute bottom--0.08rem left--0.12rem rotate-(x-180) block uno-bg_/thj-modal-line.png z-2 pointer-events-none"
        ></i>
        <i
          class="w-1.64rem h-1.64rem absolute bottom--0.05rem right--0.16rem rotate-180 block uno-bg_/thj-modal-line.png z-2 pointer-events-none"
        ></i>

        <i class="w-0.26rem h-1.3rem absolute top-1.43rem left-0 block uno-bg_/thj-modal-dot.png z-2 pointer-events-none dot"></i>
        <i
          class="w-0.26rem h-1.3rem absolute top-1.43rem right-0 rotate-(y-180) block uno-bg_/thj-modal-dot.png z-2 pointer-events-none dot"
        ></i>
        <i
          class="w-0.26rem h-1.3rem absolute bottom-1.43rem left-0 rotate-(x-180) block uno-bg_/thj-modal-dot.png z-2 pointer-events-none dot"
        ></i>
        <i
          class="w-0.26rem h-1.3rem absolute bottom-1.43rem right-0 rotate-180 block uno-bg_/thj-modal-dot.png z-2 pointer-events-none dot"
        ></i>

        <template #header>
          <slot v-if="$slots.header" name="header" />
          <header v-else class="modal-header w-10.53rem h-0.74rem ml-20.3% relative">
            <i class="w-3.2rem h-0.46rem absolute top-0.03rem left-0 block uno-bg_/thj-modal-header-bg-l.png z-3"></i>
            <h2 class="w-8.3rem header-title m-auto uno-bg_/thj-modal-header-bg.png static z-8">{{ modalOptions.title }}</h2>
            <i
              class="w-3.2rem h-0.46rem absolute top-0 right--0.08rem rotate-(y-180) block uno-bg_/thj-modal-header-bg-l.png z-3"
            ></i>
          </header>
        </template>

        <slot></slot>

        <!-- 底部按钮 -->
        <template #footer>
          <slot v-if="$slots.footer" name="footer" />
          <footer v-else-if="modalOptions.showFooter" class="flex justify-end">
            <n-button v-if="modalOptions.showCancel" @click="handleCancel()">
              {{ modalOptions.cancelText }}
            </n-button>
            <n-button
              v-if="modalOptions.showOk"
              type="primary"
              :loading="modalOptions.okLoading"
              class="ml-20"
              @click="handleOk()"
            >
              {{ modalOptions.okText }}
            </n-button>
          </footer>
        </template>
      </n-card>
    </n-spin>
  </n-modal>
</template>

<script setup>
  import { initDrag } from './utils'
  import { getCurrentInstance } from 'vue'

  const instance = getCurrentInstance()
  const slots = useSlots()
  const attrs = useAttrs()
  const props = defineProps({
    width: {
      type: String,
      // default: '800px',
    },
    title: {
      type: String,
      default: '',
    },
    closable: {
      type: Boolean,
      default: true,
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    okText: {
      type: String,
      default: '确定',
    },
    showFooter: {
      type: Boolean,
      default: true,
    },
    showCancel: {
      type: Boolean,
      default: true,
    },
    showOk: {
      type: Boolean,
      default: true,
    },
    modalStyle: {
      type: Object,
      default: () => {},
    },
    contentStyle: {
      type: Object,
      default: () => {},
    },
    onOk: {
      type: Function,
      default: () => {},
    },
    onClose: {
      type: Function,
      default: () => {},
    },
    draggable: {
      type: Boolean,
      default: false,
    },
  })
  const isRender = ref(false)

  // 声明一个show变量，用于控制模态框的显示与隐藏
  const show = ref(false)
  // 声明一个modalOptions变量，用于存储模态框的配置信息
  const modalOptions = ref({})

  const title = computed({
    get() {
      return modalOptions.value.title
    },
    set(v) {
      modalOptions.value.title = v
    },
  })

  const loading = computed({
    get() {
      return !!modalOptions.value?.loading
    },
    set(v) {
      if (modalOptions.value) {
        modalOptions.value.loading = v
      }
    },
  })

  const okLoading = computed({
    get() {
      return !!modalOptions.value?.okLoading
    },
    set(v) {
      if (modalOptions.value) {
        modalOptions.value.okLoading = v
      }
    },
  })

  // 打开模态框
  async function open(options = {}) {
    isRender.value = open
    await nextTick()
    // 将props和options合并赋值给modalOptions
    modalOptions.value = { ...attrs, ...props, ...options }
    if (!modalOptions.value?.loading) {
      modalOptions.value.loading = false
    }

    // 将show的值设置为true
    show.value = true
    await nextTick()
    props.draggable &&
      initDrag(
        Array.prototype.at.call(document.querySelectorAll('.modal-header'), -1),
        Array.prototype.at.call(document.querySelectorAll('.modal-box'), -1),
      )
  }

  // 定义一个close函数，用于关闭模态框
  function close() {
    show.value = false
  }

  // 定义一个handleOk函数，用于处理模态框确定操作
  async function handleOk(data) {
    // 如果modalOptions中没有onOk函数，则直接关闭模态框
    if (typeof modalOptions.value.onOk !== 'function') {
      return close()
    }
    try {
      // 调用onOk函数，传入data参数
      const res = await modalOptions.value.onOk(data)
      // 如果onOk函数的返回值不为false，则关闭模态框
      // res !== false && close()
    } catch (error) {
      okLoading.value = false
      console.error(error)
    }
  }

  // 定义一个handleCancel函数，用于处理模态框取消操作
  async function handleCancel(data) {
    close()
  }

  async function onAfterLeave() {
    close()
    await nextTick()
    if (typeof modalOptions.value.onClose === 'function') {
      modalOptions.value.onClose()
    }
    await nextTick()
    isRender.value = false
    props.draggable &&
      initDrag(
        Array.prototype.at.call(document.querySelectorAll('.modal-header'), -1),
        Array.prototype.at.call(document.querySelectorAll('.modal-box'), -1),
      )
  }

  // 定义一个defineExpose函数，用于暴露open、close、handleOk、handleCancel函数
  defineExpose({
    open,
    close,
    handleOk,
    handleCancel,
    title,
    loading,
    okLoading,
    options: modalOptions,
  })
</script>

<style lang="scss" scoped>
  :deep(.n-card > .n-card-header) {
    padding: 0;
  }
  .header-title {
    font-family: KwangLiangWine;
    font-size: 0.36rem;
    text-align: center;
    font-weight: normal;
    line-height: 0.76rem;
    letter-spacing: 0.05em;
    text-shadow: 0px 4px 10px #229081;
  }
  :deep(.n-card > .n-card-header .n-card-header__close) {
    font-size: 0.26rem;
    font-family: bold;
    margin-right: 0.4rem;
    color: #fff;
  }
  :deep(.n-card) {
    border-radius: 16px;
    position: relative;
    background: url('@/assets/images/thj-modal-bg.png') no-repeat center / 100% 100%;
  }
  .backdrop-filter-card {
    backdrop-filter: blur(10px);
  }
  :deep(.n-card.n-card--bordered) {
    border: none;
  }
  :deep(.n-card__content) {
    // overflow: auto;
    overflow: hidden;
  }

  :deep(.n-spin-content--spinning) {
    position: relative;
    opacity: 1 !important;
    &::before {
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      background-color: rgba(200, 200, 200, 0.4);
      z-index: 1;
    }
  }
  .dot {
    animation: fadeIn 2s infinite;
  }
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
</style>

<style lang="scss">
  .n-scrollbar-content.n-modal-scroll-content > .n-spin-container.n-modal.modal-box {
    border-radius: 16px;
    overflow: hidden;
  }
</style>
