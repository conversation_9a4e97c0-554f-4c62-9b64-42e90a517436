<template>
  <ScaleBox :style="{ width: '190px', height: '190px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="MultiPieChart">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()

  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  const serviceStatusColor = ['#FF4E4B', '#20E5CB']
  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )
  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    return {
      backgroundColor: 'rgba(202, 227, 254, 0)',
      tooltip: {
        show: false,
        trigger: 'item',
        // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
        // textStyle: { fontSize: 12, color: '#fff' },
        // borderWidth: 0,
        // axisPointer: {
        //   type: 'cross',
        //   label: {
        //     backgroundColor: '#6a7985',
        //   },
        // },
        // trigger: 'axis',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        formatter: '{b}: {c}个',
      },
      // legend: {
      //   top: '5%',
      //   left: 'center',
      // },
      series: [
        {
          name: '设备占比',
          type: 'pie',
          radius: ['74%', '84%'],
          showEmptyCircle: true,
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          itemStyle: {
            //borderColor: '#C6E7FF', // 设置环形边框颜色
            borderWidth: 1, // 设置边框宽度
            color: 'rgba(255, 255, 255, 0.3)', // 设置环状背景色，这里设置为半透明的白色
          },
          data: data,
          // data: [
          //   { value: 1048, name: 'Search Engine' },
          //   { value: 735, name: 'Direct' },
          //   { value: 580, name: 'Email' },
          //   { value: 484, name: 'Union Ads' },
          //   { value: 300, name: 'Video Ads' },
          // ],
        },
      ],
    }
  }
</script>

<style lang="scss" scoped></style>
