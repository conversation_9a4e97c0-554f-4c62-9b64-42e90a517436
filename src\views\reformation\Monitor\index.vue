<template>
  <MyCard name="农业用水实时监测">
    <!-- mb-0.2rem -->
    <div class="content h-4.4rem">
      <div class="flex flex-col h-[100%] w-[100%] relative">
        <div
          class="select-items relative flex overflow-x-auto whitespace-nowrap items-center h-0.5rem w-[93%] mt-0.06rem ml-0.16rem pr-0.16rem"
        >
          <div
            v-for="(item, key) in rainData"
            :key="key"
            class="h-0.35rem w-1rem px-0.15rem text-0.15rem font-350 flex items-center justify-center mt-0.01rem mr-0.1rem cursor-pointer"
            :style="{
              background:
                currentNum == key
                  ? `url(${getImageUrl('right/content-btn2.png')}) no-repeat center / 100% 100%`
                  : `url(${getImageUrl('right/content-btn1.png')}) no-repeat center / 100% 100%`,
              color: currentNum == key ? '#09C8FC' : '#fff',
            }"
            @click="handleRainClick(item.label, key)"
          >
            {{ item.label }}
          </div>
        </div>
        <div class="data-list h-3.5rem mt-0.16rem ml-0.16rem mr-0.16re">
          <vue3-seamless-scroll :list="dataSource" class="data-scroll" hover wheel :key="1" :step="1" v-if="dataSource?.length">
            <div
              class="scroll-item relative mt-0.12rem text-0.14rem cursor-pointer"
              v-for="(el, index) in dataSource"
              :key="index"
              @click="selectSlide(index)"
            >
              <div class="w-100% h-0.7rem p-0.13rem bg-[rgba(255,255,255,0.05)] rounded-[0.05rem] overflow-hidden" :key="index">
                <div class="h-[100%] w-[100%] flex flex-row justify-between">
                  <div class="flex flex-col flex-7">
                    <div class="h-0.18rem text-[#B2DAEA]">{{ el.name }}</div>
                    <div class="info-item-dept h-0.2rem text-0.12rem text-[#B2DAEA] mt-0.08rem">
                      {{ el?.time||'-' }}
                    </div>
                  </div>
                  <div class="flex flex-col flex-4">
                    <div class="h-0.18rem text-[#B2DAEA]">{{ el.flowKey }}</div>
                    <div class="info-item-dept h-0.2rem text-0.14rem text-[#09C8FC] mt-0.08rem">
                      {{ el.flowValue }}
                    </div>
                  </div>
                  <div class="flex flex-col flex-5">
                    <div class="h-0.18rem text-[#B2DAEA]">{{ el.riverKey }}</div>
                    <div class="info-item-dept h-0.2rem text-0.14rem text-[#09C8FC] mt-0.08rem">
                      {{ el.riverValue }}
                    </div>
                  </div>
                  <div class="flex flex-col flex-1.5">
                    <div class="h-0.18rem text-[#B2DAEA]">{{ el.electricKey }}</div>
                    <div class="info-item-dept h-0.2rem text-0.14rem text-[#09C8FC] mt-0.08rem">
                      {{ el.electricValue  }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </vue3-seamless-scroll>
          <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
        </div>
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="DeviceInfo">
  import { thjDrainageStatuses, serviceColor, projectColor } from '@/constants'
  import { getRealUserList } from '../services'
  import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
  import { dealNumber } from '@/utils'

  const rainData = ref([])
  const currentNum = ref(0)
  const dataSource = ref([])
  let resDataAll = [] // 请求返回的数据
  const handleRainClick = (label, key) => {
    currentNum.value = key
    
    let target = resDataAll.filter(el => el.districtName == label)
    if (target.length && target[0].sites && target[0].sites.length) {
      console.log('get data',label, key,target[0].sites)
      dataSource.value = target[0].sites?.map(ele => {
        
        return {
          name: ele.siteName,
          time: ele.indexFields?.[0]?.dateTime,
          flowKey: '瞬时流量',
          flowValue: ele.indexFields?.[1] ?(ele.indexFields[1].fieldValue==null?'-': ele.indexFields[1].fieldValue + ele.indexFields[1].unit) : '-',
          riverKey: '瞬时河道水位',
          riverValue: ele.indexFields?.[0] ? (ele.indexFields[0].fieldValue==null?'-':ele.indexFields[0].fieldValue + ele.indexFields[0].unit) : '-',
          electricKey: '电源电压',
          electricValue: ele.indexFields?.[2] ? (ele.indexFields[2].fieldValue==null?'-':ele.indexFields[2].fieldValue + ele.indexFields[2].unit ): '-',
        }
      })
    } else {
      dataSource.value = [] // sites 为空时，清空dataSource
    }
  }
  onMounted(() => {
    getList()
  })
  const getList = () => {
    getRealUserList().then(res => {
      let newList=[]
      newList = res?.data
        ? res?.data
            ?.filter(item => item.sites?.length > 0) // 过滤掉 sites 为 null 的项
            ?.map((item, index) => {
              if (item.sites) {
                return { label: item.districtName, value: index }
              }
            })
        : []
      rainData.value=newList
      resDataAll = res?.data || []

      handleRainClick(newList[0].label, 0)
      
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }

  .select-items::-webkit-scrollbar {
    width: 10px;
    /* 横向滚动条的高度 */
    height: 0.03rem;
    /* 纵向滚动条的宽度 */
    background-color: #1f536a;
    /* 滚动条背景颜色 */
  }

  .select-items::-webkit-scrollbar:hover {
    height: 0.06rem;
    /* 纵向滚动条的宽度 */
  }

  .select-items::-webkit-scrollbar-thumb {
    background-color: #09c8fc;
    /* 滑块背景颜色 */
    border-radius: 0.03rem;
    /* 滑块圆角 */
  }

  .select-items::-webkit-scrollbar-track {
    background-color: #1f536a;
    /* 轨道背景颜色 */
    border-radius: 0.03rem;
    /* 轨道圆角 */
  }

  .select-items::-webkit-scrollbar-thumb:hover {
    background-color: #09c8fc;
    /* 滑块悬停时的背景颜色 */
  }

  .data-scroll {
    height: 100%;
    // background: red;
    overflow: hidden;

    .scroll-item {
      // margin-right: 0.2rem;
      width: 100%;
      height: 0.7rem;
      // background: url('@/assets/images/intelligent/chart.png') no-repeat;
      background-size: 100% 100%;
    }
  }
</style>
