<template>
  <div class="flex p-0.1rem uno-bg_/dispatch-info-bg.png">
    <div class="flex-1">
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">调度方案编号:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">{{ state.dispatchInfo?.caseCode }}</span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">调度方案名称:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">{{ state.dispatchInfo?.caseName }}</span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">来水预报方案:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">
          {{ state.waterFcstOptions.find(ele => ele.value == state.dispatchInfo?.waterFcstId)?.label || '--' }}
        </span>
      </div>
    </div>
    <div class="flex-1">
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">开始时间:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">{{ state.dispatchInfo?.startTime }}</span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">结束时间:&nbsp;</label>
        <span class="text-0.14rem c-#fff">{{ state.dispatchInfo?.endTime }}</span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">时段降雨总量:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">
          {{ state.dispatchInfo?.sumRainfall == null ? '--' : state.dispatchInfo?.sumRainfall + ' mm' }}
        </span>
      </div>
    </div>
    <div class="flex-1">
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">调度模式:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">
          {{ state.dispatchInfo?.dispathMode == 1 ? '水位控制调度' : '指令调度' }}
        </span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">调度类型:&nbsp;</label>
        <span class="text-0.14rem c-#fff text-overflow1">
          {{ state.dispatchInfo?.dispathType == 1 ? '流量调度' : '开度调度' }}
        </span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">起调水位:&nbsp;</label>
        <span class="text-0.14rem c-#fff">
          {{ state.dispatchInfo?.startWaterLevel == null ? '--' : state.dispatchInfo?.startWaterLevel + ' m' }}
        </span>
      </div>
    </div>
    <div class="flex-1">
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">末期水位:&nbsp;</label>
        <span class="text-0.14rem c-#fff">
          {{ state.dispatchInfo?.endWaterLevel == null ? '--' : state.dispatchInfo?.endWaterLevel + ' m' }}
        </span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">时段累计入库水量:&nbsp;</label>
        <span class="text-0.14rem c-#fff">
          {{ state.dispatchInfo?.sumInWater == null ? '--' : state.dispatchInfo?.sumInWater + ' 万m³' }}
        </span>
      </div>
      <div class="flex mb-0.06rem">
        <label class="text-0.14rem c-#B2DAEA">时段累计出库水量:&nbsp;</label>
        <span class="text-0.14rem c-#fff">
          {{ state.dispatchInfo?.sumOutWater == null ? '--' : state.dispatchInfo?.sumOutWater + ' 万m³' }}
        </span>
      </div>
    </div>
  </div>
  <div class="h-3.14rem mt-0.14rem">
    <DispatchChart
      v-show="state.dispatchInfo?.resvrDispResList?.length && state.dispatchInfo?.resvrDispResList !== null"
      :dataSource="state.chartData"
    />
    <n-empty
      v-if="state.dispatchInfo?.resvrDispResList == null || state.dispatchInfo?.resvrDispResList?.length == 0"
      class="pt-0.6rem"
      description="暂无数据"
    />
  </div>
  <DispatchTable :tableData="state.dispatchInfo?.resvrDispResList || []" :dispatchInfo="state.dispatchInfo" />
</template>
<script setup lang="ts" name="DispatchResult">
  import { getInWaterPage, getDispatchDetails } from '../services'

  import DispatchChart from './DispatchChart.vue'
  import DispatchTable from './DispatchTable.vue'

  const props = defineProps({
    dispatchId: {
      type: Number,
      default: undefined,
    },
  })

  watch(
    () => props.dispatchId,
    newVal => {
      if (newVal) {
        getInfo()
      }
    },
    { deep: true },
  )

  const state = reactive({
    waterFcstOptions: [],
    dispatchInfo: {},

    chartData: [],
  })

  getInWaterPage({ pageNum: 1, pageSize: Number.MAX_SAFE_INTEGER }).then(res => {
    state.waterFcstOptions = (res.data?.data || []).map(el => ({ ...el, label: el.caseName, value: el.inWaterId }))
  })

  const getInfo = () => {
    getDispatchDetails({ resvrDispId: props?.dispatchId }).then(res => {
      state.dispatchInfo = res?.data

      // 处理图表数据
      const dispatchList = res.data?.resvrDispResList || []

      const rainData = {
        name: '雨量',
        data: dispatchList.map(el => [el.tm, el.rain]),
      }
      const sumRainData = {
        name: '累计雨量',
        data: rainData.data.map((el, idx) => {
          const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
          return [el[0], +sum.toFixed(1)]
        }),
      }
      if (dispatchList?.length) {
        state.chartData = [
          rainData,
          sumRainData,
          {
            name: '水位',
            data: dispatchList.map(el => [el.tm, el.wlv]),
          },
          {
            name: '入库流量',
            data: dispatchList.map(el => [el.tm, el.inflow]),
          },
          {
            name: '出库流量',
            data: dispatchList.map(el => [el.tm, el.outWater]),
          },
        ]
      }
    })
  }
</script>
<style lang="scss" scoped></style>
