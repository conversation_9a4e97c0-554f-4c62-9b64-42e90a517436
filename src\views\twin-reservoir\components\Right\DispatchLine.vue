<template>
  <ScaleBox :style="{ width: '430px', height: '200px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="ts" name="DispatchLine">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  function getConfig(list) {
    return {
      tooltip: {
        show: true,
        trigger: 'axis',
        appendToBody: true,
        confine: true,
        className: 'echart-tooltip',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        axisPointer: {
          type: 'shadow',
          label: {
            backgroundColor: 'rgba(152,224,255,0.15)',
          },
        },
        textStyle: {
          color: '#fff',
        },
      },
      grid: {
        left: 5,
        right: 5,
        top: 40,
        bottom: 5,
        containLabel: true,
      },
      legend: {
        left: '20%',
        top: 5,
        textStyle: {
          color: '#C0DDFF',
        },
        data: ['水位', '出库流量'],
      },

      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#54608A',
          },
        },
        axisLabel: {
          color: '#8092AC',
        },
        data: list?.map(el => el.tm.substring(8)),
      },
      yAxis: [
        {
          type: 'value',
          name: '水位:m',
          nameTextStyle: {
            color: '#C0DDFF',
            padding: [0, 0, 0, 25],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#54608A',
            },
          },
          axisLabel: {
            color: '#8092AC',
          },
          splitLine: {
            lineStyle: {
              color: '#363E5B',
            },
          },
          min: 115,
        },
        {
          type: 'value',
          name: '流量:m³/s',
          nameTextStyle: {
            color: '#C0DDFF',
            padding: [0, 30, 0, 0],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#54608A',
            },
          },
          axisLabel: {
            color: '#8092AC',
          },
          splitLine: {
            lineStyle: {
              color: '#363E5B',
            },
          },
        },
      ],
      dataZoom: [
        {
          show: false,
          type: 'inside',
          xAxisIndex: [0, 1],
        },
      ],
      series: [
        {
          type: 'line',
          data: list.map(el => el.wlv),
          name: '水位',
          lineStyle: {
            color: '#09C8FC',
          },
          itemStyle: {
            normal: {
              color: '#09C8FC',
              borderColor: '#09C8FC',
            },
          },
        },
        {
          type: 'line',
          data: list.map(el => el.outflow),
          name: '出库流量',
          yAxisIndex: 1,
          lineStyle: {
            color: '#D2B461',
          },
          itemStyle: {
            normal: {
              color: '#D2B461',
              borderColor: '#D2B461',
            },
          },
        },
      ],
    }
  }
</script>

<style lang="scss" scoped></style>
