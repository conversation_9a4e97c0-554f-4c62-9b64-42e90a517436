<script setup lang="ts">
  import { useSizeStore, useUserStore } from '@/store'
  import { logout } from '@/api/user'
  import { getWeatherForecast, getWarnMessageList, getWarnMessageRecord } from '@/api'
  import { sleep } from '@/utils/index.js'
  import { platforms, routeMenus } from '@/constants'
  import dayjs from 'dayjs'
  import CultureModel from './CultureModel.vue'

  const sizeStore = useSizeStore()
  const userStore = useUserStore()

  const router = useRouter()
  const route = useRoute()
  const currentRouteName = ref(route.name)

  const updateCurrentRouteName = () => {
    currentRouteName.value = route.name
  }

  const { currentTime, weekday, currentDate } = useCurrentTime()
  const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
  const weather = ref(null)
  const currentMenu = ref(null)
  const menus = router.getRoutes().find(el => el.name === 'root').children
  const offLineList = ref(null)
  const isShowCulture = ref(false)
  const showCultureModel = ref(false)
  offLineList.value = []
  const isShow = ref(false)

  onMounted(() => {
    // console.log('onMounted', route.name)
    init()
  })
  const init = async () => {
    const rainWarn = await getWarnMessageList()
    // let param = {
    //   groupId: null,
    //   objectCode: '',
    //   objectName: '',
    //   pageNum: 1,
    //   status: 1,
    //   // pageSize: Number.MAX_SAFE_INTEGER,
    //   pageSize: 2,
    //   warnTimeLower: null,
    //   warnTimeUpper: null,
    // }

    // let waterWarn = await getWarnMessageRecord(param)
    console.log('rainWarn', rainWarn)
    // offLineList.value = [...rainWarn.data, ...waterWarn.data?.data?.map(el => el.message)]
    offLineList.value = [...rainWarn?.data]
  }

  // 退出登录
  const onLogOutClick = () => {
    window.$dialog.warning({
      title: '退出登录',
      content: '确认退出登录？',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick() {
        logout().then(async () => {
          window.$message.success('退出成功')
          userStore.$patch({ token: null })
          userStore.$patch({ user: null })

          await sleep(1500)

          window.location.href = `${import.meta.env.VITE_CAS_URL}/logout?service=${window.location.origin}`
        })
      },
      onNegativeClick() {
        $message.warning('已取消')
      },
    })
  }
  // 天气
  const getInfo = async () => {
    // platforms.weatherCode

    const res: any = await getWeatherForecast({ locationCode: platforms.weatherCode })

    currentMenu.value = 0
    weather.value = (res.data || []).map(el => ({ ...el, iconType: 'iconDay', textType: 'textDay' }))
    // console.log('123 get platforms', weather.value)
    const sunsetTime = `${dayjs().format('YYYY-MM-DD')} ${weather.value[0].sunset}`
    const sunriseTime = `${dayjs().format('YYYY-MM-DD')} ${weather.value[0].sunrise}`
    if (+new Date() < dayjs(sunsetTime).valueOf() && +new Date() > dayjs(sunriseTime).valueOf()) {
      weather.value[0].iconType = 'iconDay'
      weather.value[0].textType = 'textDay'
    } else {
      weather.value[0].iconType = 'iconNight'
      weather.value[0].textType = 'textNight'
    }
  }
  getInfo()
  const onResize = () => {
    sizeStore.onWindowResize()
  }
  const onShowCulture = () => {
    console.log('onShowCulture', isShowCulture.value)
    // if (isShowCulture.value) {
    //   isShowCulture.value = false
    // } else {
    //   isShowCulture.value = true
    // }
    isShowCulture.value = true
    showCultureModel.value.openModal()
  }
  const routerClick = name => {
    if (name == 'reservoir') {
      router.push('/twin-reservoir')
    } else {
      router.push({ name: name })
    }
  }
</script>

<template>
  <div class="app-wrapper">
    <div class="app-wrapper-content" v-resize="onResize">
      <header class="app-header">
        <!-- <div class="logo"></div> -->
        <div class="header" :style="{ background: `url(${getImageUrl(`header.png`)}) no-repeat center / 100% 100%` }">
          <div class="title">
            {{ platforms?.title }}
          </div>
          <div
            class="mt-0.18rem ml-5.6rem h-0.34rem w-0.34rem uno-bg_culture.png fixed cursor-pointer pointer-events-auto z-9999999"
            @click="onShowCulture"
          ></div>
        </div>
        <div class="header-left">
          <div class="header-title">
            <div class="header-title-left"></div>
            <div class="header-title-right">{{ routeMenus[route.name]?.title }}</div>
          </div>
          <div v-if="weather" class="weather">
            <i :class="[`qi-${weather[0]?.[weather[0].iconType]}`, 'ml-0.08rem mr-0.12rem text-0.18rem']"></i>
            <span class="ml-0.08rem mr-0.12rem text-0.14rem font-normal">
              {{ `${weather[0]?.windDirDay}转${weather[0]?.windDirNight}` }}
            </span>
            <span class="ml-0.08rem mr-0.12rem text-0.18rem font-normal">
              {{ weather[0]?.tempMin || '' }}℃/{{ weather[0]?.tempMax }}℃
            </span>
          </div>
        </div>
        <div class="header-right">
          <span class="mr-0.08rem text-0.14rem">{{ currentDate }}</span>
          <span class="mr-0.08rem text-0.14rem">{{ weekday }}</span>
          <span class="mr-0.08rem text-0.18rem font-bold">
            {{ currentTime }}
          </span>
        </div>
      </header>
      <footer class="app-footer">
        <div class="menu-icon" v-if="!isShow" @click="isShow = true"></div>
        <!-- :style="{ background: `url(${getImageUrl(`footer.png`)}) no-repeat center / 100% 100%` }" -->
        <!-- <div class="absolute z-11 bottom-0.3rem left-1/2 -translate-x-1/2 flex bg-red" v-if="isShow">
          <div
            v-for="(el, idx) in menus"
            :key="idx"
            class="size-0.6rem cursor-pointer flex items-end justify-center font-[AlimamaShuHeiTi] text-0.14rem tracking-0.01rem"
            :style="{
              background: `url(${getImageUrl(`menus/${el.name}${route.name === el.name ? '-active' : ''}.png`)}) no-repeat center / 100% 100%`,
              marginLeft: idx === 0 ? '0' : idx === 2 ? '0.56rem' : '0.61rem',
              marginTop: idx === 0 || idx === 4 ? '-0.1rem' : idx === 1 || idx === 3 ? '-0.28rem' : '-0.5rem',
              color: route.name === el.name ? 'var(--primary-color)' : '#C0DDFF',
            }"
            @click="router.push({ name: el.name })"
          >
            <div
              class="absolute z-10"
              :style="{ marginTop: idx === 0 || idx === 4 ? '-0.1rem' : idx === 1 || idx === 3 ? '-0.2rem' : '-0.8rem' }"
            >
              {{ el.meta.title }}
            </div>
          </div>
        </div> -->
        <div class="footer"></div>
      </footer>
      <RouterView />
    </div>
    <div class="bamboo"></div>
    <!-- 模块菜单切换-->
    <div class="base-box z-11 flex relative" v-if="isShow">
      <div class="box-content" v-for="(value, key, idx) in routeMenus" :key="idx" @click="routerClick(key)">
        <div
          class="box1 absolute text-[#fff] text-0.15rem z-10"
          :style="{
            marginTop: idx === 1 ? '2.06rem' : idx === 2 ? '1.53rem' : idx === 3 ? '2.06rem' : idx === 4 ? '2.6rem' : '2.62rem',
            marginLeft: idx === 1 ? '-0.48rem' : idx === 2 ? '0.62rem' : idx === 3 ? '1.05rem' : idx === 4 ? '0.8rem' : '1.18rem',
          }"
          :class="route.name === key ? 'menu-title' : ''"
        >
          {{ value.title }}
        </div>
        <div
          class="box2 size-0.4rem cursor-pointer flex items-end justify-center font-[AlimamaShuHeiTi] text-0.16rem tracking-0.01rem z-12 relative"
          :style="{
            background: `url(${getImageUrl(`menus/${key}${route.name === key ? '-active' : ''}.png`)}) no-repeat center / 100% 100%`,
            marginLeft: idx === 1 ? '0.36rem' : idx === 2 ? '0.57rem' : idx === 3 ? '0.58rem' : idx === 4 ? '0.37rem' : '2.16rem',
            marginTop: idx === 1 ? '1.94rem' : idx === 2 ? '1.74rem' : idx === 3 ? '1.94rem' : idx === 4 ? '2.48rem' : '2.48rem',
            color: route.name === key ? 'var(--primary-color)' : '#C0DDFF',
          }"
        >
          <div class="absolute top--0.05rem left--0.03rem size-0.47rem" :class="{ 'box-animation': route.name == key }"></div>
        </div>
      </div>

      <div
        class="absolute pointer-events-auto z-12 items-center left-3.8rem bottom-0.15rem w-0.64rem h-0.64rem bg-red cursor-pointer"
        :style="{ background: `url(${getImageUrl('cancel.png')}) no-repeat center / 100% 100%` }"
        @click="isShow = false"
      ></div>
    </div>

    <!-- 页面预警信息 -->
    <div
      class="message relative"
      v-if="offLineList.length"
      :style="{ background: `url(${getImageUrl('warning.png')}) no-repeat center / 100% 100%` }"
    >
      <div
        class="absolute z-10 left-0.18rem w-0.8rem h-0.8rem"
        :style="{ background: `url(${getImageUrl('warning-icon.png')}) no-repeat center / 100% 100%` }"
      ></div>
      <!-- <div class="size-0.38rem" :style="{ background: `url(${getImageUrl('warning.png')}) no-repeat center / 100% 100%` }"></div> -->
      <TextSwiperScroll :dataSource="offLineList" />
    </div>
    <CultureModel ref="showCultureModel" @close="isShowCulture = false" />
  </div>
</template>

<style lang="scss" scoped>
  .app-wrapper {
    position: relative;
    height: 100vh;
    width: 100vw;

    .app-wrapper-content {
      position: absolute;

      height: 100vh;
      width: 100vw;
      background: url('@/assets/images/bg.png') no-repeat center / 100% 100%;
      z-index: 4;
      .app-header {
        .logo {
          position: fixed;
          z-index: 3;
          left: 0.2rem;
          top: 0.23rem;
          width: 1.14rem;
          height: 0.36rem;
          // background: url('@/assets/images/logo.png') no-repeat center / 100% 100%;
          background: red;
        }
        .header {
          position: fixed;
          z-index: 3;
          // left: 50%;
          // transform: translate(-50%);
          top: 0;
          // width: 7.44rem;
          width: 100%;
          height: 1.2rem;
          // height: 1.1rem;
          // pointer-events: none;
          display: flex;
          justify-content: center;
          pointer-events: none;

          .title {
            position: fixed;
            margin-top: 0.15rem;
            font-family: KwangLiangWine;
            font-size: 0.4rem;
            letter-spacing: 0.09em;
            background: radial-gradient(45% 153% at 43% 50%, #31dfc7 0%, rgba(49, 223, 199, 0) 100%), #ffffff;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-fill-color: transparent;
            // text-shadow: 0px 0.04rem 0.1rem rgba(0, 0, 0, 0.3);
          }
        }
        .header-left {
          position: fixed;
          z-index: 4;
          left: 0.2rem;
          top: 0.23rem;
          font-size: 0.2rem;
          color: #fff;
          display: flex;
          align-items: center;
          .header-title {
            margin-left: 0.16rem;
            height: 0.27rem;
            border-radius: 4px;
            opacity: 1;
            /* 自动布局 */
            display: flex;
            flex-direction: row;
            align-items: center;
            padding: 2px 10px;
            gap: 4px;

            background:
              radial-gradient(46% 46% at 50% 100%, rgba(27, 183, 226, 0.45) 0%, rgba(36, 177, 216, 0) 100%),
              linear-gradient(180deg, #13232c 0%, #142631 98%);

            box-sizing: border-box;
            border: 1px solid;
            border-image: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0) 97%) 0.6;

            z-index: 0;

            .header-title-left {
              width: 0.12rem;
              height: 0.12rem;
              background: url('@/assets/images/header-icon.png') no-repeat;
              // background: red;
              background-size: 100% 100%;
            }
            .header-title-right {
              font-family: Source Han Sans;
              font-size: 0.16rem;
              font-weight: normal;
              line-height: normal;
              letter-spacing: 0em;

              font-variation-settings: 'opsz' auto;
              font-feature-settings: 'kern' on;
              /* 文本/二级 */
              color: #b2daea;

              text-shadow: 0px 4px 10px rgba(0, 0, 0, 0.3);

              z-index: 1;
            }
          }
          .weather {
            margin-left: 0.16rem;
            font-size: 0.14rem;
            font-weight: 700;
            color: #ffffff;
            display: flex;
            align-items: center;

            .weather-btn {
              display: flex;
              align-items: center;
              border-radius: 0.04rem;
              background: linear-gradient(180deg, rgba(255, 255, 255, 0.17) 0%, rgba(112, 105, 252, 0.1) 95%);
              border: 1px solid rgba(168, 167, 255, 0.56);
              padding: 0.04rem 0.06rem;
              cursor: pointer;
            }
            .weather-icon {
              font-size: 0.26rem;
              margin-right: 0.16rem;
              margin-left: 0.04rem;
            }
          }
        }
        .header-right {
          position: fixed;
          z-index: 4;
          right: 1.4rem;
          top: 0.23rem;
          font-size: 0.2rem;
          color: #fff;
          display: flex;
          align-items: center;
        }

        .footer {
          // position: fixed;
          margin-top: 1.6rem 1.28rem 0 1.28rem;
          // margin: 0 1.28rem;
          // margin: 0 1.28rem;
          bottom: 0;
          // z-index: 4;
          width: 100%;
        }
      }
      .app-footer {
        position: fixed;
        bottom: 0.04rem;
        left: 0;
        width: 100%;
        height: 0.33rem;
        background: url('@/assets/images/footer.png') no-repeat;
        background-size: 100% 100%;
        z-index: 3;
        display: flex;
        // align-items: center;
        justify-content: center; /* 水平居中 */
        align-items: center; /* 垂直居中（可选） */
        // height: 100vh; /* 使容器高度占满整个视口高度，可选 */
        .menu-icon {
          cursor: pointer;
          width: 0.5rem;
          height: 0.5rem;
          margin-bottom: 0.2rem;
          // background-color: #ff6b6b; /* 选择你喜欢的颜色 */
          background: url('@/assets/images/menu-tooltip.png') no-repeat;
          background-size: 100% 100%;
          // border-radius: 50%;
          // position: relative;
          animation: breathe 2s ease-in-out infinite;
        }
        @keyframes breathe {
          0%,
          100% {
            transform: translateY(0);
          }
          50% {
            transform: translateY(-20px); /* 调整上下跳动的距离 */
          }
        }
      }
    }

    .annulus {
      //   position: absolute;
      //   // left: 5.07rem;
      //   // top: 1.54rem;
      //   left: 50%;
      //   // transform: translateX(-50%);
      //   top: 1.6rem;
      //   width: 8.82rem;
      //   height: 8.35rem;
      //   opacity: 1;

      //   background: url('@/assets/images/bg-annulus.png') no-repeat center / 100% 100%;
    }
  }

  .bamboo {
    position: absolute;
    // position: fixed;
    // left: 50%;
    // transform: translateX(-50%);
    right: 0;
    top: 0;
    // width: 2.53rem;
    // height: 2.6rem;
    // width: 5rem;
    // height: 5.2rem;
    // width: 4rem;
    // height: 4.2rem;
    width: 3.5rem;
    height: 3.84rem;
    opacity: 1;
    background: url('@/assets/images/bamboo.png') no-repeat;
    background-size: 100% 100%;
    z-index: 4;
    pointer-events: none;
  }

  .weather-item {
    padding: 0;
    width: 3.68rem;
    cursor: default;
    border-radius: 0.08rem;
    border: 1px solid #20355b;
    background:
      radial-gradient(46% 77% at 89% 0%, rgba(162, 207, 255, 0.15) 4%, rgba(151, 201, 255, 0) 100%),
      linear-gradient(99deg, rgba(21, 40, 87, 0.6) 11%, rgba(32, 53, 91, 0.6) 86%);

    .weather-header {
      background: linear-gradient(92deg, rgba(63, 94, 144, 0.32) 12%, rgba(64, 88, 124, 0.25) 87%);
      height: 0.32rem;
      padding: 0 0.12rem;
    }
  }

  .message {
    position: absolute;
    top: 0.8rem;
    // top: 1.03rem;
    left: 50%;
    transform: translateX(-50%);
    width: 5.28rem;
    height: 0.38rem;
    padding: 0 0.32rem 0 0.9rem;
    // border: 1px solid rgba(255, 255, 255, 0.23);
    // border-radius: 0.38rem;
    z-index: 4;

    display: flex;
    align-items: center;

    // background: rgba(217, 237, 255, 0.1);
    // backdrop-filter: blur(0.2rem);
  }
  .base-box {
    position: absolute;
    bottom: 0rem;
    left: 50%;
    transform: translateX(-50%);
    width: 8.23rem;
    height: 3.35rem;

    background: url('@/assets/images/base-bg.png') no-repeat;
    background-size: 100% 100%;
    // padding: 0 0.32rem 0 0.36rem;
    // border: 1px solid rgba(255, 255, 255, 0.23);
    // border-radius: 0.38rem;
    z-index: 4;

    display: flex;
    // align-items: center;
    pointer-events: none;
    .box-content {
      height: 2rem;

      pointer-events: none;
      // background: red;
      .box1 {
        // background: yellow;
        pointer-events: auto;
      }
      .box2 {
        // background: green;
        pointer-events: auto;
      }
    }
  }
  .menu-title {
    opacity: 1;

    // font-family: Source Han Sans;
    font-size: 0.16rem;
    font-weight: 500;
    // line-height: normal;
    letter-spacing: 0em;

    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
    /* 文本/一级 */
    color: #ffffff;

    text-shadow: 0px 4px 10px #09c8fc;
  }
  .box-animation {
    background: url('@/assets/images/menus/loading.png') no-repeat;
    background-size: 100% 100%;
    animation: rotate 14s linear infinite;
  }

  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
