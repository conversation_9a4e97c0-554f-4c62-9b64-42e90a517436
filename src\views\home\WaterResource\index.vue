<template>
  <MyCard name="灌区水资源管理" class="relative z-3">
    <!-- h-1.9rem -->
    <div class="content pt-0.18rem z-3" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave" @wheel="handleWheel">
      <!-- hover -->
      <vue3-seamless-scroll
        :list="dataSource"
        class="data-scroll"
        :hover="isHovering"
        ref="scrollRef"
        :key="1"
        :step="1"
        v-if="dataSource?.length"
      >
        <div
          class="scroll-item relative text-0.14rem cursor-pointer"
          v-for="(el, index) in dataSource"
          :key="index"
          @click="selectSlide(index)"
        >
          <div
            class="item-content w-98% h-1.6rem flex flex-col ml-0.05rem mb-0.1rem mr-0.1rem overflow-hidden"
            :key="index"
            :class="{ active: activeSlide == index }"
            :style="{
              background:
                activeSlide == index
                  ? `url(${getImageUrl('right/content-type3.png')}) no-repeat center / 100% 100%`
                  : `url(${getImageUrl('right/content-type1.png')}) no-repeat center / 100% 100%`,
            }"
          >
            <div class="h-0.32rem px-0.12rem flex items-center mt-0.06rem text-[#fff] text-0.14rem">
              <span class="text-[#B2DAEA]">灌溉范围:&nbsp;</span>
              <span class="text-overflow1 min-w-2rem w-2.2rem">
                {{ el.districtCodeName || '-' }}
              </span>
            </div>
            <div class="h-0.32rem px-0.12rem flex items-center mt-0.06rem text-[#fff] text-0.14rem">
              <span class="text-[#B2DAEA]">调度单编码:&nbsp;</span>
              {{ el.serialNumber || '-' }}
            </div>
            <div class="info-item h-0.58rem flex my-0.06rem mx-0.14rem justify-between w-[93%] px-0.1rem pt-0.05rem text-0.14rem">
              <div class="flex flex-col flex-1">
                <span class="text-[#B2DAEA]">计划调度水量</span>
                <div class="text-0.16rem text-[#09fcc7] pt-0.04rem mt-0.03rem">{{ el.waterValue || '-' }} 万m³</div>
              </div>
              <div class="flex flex-col flex-1">
                <span class="text-[#B2DAEA]">田间总用水量</span>
                <div class="text-0.16rem text-[#09fcc7] pt-0.04rem mt-0.03rem">{{ el.fieldWaterFlow || '-' }} 万m³</div>
              </div>
              <div class="flex flex-col flex-1 text-[##09FCC7]">
                <span class="text-[#B2DAEA]">水库放水量</span>
                <span class="text-0.16rem text-[#09fcc7] pt-0.04rem mt-0.03rem">{{ el.waterFlow || '-' }} 万m³</span>
              </div>
            </div>
            <div class="h-0.32rem px-0.12rem flex items-center mb-0.06rem text-[#fff] text-0.14rem">
              <div class="text-[#B2DAEA]">计划调度时间:&nbsp;</div>
              {{ el.waterDate || '-' }}
            </div>
          </div>
          <!-- <div
              style="position: absolute; top: 0.1rem; right: 0.1rem; z-index: 9999"
              :style="{ background: `url(${getImageUrl('right/type3.png')}) no-repeat center / 100% 100%` }"
            ></div> -->
          <div
            class="item-content-type absolute h-0.25rem w-1.08rem top-0rem right-0rem mr-0.05rem items-center text-center pt-0.03rem text-0.14rem text-[#fff] z-9999"
            :class="{ active: activeSlide == index }"
            :style="{
              background:
                el.schedulingType == 1
                  ? `url(${getImageUrl('right/type1.png')}) no-repeat center / 100% 100%`
                  : `url(${getImageUrl('right/type3.png')}) no-repeat center / 100% 100%`,
            }"
          >
            <!-- <img src="@/assets/images/right/type3.png" class="w-full h-full" /> -->
            {{ waterSourceTypes[el.schedulingType]?.name || '-' }}
          </div>
        </div>
      </vue3-seamless-scroll>
      <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
    </div>
    <div
      class="absolute w-0.88rem h-0.22rem top-0.11rem right-0.06rem z-5 cursor-pointer"
      :style="{ background: `url(${getImageUrl('home/more.png')}) no-repeat center / 100% 100%` }"
      @click="jumpTo(3)"
    ></div>
  </MyCard>
</template>

<script setup lang="tsx" name="Operation">
  import { waterSourceTypes } from '@/constants'
  import { getConsumptionList } from '../services'
  import { getOptions } from '@/api'
  import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
  import { useUserStore } from '@/store'
  const userStore = useUserStore()
  const dataSource = ref([])

  const swiperInstance = ref(null)
  //初始化swiper后，为swiperInstance赋值
  const activeSlide = ref(null)
  const state = $ref({
    linkList: [],
  })
  const selectSlide = index => {
    activeSlide.value = index
    if (swiperInstance.value) {
      swiperInstance.value.slideTo(index)
    }
  }

  const isHovering = ref(false) // 是否暂停滚动
  const currentIndex = ref(0) // 当前显示的数据索引
  const scrollRef = ref(null) // 引用无缝滚动组件实例

  // 鼠标移入时暂停滚动
  const handleMouseEnter = () => {
    isHovering.value = true
  }

  // 鼠标移出时恢复滚动
  const handleMouseLeave = () => {
    isHovering.value = false
  }

  // 滚轮事件：切换当前显示的数据
  const handleWheel = event => {
    if (!isHovering.value) return // 如果没有暂停，不处理滚轮事件

    event.preventDefault() // 阻止默认滚动行为

    if (event.deltaY > 0) {
      // 向下滚动：切换到下一个数据
      currentIndex.value = (currentIndex.value + 1) % dataSource.value.length
    } else {
      // 向上滚动：切换到上一个数据
      currentIndex.value = (currentIndex.value - 1 + dataSource.value.length) % dataSource.value.length
    }

    // 手动更新滚动位置
    if (scrollRef.value) {
      const container = scrollRef.value.$el.querySelector('ul')
      container.style.transform = `translateY(-${currentIndex.value * 40}px)` // 假设每个数据项高度为 40px
    }
  }

  onMounted(() => {
    init()
  })
  const init = () => {
    getOptions('screenJumpLink').then(res => {
      state.linkList = res.data
    })
    let param = {
      consumptionId: null,
      endTime: '',
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
      schedulingStatus: null, //调度阶段(1下发调度单2用水调度3水量复核4调度完成)
      schedulingType: null, //调度类型(灌溉调度2防汛调度)
      serialNumber: '',
      sort: [],
      startTime: '',
      type: 1,
    }
    getConsumptionList(param).then(res => {
      dataSource.value = res?.data?.data || []
    })
  }
  //获取更多
  const jumpTo = val => {
    state.linkList.forEach(item => {
      if (item.key == val) {
        const url = `${item.value}?token=${userStore.token}`
        window.open(url, '_blank')
      }
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  :deep(.slick-slide) {
    height: 1.6rem;
    overflow: hidden;
    color: #ffffff;
  }
  .info-item {
    border-radius: 0.04rem;
    background: linear-gradient(94deg, rgba(63, 94, 144, 0.32) 12%, rgba(64, 88, 124, 0.25) 86%);
    box-shadow: inset 0px -1px 0.02rem 0px rgba(255, 255, 255, 0.2);
  }

  .data-scroll {
    height: 100%;
    // background: red;
    overflow: hidden;

    .scroll-item {
      // margin-right: 0.2rem;
      width: 100%;
      height: 1.66rem;
      // background: url('@/assets/images/intelligent/chart.png') no-repeat;
      background-size: 100% 100%;
      margin-bottom: 10px;
    }
  }
</style>
