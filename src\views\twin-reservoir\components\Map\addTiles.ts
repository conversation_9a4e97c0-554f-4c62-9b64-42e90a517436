//
import * as Cesium from 'cesium'
const modelUrl = import.meta.env.VITE_MODEL_URL
import { tilesList, closeGateMatrix } from './tiles'
import { markList } from './marks'

export const addPhotography = async (viewer: any) => {
  try {
    const thj = await Cesium.Cesium3DTileset.fromUrl(modelUrl + '/taohuajiang-3dtiles/tileset.json')
    thj.id = 'photography'
    ;(thj.cacheBytes = 1336870912), (thj.maximumCacheOverflowBytes = 366870912)
    viewer.scene.primitives.add(thj)
  } catch (error) {
    console.log('error')
  }
}

export const addBuilding = async (viewer: any) => {
  try {
    for (let i = 0; i < tilesList.length; i++) {
      let tiles = await Cesium.Cesium3DTileset.fromUrl(tilesList[i].url)
      tiles.id = tilesList[i].id
      if (tilesList[i].id.includes('SZZM')) {
        tiles.modelMatrix = closeGateMatrix
      }

      viewer.scene.primitives.add(tiles)
    }
  } catch (error) {
    console.log('error')
  }
}

export const addRoof = async (viewer: any, id) => {
  try {
    const row = tilesList.find(el => el.id == id)
    let tiles = await Cesium.Cesium3DTileset.fromUrl(row.url)
    tiles.id = row.id

    viewer.scene.primitives.add(tiles)
  } catch (error) {
    console.log('error')
  }
}
//shuimian.kml
export const addRiverSystem = async (viewer: any, waterHeight: number) => {
  const waterInstances: [] = []

  const waterPromise = await Cesium.GeoJsonDataSource.load(modelUrl + '/shuimian-shp/shuimian.geojson')
  const entities = waterPromise.entities.values
  for (let i = 0; i < entities.length; i++) {
    const entity = entities[i]
    if (entity?.polygon?.hierarchy) {
      const positions = entity.polygon.hierarchy.getValue(Cesium.JulianDate.now()).positions
      const geometry = new Cesium.GeometryInstance({
        id: 'river-system',
        geometry: new Cesium.PolygonGeometry({
          polygonHierarchy: new Cesium.PolygonHierarchy(positions),
          height: waterHeight || 0.0,
          extrudedHeight: 1.0,
          arcType: Cesium.ArcType.GEODESIC,
          vertexFormat: Cesium.EllipsoidSurfaceAppearance.VERTEX_FORMAT,
        }),
        attributes: {
          color: Cesium.ColorGeometryInstanceAttribute.fromColor(
            Cesium.Color.fromRandom({
              red: 0,
              green: 0.41568627450980394,
              blue: 0.7058823529411765,
              alpha: 1,
            }),
          ),
        },
      })
      waterInstances.push(geometry)
    }
  }
  const waterPrimitive = new Cesium.Primitive({
    allowPicking: true,
    asynchronous: false,
    geometryInstances: waterInstances,
    appearance: new Cesium.EllipsoidSurfaceAppearance({
      aboveGround: true,
      material: new Cesium.Material({
        fabric: {
          type: 'Water',
          uniforms: {
            baseWaterColor: new Cesium.Color(0, 0.415, 0.7058, 0.7),
            blendColor: new Cesium.Color(0, 0.415, 0.7058, 0.7),
            normalMap: '/imgs/waterNormals.jpg',
            frequency: 8000.0, // 控制波数的数字。'#006ab4'
            animationSpeed: 0.02, // 控制水的动画速度的数字。
            amplitude: 5.0, // 控制水波振幅的数字。
            specularIntensity: 0.8, // 控制镜面反射强度的数字。
          },
        },
      }),
    }),
  })
  viewer.scene.primitives.add(waterPrimitive)
}

export const addDots = (viewer: any, categoryCode: string) => {
  for (let i = 0; i < markList.length; i++) {
    if (markList[i].categoryCode == categoryCode) {
      viewer.entities.add({
        name: markList[i].name,
        id: categoryCode === 'section' ? markList[i].siteCode : markList[i].id,
        type: markList[i].categoryCode,
        position: Cesium.Cartesian3.fromDegrees(+markList[i].position[0], +markList[i].position[1], markList[i].alt),
        label: {
          text: markList[i].name,
          show: true,
          font: '15px',
          fontWeight: '500',
          showBackground: true,
          pixelOffset:
            viewer?.canvas?.clientWidth > 3600
              ? new Cesium.Cartesian2(84, 36)
              : viewer?.canvas?.clientWidth > 2600
                ? new Cesium.Cartesian2(63, 27)
                : new Cesium.Cartesian2(42, 18),
          backgroundColor: Cesium.Color.BLACK.withAlpha(1.0),
          scale: viewer?.canvas?.clientWidth > 3600 ? 2 : viewer?.canvas?.clientWidth > 2600 ? 1.5 : 1,
        },
        billboard: {
          image: markList[i].icon,
          show: true,
          horizontalOrigin: Cesium.HorizontalOrigin.RIGHT,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          scale: viewer?.canvas?.clientWidth > 3600 ? 2 : viewer?.canvas?.clientWidth > 2600 ? 1.5 : 1,
          width: 45,
          height: 77,
          zIndex: 999,
        },
      })
    }
  }
}

//
export const addWaterLevelLabel = (viewer: any, num) => {
  viewer.entities.add({
    name: '水库水位:' + num + 'm',
    id: 'reservoir-water-level',
    position: Cesium.Cartesian3.fromDegrees(112.1037572051917, 28.324325362234266, 150),
    label: {
      text: '水库水位:' + num + 'm',
      show: true,
      font: '15px',
      fontWeight: '500',

      showBackground: true,
      pixelOffset:
        viewer?.canvas?.clientWidth > 3600
          ? new Cesium.Cartesian2(0, -34)
          : viewer?.canvas?.clientWidth > 2600
            ? new Cesium.Cartesian2(0, -25)
            : new Cesium.Cartesian2(0, -16),
      backgroundColor: Cesium.Color.BLACK.withAlpha(0.9),
      scale: viewer?.canvas?.clientWidth > 3600 ? 2 : viewer?.canvas?.clientWidth > 2600 ? 1.5 : 1,
      zIndex: 9999,
    },
    billboard: {
      image: getImageUrl('twin-reservoir/reservoir-water-level.png'),
      show: true,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      scale: viewer?.canvas?.clientWidth > 3600 ? 2.1 : viewer?.canvas?.clientWidth > 2600 ? 1.6 : 1.1,
      width: 118,
      height: 28,
      zIndex: 9999,
    },
  })
}

export const removeRiverSystem = (viewer: any, str: string) => {
  const allPrims = viewer?.scene?.primitives?._primitives
  const tileToRemove = []

  for (let i = 0; i < allPrims?.length; i++) {
    if (allPrims[i]?.appearance && allPrims[i]?._instanceIds[0]?.includes(str)) {
      tileToRemove.push(allPrims[i])
    }
  }
  removeArr(tileToRemove, viewer)
}

export const remove3DTile = (viewer: any, str: string) => {
  const allPrims = viewer?.scene?.primitives?._primitives
  const tileToRemove = []
  for (let i = 0; i < allPrims?.length; i++) {
    if (allPrims[i]?.id?.includes(str)) {
      tileToRemove.push(allPrims[i])
    }
  }
  removeArr(tileToRemove, viewer)
}

const removeArr = (arr: any, viewer: any) => {
  arr?.forEach(function (tileset: any) {
    viewer.scene.primitives.remove(tileset)
  })
}
