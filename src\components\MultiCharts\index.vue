<template>
  <div ref="chartContainer" :style="{ width: width, height: height }"></div>
</template>

<script>
  import * as echarts from 'echarts'
  import { ref, onMounted, onUnmounted, watch } from 'vue'

  export default {
    props: {
      options: {
        type: Object,
        required: true,
      },
      width: {
        type: String,
        default: '100%',
      },
      height: {
        type: String,
        default: '400px',
      },
    },
    setup(props) {
      const chartContainer = ref(null)
      let chartInstance = null

      const initChart = () => {
        if (chartInstance) {
          chartInstance.dispose()
        }
        chartInstance = echarts.init(chartContainer.value)
        chartInstance.setOption(props.options)
      }

      const resizeChart = () => {
        if (chartInstance) {
          chartInstance.resize()
        }
      }

      onMounted(() => {
        initChart()
        window.addEventListener('resize', resizeChart)
      })

      onUnmounted(() => {
        if (chartInstance) {
          chartInstance.dispose()
        }
        window.removeEventListener('resize', resizeChart)
      })

      watch(
        () => props.options,
        () => {
          if (chartInstance) {
            chartInstance.setOption(props.options)
          }
        },
        { deep: true },
      )

      return {
        chartContainer,
      }
    },
  }
</script>

<style scoped>
  /* 可以添加一些样式来美化图表容器 */
</style>
