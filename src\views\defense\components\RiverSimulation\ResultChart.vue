<template>
  <ScaleBox :style="{ width: '480px', height: '200px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup name="ProcessChart">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      function getCur() {
        if (newVal.type === 0 || newVal.type === 2) {
          return { field: 'outFlow', label: '过闸流量' }
        }
        if (newVal.type === 1 || newVal.type === 4) {
          return { field: 'inFlow', label: '抽水流量' }
        }
      }
      const data = [
        {
          name: '上游水位',
          color: '#EF8432',
          data: newVal.chartData.map(el => [el.tm.slice(0, 16), el.upWlv]),
        },
        {
          name: '下游水位',
          color: '#74CF70',
          data: newVal.chartData.map(el => [el.tm.slice(0, 16), el.downWlv]),
        },
        {
          name: getCur()?.label,
          color: '#09ECFF',
          yAxisIndex: 1,
          data: newVal.chartData.map(el => [el.tm.slice(0, 16), el[getCur()?.field]]),
        },
      ]
      nextTick(() => {
        setTimeout(() => {
          updateOptions(opt => {
            return getConfig(data)
          })
        })
      })
    },
    { immediate: true },
  )

  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    return {
      grid: {
        left: 25,
        right: 20,
        top: 45,
        bottom: 10,
        containLabel: true,
      },
      tooltip: {
        // appendToBody: true,
        // confine: true,
        // position: (pos, params, dom, rect, size) => {
        //   let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
        //   obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
        //     pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
        //   return obj
        // },
        // trigger: 'axis',
        // className: 'echart-tooltip',
        // backgroundColor: 'rgba(22,45,72,0.84)',
        // borderWidth: 0,
        // textStyle: {
        //   color: '#ffffff',
        // },
        // axisPointer: {
        //   type: 'cross',
        //   label: {
        //     backgroundColor: 'rgba(22,45,72,1)',
        //   },
        // },
        // formatter: params => {
        //   let str = `
        //     <div style="line-height:1.2">
        //       <span style="font-size:0.12rem;color:var(--text-md);color:#fff;">${params[0].value[0]}</span>
        //     </div>
        //   `
        //   params.forEach(el => {
        //     str += `
        //       <div style='display:flex;align-items:center; '>
        //         <span style='display:inline-block;margin-right:4px;width:8px;height:8px;border-radius:8px;background-color:${el.color};'>
        //         </span>
        //          <div style="font-size:0.14rem;">${el.seriesName}: <span style="color:${el.color}">${el.value[1]}m³/s</span></div>
        //       </div>
        //       `
        //   })

        //   return str
        // },
        appendToBody: true,
        confine: true,
        trigger: 'axis',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '-') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = param.color //图例颜色
              console.log('*** 11 info chart color', param)
              function getUnit(seriesName) {
                switch (seriesName) {
                  case '降雨量':
                    return 'mm'
                  case '累计雨量':
                    return 'mm'
                  case '水位':
                    return 'm'
                  case '上游水位':
                    return 'm'
                  case '下游水位':
                    return 'm'
                  case '预报来水流量':
                    return 'm³/s'
                  case '过闸流量':
                    return 'm³/s'
                  default:
                    return
                }
              }
              htmlStr += `
                    <div style="border: 1px solid rgba(152,224,255,0.3) border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
      },
      xAxis: {
        data: data.length ? data[0].data.map(i => i[0]) : [],
        nameTextStyle: {
          padding: [0, 0, 0, -5],
          color: 'rgba(54, 96, 103, 0.5)',
          fontSize: 12,
          fontWeight: 400,
        },
        axisLabel: {
          textStyle: {
            color: 'rgba(222, 242, 252, 1)',
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        z: 10,
      },
      yAxis: [
        {
          name: '水位(m)',
          nameTextStyle: { color: 'rgba(222, 242, 252, 1)' },
          axisPointer: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(105, 157, 178, 1)',
            },
          },
        },
        {
          name: '流量(m³/s)',
          position: 'right',
          nameTextStyle: {
            color: 'rgba(222, 242, 252, 1)',
          },
          axisPointer: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.1)',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: 'rgba(105, 157, 178, 1)',
            },
          },
        },
      ],
      dataZoom: [
        {
          type: 'inside',
        },
      ],
      legend: {
        show: true,
        left: 'center',
        top: 10,
        icon: 'rect',
        itemWidth: 15,
        itemHeight: 2,
        textStyle: {
          color: 'rgba(222, 242, 252, 1)',
        },
      },
      series: data.map((item, i) => {
        return {
          type: 'line',
          showBackground: true,
          smooth: true,
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          name: item.name,
          color: item.color,
          stack: 'something',
          yAxisIndex: item?.yAxisIndex || 0,

          emphasis: {
            focus: 'series',
          },
          data: item.data,
        }
      }),
    }
  }
</script>

<style lang="scss" scoped></style>
