export const markList = [
  {
    position: [112.10414977629469, 28.324838444580294],
    alt: 156,
    name: '输水隧洞闸门',
    id: 'house-gate',
    categoryCode: 'sluice-gate',
    icon: getImageUrl('twin-reservoir/mark-gate.png'),
  },
  {
    position: [112.10396079826317, 28.324681311743383],
    alt: 146,
    name: '水库一号闸门',
    id: 'sluice-gate1',
    categoryCode: 'sluice-gate',
    icon: getImageUrl('twin-reservoir/mark-gate.png'),
  },
  {
    position: [112.10385321189796, 28.324542553603045], //[112.10391652038696, 28.324469729110903],
    alt: 146,
    name: '水库二号闸门',
    id: 'sluice-gate2',
    categoryCode: 'sluice-gate',
    icon: getImageUrl('twin-reservoir/mark-gate.png'),
  },
  {
    position: [112.10354631085569, 28.324400652342202],
    alt: 150,
    name: '雨水情监测',
    id: 'rain-water',
    categoryCode: 'rain',
    icon: getImageUrl('twin-reservoir/mark-rainwater.png'),
  },
  {
    position: [112.10335993779601, 28.32418215546366],
    alt: 150,
    name: '水库视频监控',
    id: 'video',
    categoryCode: 'video',
    icon: getImageUrl('twin-reservoir/mark-video.png'),
  },
  {
    position: [112.10301171609001, 28.32452122366741],
    alt: 160,
    name: '水库大坝管理所',
    id: 'management-office',
    categoryCode: 'office',
    icon: getImageUrl('twin-reservoir/mark-office.png'),
  },
  {
    position: [112.10370254775745, 28.325789820598242],
    alt: 121,
    name: '水库量测水',
    id: 'measure-water',
    categoryCode: 'measure',
    icon: getImageUrl('twin-reservoir/mark-measure.png'),
  },
  {
    position: [112.1037637151899, 28.325587173402965],
    alt: 131,
    name: '桃花江水电站',
    id: 'hydropower-station',
    categoryCode: 'hydropower',
    icon: getImageUrl('twin-reservoir/mark-hydropower.png'),
  },

  {
    id: 93,
    siteCode: 'AQJC_5_0',
    name: '5-0（基准点）',
    alt: 160,
    categoryCode: 'section',
    position: [112.10300155609001, 28.32453022366741],
    icon: getImageUrl('twin-reservoir/mark-section.png'),
  },
  {
    id: 94,
    siteCode: 'AQJC_5_1',
    name: '5-1（测点）',
    alt: 150,
    categoryCode: 'section',
    position: [112.10337376999368, 28.324422341671304],
    icon: getImageUrl('twin-reservoir/mark-section.png'),
  },
  {
    id: 95,
    siteCode: 'AQJC_5_2',
    name: '5-2（测点）',
    alt: 150,
    categoryCode: 'section',
    position: [112.10365648143083, 28.32448089601316],
    icon: getImageUrl('twin-reservoir/mark-section.png'),
  },
  {
    id: 96,
    siteCode: 'AQJC_5_3',
    name: '5-3（测点）',
    alt: 150,
    categoryCode: 'section',
    position: [112.10391421319468, 28.324808515409043],
    icon: getImageUrl('twin-reservoir/mark-section.png'),
  },
  {
    id: 97,
    siteCode: 'AQJC_5_4',
    name: '5-4（测点）',
    alt: 150,
    categoryCode: 'section',
    position: [112.10425301299007, 28.325250621154385],
    icon: getImageUrl('twin-reservoir/mark-section.png'),
  },
]
