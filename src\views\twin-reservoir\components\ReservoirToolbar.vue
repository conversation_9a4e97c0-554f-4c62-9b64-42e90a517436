<template>
  <div class="w-1.24rem fixed left-4.67rem bottom-0.5rem z-50">
    <n-checkbox-group v-model:value="state.dotChecked" class="flex-col gap-0.1rem" @update:value="handleDotUpdate">
      <n-checkbox
        v-for="(el, idx) in dotList"
        :key="idx"
        :value="el.value"
        style="--n-font-size: 0.16rem; --n-size: 0.18rem; width: 100%; --n-label-padding: 0 0.08rem"
      >
        <div
          class="text-0.16rem border-rd-0.04rem c-#B2DAEA bg-[rgba(18,33,41,0.6)] flex b-1px b-[rgba(18,33,41,0.6)] b-solid"
          :class="{ 'checkbox-border': state.dotChecked.includes(el.value) }"
        >
          <i
            class="w-0.36rem h-0.36rem block ml--0.03rem mr--0.05rem mt-0.02rem"
            :style="{ background: `url(${el.icon})no-repeat center / 100% 100%` }"
          ></i>
          {{ el.label }}
        </div>
      </n-checkbox>
    </n-checkbox-group>
  </div>

  <div class="w-1.08rem fixed right-0.26rem top-2.1rem z-50">
    <n-checkbox
      :checked="state.photography"
      @update:checked="handleCheckedPhotography"
      style="--n-font-size: 0.14rem; --n-size: 0.18rem; width: 100%; --n-label-padding: 0 0.08rem"
    >
      <div
        class="mb-0.05rem text-0.14rem c-#B2DAEA border-rd-0.08rem bg-[rgba(32,49,61,0.83)] flex b-2px b-[#819CAA] b-solid"
        :class="{ 'layer-active': state.photography }"
      >
        <i
          class="w-0.16rem h-0.13rem block mt-0.09rem ml-0.06rem mr-0.06rem"
          :style="{
            background: `url(${state.photography ? getImageUrl('twin-reservoir/layer-3d-active.png') : getImageUrl('twin-reservoir/layer-3d.png')})no-repeat center / 100% 100%`,
          }"
        ></i>
        倾斜摄影
      </div>
    </n-checkbox>
    <n-checkbox
      class="mb-0.12rem mt-0.12rem"
      :checked="state.waterLevel"
      @update:checked="handleCheckedWaterLevel"
      style="--n-font-size: 0.14rem; --n-size: 0.18rem; width: 100%; --n-label-padding: 0 0.08rem"
    >
      <div
        class="text-0.14rem c-#B2DAEA border-rd-0.08rem bg-[rgba(32,49,61,0.83)] flex b-2px b-[#819CAA] b-solid"
        :class="{ 'layer-active': state.waterLevel }"
      >
        <i
          class="w-0.16rem h-0.13rem block mt-0.09rem ml-0.06rem mr-0.06rem"
          :style="{
            background: `url(${state.waterLevel ? getImageUrl('twin-reservoir/layer-water-level-active.png') : getImageUrl('twin-reservoir/layer-water-level.png')})no-repeat center / 100% 100%`,
          }"
        ></i>
        水库水位
      </div>
    </n-checkbox>
    <n-checkbox
      :checked="state.roof"
      @update:checked="handleCheckedRoof"
      style="--n-font-size: 0.14rem; --n-size: 0.18rem; width: 100%; --n-label-padding: 0 0.08rem"
    >
      <div
        class="text-0.14rem c-#B2DAEA border-rd-0.08rem bg-[rgba(32,49,61,0.83)] flex b-2px b-[#819CAA] b-solid"
        :class="{ 'layer-active': state.roof }"
      >
        <i
          class="w-0.16rem h-0.13rem block mt-0.09rem ml-0.06rem mr-0.06rem"
          :style="{
            background: `url(${state.roof ? getImageUrl('twin-reservoir/layer-roof-active.png') : getImageUrl('twin-reservoir/layer-roof.png')})no-repeat center / 100% 100%`,
          }"
        ></i>
        建筑物屋顶
      </div>
    </n-checkbox>
  </div>

  <div class="w-1.08rem fixed right-0.26rem top-3.58rem z-50">
    <n-checkbox
      class="mb-0.02rem"
      v-for="(el, idx) in state.simulationList"
      :key="idx"
      :checked="el.checked"
      style="--n-font-size: 0.14rem; --n-size: 0.18rem; width: 100%; --n-label-padding: 0 0.08rem"
    >
      <div
        class="text-0.14rem w-1.08rem h-0.32rem lh-0.32rem c-#B2DAEA border-rd-0.08rem b-1px b-[#819CAA] b-solid flex simulation-btn"
        :class="{ 'simulation-active': state.simulationChecked == el.value }"
        @click="changeSimulation(el)"
      >
        <i
          class="w-0.16rem h-0.13rem block mt-0.1rem mr-0.06rem ml-0.08rem"
          :style="{
            background: `url(${state.simulationChecked == el.value ? el.activeIcon : el.icon}) no-repeat center / 100% 100%`,
          }"
        ></i>
        {{ el.label }}
      </div>
    </n-checkbox>
  </div>
</template>
<script setup lang="ts" name="ReservoirToolbar">
  import { dotList, simulationList } from './dot'

  onMounted(() => {})

  const state = reactive({
    dotChecked: [],
    simulationChecked: null,

    simulationList: simulationList,
    simulationIndex: 0,

    photography: true,
    waterLevel: true,
    roof: true,
  })
  const emits = defineEmits(['change-dot', 'change-photography', 'change-water-level', 'change-roof', 'change-simulation'])

  state.dotChecked = dotList.filter(el => el.checked === true).map(el => el.value)
  nextTick(() => {
    setTimeout(() => {
      emits('change-dot', state.dotChecked)
    }, 2000)
  })

  const handleDotUpdate = (val: object) => {
    emits('change-dot', val)
  }
  const handleCheckedPhotography = (checked: boolean) => {
    state.photography = checked
    emits('change-photography', checked)
  }

  const handleCheckedWaterLevel = (checked: boolean) => {
    state.waterLevel = checked
    emits('change-water-level', checked)
  }
  const handleCheckedRoof = (checked: boolean) => {
    state.roof = checked
    emits('change-roof', checked)
  }

  const changeSimulation = row => {
    for (let i = 0; i < state.simulationList.length; i++) {
      if (i !== state.simulationIndex) {
        state.simulationList[i].checked = false
      }
    }
    row.checked = !row.checked
    if (row.checked) {
      state.simulationChecked = row.value
    } else {
      state.simulationList.map(el => (el.checked = false))
      state.simulationChecked = ''
    }
    for (let i = 0; i < state.simulationList.length; i++) {
      if (state.simulationList[i].value === row.value) {
        state.simulationIndex = i
      }
    }
    emits('change-simulation', state.simulationChecked)
  }
</script>
<style lang="scss" scoped>
  :deep(.n-button .n-button__border) {
    border: none;
  }
  :deep(.n-checkbox-box-wrapper) {
    display: none;
  }
  :deep(.n-checkbox__label) {
    width: 1.24rem;
    height: 0.32rem;
    padding: 0;
    font-size: 0.14rem;
    border-radius: 0.04rem;
    line-height: 0.34rem;
    margin-bottom: 0.05rem;
  }
  .checkbox-border {
    color: #09c8fc;
    border: 1px solid #09c8fc;
  }
  .layer-active {
    color: #fff;
    background: radial-gradient(75% 65% at 47% 100%, #09fc4a 0%, rgba(9, 252, 220, 0) 94%),
      linear-gradient(180deg, #098ffc 0%, rgba(85, 130, 148, 0) 100%);
    box-sizing: border-box;
    border: 1px solid #09c8fc;
  }
  .simulation-btn {
    background: linear-gradient(180deg, #0c2029 0%, #1f536a 100%);
  }
  .simulation-active {
    color: #09fcc7;
    height: 0.34rem;
    line-height: 0.34rem;
    border: none;
    background: url('@/assets/images/twin-reservoir/simulation-border-active.png') no-repeat center / 100% 100%;
  }
</style>
