import { createVNode, render } from 'vue'
import ComMarkerPopup from './PopupContent.vue'
import mapboxgl from 'mapbox-gl'

export function mapboxPopup(mapIns, item) {
  let elPopup = document.createElement('div')
  //ComMarkerPopup为组件名称
  let vNodePopup = createVNode(ComMarkerPopup, item)
  render(vNodePopup, elPopup)
  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: [0, 0],
  }

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(elPopup).addTo(mapIns)
}
