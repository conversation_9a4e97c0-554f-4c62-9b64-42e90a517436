const modelUrl = import.meta.env.VITE_MODEL_URL

export const closeGateMatrix = {
  '0': 0.9994583654019555,
  '1': -0.028214584574161903,
  '2': -0.016938507766570472,
  '3': 0,
  '4': 0.027999526273730934,
  '5': 0.9995259537764402,
  '6': -0.012802119189740344,
  '7': 0,
  '8': 0.01729168460553989,
  '9': 0.01232091492580952,
  '10': 0.9997745709403167,
  '11': 0,
  '12': -198926.06853787007,
  '13': -94251.31440315003,
  '14': 31511.408012783235,
  '15': 0.9999999999999988,
}

export const tilesList = [
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ001/tileset.json',
    id: 'BDDZ001',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ002/tileset.json',
    id: 'BDDZ002',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ003/tileset.json',
    id: 'BDDZ003',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ004/tileset.json',
    id: 'BDDZ004',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ005/tileset.json',
    id: 'BDDZ005',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ006/tileset.json',
    id: 'BDDZ006',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/BDDZ007/tileset.json',
    id: 'BDDZ007',
  },
  //管理所
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS001/tileset.json',
    id: 'GLS001',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS002/tileset.json',
    id: 'GLS002',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS003/tileset.json',
    id: 'GLS003',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS004/tileset.json',
    id: 'GLS004',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS005/tileset.json',
    id: 'GLS005',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS006/tileset.json',
    id: 'GLS006',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS007/tileset.json',
    id: 'GLS007',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/GLS008/tileset.json',
    id: 'GLS008',
  },
  //闸门
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/SZZM001/tileset.json',
    id: 'SZZM001',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/SZZM002/tileset.json',
    id: 'SZZM002',
  },
  //水电站
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ001/tileset.json',
    id: 'THJSDZ001', //屋顶
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ002/tileset.json',
    id: 'THJSDZ002',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ003/tileset.json',
    id: 'THJSDZ003',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ005/tileset.json',
    id: 'THJSDZ005',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ006/tileset.json',
    id: 'THJSDZ006',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ007/tileset.json',
    id: 'THJSDZ007',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ008/tileset.json',
    id: 'THJSDZ008',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ009/tileset.json',
    id: 'THJSDZ009',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ010/tileset.json',
    id: 'THJSDZ010',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ011/tileset.json',
    id: 'THJSDZ011',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ012/tileset.json',
    id: 'THJSDZ012',
  },
  {
    url: modelUrl + '/taohuajiang-shinei-3dtiles/THJSDZ013/tileset.json',
    id: 'THJSDZ013',
  },

  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM1001/tileset.json',
  //     id: 'ZM1001',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM1002/tileset.json',
  //     id: 'ZM1002',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM1003/tileset.json',
  //     id: 'ZM1003',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM1004/tileset.json',
  //     id: 'ZM1004',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM1005/tileset.json',
  //     id: 'ZM1005',
  //   },

  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM2001/tileset.json',
  //     id: 'ZM2001',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM2002/tileset.json',
  //     id: 'ZM2002',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM2003/tileset.json',
  //     id: 'ZM2003',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM2004/tileset.json',
  //     id: 'ZM2004',
  //   },
  //   {
  //     url: modelUrl + '/taohuajiang-shinei-3dtiles/ZM2005/tileset.json',
  //     id: 'ZM2005',
  //   },
]
