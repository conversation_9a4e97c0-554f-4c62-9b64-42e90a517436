<template>
  <n-tabs class="my-custom-naive-tabs" type="segment" animated>
    <n-tab v-for="(el, idx) in options" :key="idx" :name="el.key" :tab="el.title"></n-tab>
  </n-tabs>
</template>
<script setup lang="tsx" name="NaSegmentTabs">
  const attrs = useAttrs()
  const slots = useSlots()
  const { options = [] } = defineProps(['options'])
</script>
<style lang="scss">
  .my-custom-naive-tabs.n-tabs {
    .n-tabs-rail {
      padding: 0.005rem;
      // background: rgba(9, 36, 63, 0.7);
      // box-shadow: inset 0px 0px 0.23rem 0px rgba(0, 131, 230, 0.5);

      border: 1px solid rgba(21, 150, 95, 0.2); // rgba(152, 224, 255, 0.2);

      background: linear-gradient(180deg, #0c2029 0%, #1f536a 100%);

      // box-sizing: border-box;
      border-radius: 0.06rem !important;
      // border: 1px solid;
      // border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%) 1;
      // line-height: 1;

      .n-tabs-tab {
        border-radius: 0.06rem !important;
        // color: var(--text-md);
        color: #fff;

        font-size: 0.14rem;
        // padding: 0.05rem 0.06rem !important;
        padding: 0.01rem 0.06rem !important;
      }
      .n-tabs-capsule {
        border-radius: 0.06rem;
        //background: radial-gradient(74% 74% at 44% 100%, #00c8ff 0%, rgba(9, 200, 252, 0) 99%),linear-gradient(180deg, #0c2029 0%, #1f536a 100%), rgba(146, 183, 202, 0.302);
        background:
          radial-gradient(74% 74% at 44% 100%, #15965f 0%, rgba(21, 150, 95, 0) 99%),
          linear-gradient(180deg, #0c2029 0%, #1f536a 100%), rgba(146, 183, 202, 0.302);

        box-sizing: border-box;
        border: 1px solid;
        //border-image: linear-gradient(180deg, #09c8fc 0%, #09c8fc 100%) 2;
        border-image: linear-gradient(180deg, #15965f 0%, #15965f 100%) 2;
        // background: linear-gradient(180deg, #0acaff 0%, rgba(10, 202, 255, 0.4) 100%), #05111c;
        // box-shadow:
        //   inset 0px 0px 0.23rem 0px rgba(0, 131, 230, 0.5),
        //   inset 0px 0px 0.08rem 0px #0acaff;
        // border: 1px solid rgba(152, 224, 255, 0.9);
        font-size: 0.15rem;
        font-weight: 500;
        // color: #09c8fc;
        // text-align: center;
      }
    }
  }
  .n-tabs .n-tabs-tab .n-tabs-tab__label {
    border-radius: 0.06rem !important;
    color: #fff !important;
  }
  //n-tabs-tab__label
  .n-tabs .n-tabs-tab.n-tabs-tab--active .n-tabs-tab__label {
    //color: #00c8ff !important;
    color: #09fcc7 !important;
  }
  // .n-tabs .n-tabs-tab .n-tabs-tab--active {
  //   border-radius: 0.06rem !important;
  // }
  .n-tabs-tab .n-tabs-tab--active {
    border-radius: 0.06rem !important;
  }
</style>
