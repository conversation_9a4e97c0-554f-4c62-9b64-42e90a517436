<template>
  <n-config-provider class="wh-full" :locale="zhCN" :date-locale="dateZhCN" :theme="darkTheme" :theme-overrides="appConfig">
    <router-view />
  </n-config-provider>
</template>

<script setup>
  import { zhCN, dateZhCN, darkTheme } from 'naive-ui'
  import { useCssVar } from '@vueuse/core'
  import { kebabCase } from 'lodash-es'
  import { appConfig } from './constants'
  import { isTokenValid } from './api/common'

  onMounted(() => {
    console.log('token:', JSON.parse(localStorage.getItem('user') || '{}').token)
    if (JSON.parse(localStorage.getItem('user') || '{}').token) {
      isTokenValid({ token: JSON.parse(localStorage.getItem('user') || '{}').token || '' }).then(res => {
        if (!res.data) {
          console.log('res:', res)
        }
      })
    }
  })

  function handleIcoCreate() {
    let link = document.querySelector("link[rel*='icon']") || document.createElement('link')
    link.type = 'image/x-icon'
    link.rel = 'shortcut icon'
    link.href = new URL(`/src/assets/ico/${import.meta.env.VITE_FAVICON}.png`, import.meta.url).href
    document.getElementsByTagName('head')[0].appendChild(link)
  }
  handleIcoCreate()

  function setDocumentTitle(appName) {
    document.title = appName
    localStorage.setItem('appName', appName)
  }
  setDocumentTitle(import.meta.env.VITE_TITLE)

  function setupCssVar() {
    const common = appConfig.common
    for (const key in common) {
      useCssVar(`--${kebabCase(key)}`, document.documentElement).value = common[key] || ''
      if (key === 'primaryColor') window.localStorage.setItem('__THEME_COLOR__', common[key] || '')
    }
  }
  setupCssVar()
</script>
