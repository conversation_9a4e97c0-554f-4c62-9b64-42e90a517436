<script setup lang="ts">
  import { appConfig } from '@/constants/appConfig'
  const primaryColor = appConfig.common.primaryColor
</script>

<template>
  <!-- bg-[#f5f7fb] bg-transparent-->
  <div
    id="loading-container"
    class="fixed left-0 top-0 z-99999 flex-center size-full flex-col bg-transparent"
    :style="{ '--primary-color': primaryColor }"
  >
    <i class="i-svg-spinners:blocks-shuffle-3 text-60px c-primary"></i>
  </div>
</template>
