import { createVNode, render } from 'vue'
import ComMarkerPopup from '../../components/PopupContent.vue'
// import ComMarkerPopup from '../layers/PopupContent.vue'
import mapboxgl from 'mapbox-gl'

export function mapboxPopup(map, item) {
  let elPopup = document.createElement('div')
  //ComMarkerPopup为组件名称
  let vNodePopup = createVNode(ComMarkerPopup, item)
  render(vNodePopup, elPopup)
  const option = {
    closeOnClick: false,
    closeButton: false,
    offset: [0, -36],
  }

  return new mapboxgl.Popup(option).setLngLat(item.lngLat).setDOMContent(elPopup).addTo(map)
}
