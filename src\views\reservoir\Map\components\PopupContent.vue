<template>
  <div class="w-1.85rem max-h-2.3rem relative flex-col py-0.12rem px-0.12rem container" @click.stop="attrs.onClick()">
    <div class="flex-center-between h-0.24rem">
      <n-performant-ellipsis class="w-90% font-bold text-0.14rem">
        {{ attrs.object_name }}
      </n-performant-ellipsis>
    </div>

    <div v-if="attrs.indexFields?.length" class="flex-1 overflow-y-auto mt-0.06rem">
      <div
        v-for="(el, idx) in attrs.indexFields"
        class="flex items-center mb-0.08rem"
        :style="{ marginBottom: idx === attrs.indexFields.length - 1 ? '0' : '0.08rem' }"
      >
        <n-performant-ellipsis class="w-0.66rem c-text_md mr-0.08rem">
          {{ el.fieldName }}
        </n-performant-ellipsis>
        <div class="flex-1 font-bold" :style="{ color: el.isOnline ? '#00B487' : 'rgba(0, 180, 135, 0.3)' }">
          {{ el.fieldValue === null ? '-' : el.fieldValue }}
        </div>
        <div class="w-0.6rem ml-0.08rem text-center">{{ el.unit }}</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="tsx" name="PopupContent">
  const attrs = useAttrs()
</script>
<style lang="scss" scoped>
  .container {
    ::-webkit-scrollbar {
      width: 3px;
      height: 3px;
      inset: 3px 4px 2px auto;
    }
    ::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.11);
      background-color: transparent;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background-color: rgba(0, 0, 0, 0.25);
    }
    ::-webkit-scrollbar-thumb:hover {
      background: rgba(0, 0, 0, 0.4);
      cursor: pointer;
    }
    ::-webkit-scrollbar-corner {
      background: transparent;
    }
  }
</style>
