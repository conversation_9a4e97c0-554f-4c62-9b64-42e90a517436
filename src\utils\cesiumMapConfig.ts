import * as Cesium from 'cesium'

export const cesiumMapConfig = (mapId: string) => {
  Cesium.Ion.defaultAccessToken =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJkYWRlMmQ1Ni1iNGMxLTRhY2YtYmExYi1jNjYyNTUxNDhjYzgiLCJpZCI6MjMwODAsInNjb3BlcyI6WyJhc2wiLCJhc3IiLCJhc3ciLCJnYyIsInByIl0sImlhdCI6MTU4OTE4NzE1NH0.QrUVmI13wKIqFwmnsGSR6aMr8FEtbO7jsTA0mqnvbdM'

  const viewer = new Cesium.Viewer(mapId, {
    animation: false,
    homeButton: false,
    geocoder: false,
    shouldAnimate: false,
    baseLayerPicker: false,
    timeline: false,
    fullscreenButton: false,
    infoBox: false,
    sceneModePicker: false,
    navigationInstructionsInitiallyVisible: false,
    navigationHelpButton: false,
    selectionIndicator: false,
  })
  return viewer
}
