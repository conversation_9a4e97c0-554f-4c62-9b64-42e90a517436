export function getFixedNum(num, count) {
  if (num === null) return null
  if (num === 0 || num === '0') return 0

  if (!num) return ''

  return +num.toFixed(count)
}

/**
 * 截取当前数据到小数点后n位
 * @param {*} num 源数据
 * @param {*} decimals 保留的小数位数
 * */
export const twoDecimalFilter = (num, n) => {
  if (num === null) {
    return '--'
  } else if (num === 0) {
    return 0
  } else {
    let handleNum = Number(num)
    let isToFixed = handleNum.toString().includes('.') && handleNum.toString().split('.')[1].length > 2
    if (isToFixed) {
      return Number(handleNum.toFixed(2))
    } else {
      return Number(handleNum)
    }
  }
}
/**
 * 保留有效数字为3位
 * @param {*} num 源数据
 * @param {*} decimals 保留的小数位数
 * */
export const effectiveFilter = (num, n) => {
  let handleNum = Number(num)
  if (handleNum == null) {
    return '--'
  }
  if (handleNum / 10000 >= 1) {
    const decNum = handleNum / 10000
    return Number(decNum.toPrecision(n))
  } else {
    return Number(handleNum.toFixed(n))
  }
}
