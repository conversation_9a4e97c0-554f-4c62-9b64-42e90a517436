<template>
    <div class="h-full w-full reactive">

        <div class="w-4.3rem absolute top-0.8rem left-0.2rem bottom-0.2rem flex-col z-2">
            <User class="mb-0.12rem" />
            <Trend class="mb-0.12rem" />
        </div>
        <div class="w-4.3rem absolute top-0.8rem right-0.2rem bottom-0.2rem flex-col z-4">
            <Monitor class="mb-0.12rem" />
            <Warning class="mb-0.12rem" />
        </div>

        <div class="w-3rem absolute left-4.6rem bottom-0.7rem flex-col z-4">
            <MapLegend v-model:activeItem="activeItem" v-model:allData="allData" />
        </div>

        <Map :isShow="isShow" :allData="allData" v-model:activeItem="activeItem"
            v-model:activeTabLevel2="activeTabLevel2" :currentDistrict="currentDistrict" />

    </div>
</template>

<script setup lang="tsx" name="Reformation">


import Map from './Map'
import MapLegend from './MapLegend'
import User from './User'
import Trend from './Trend'
import Monitor from './Monitor'
import Warning from './Warning'

const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
})

let activeTabLevel2 = $ref(null) // 左侧选中变化
// 所有数据--右侧选中变化
let allData = $ref(null)
let activeItem = $ref(null) // 右侧选中变化
onMounted(() => {
    // unmountLoading()
})
let currentDistrict = $ref(null) //

</script>

<style lang="scss" scoped></style>