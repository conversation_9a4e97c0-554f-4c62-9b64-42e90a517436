import { ref, onMounted, onUnmounted } from 'vue'
import dayjs from 'dayjs'

const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']

function useCurrentTime() {
  const currentTime = ref(`${dayjs().format('HH:mm:ss')}`) // 创建一个响应式的ref对象来存储当前时间
  const currentDate = ref(`${dayjs().format('YYYY.MM.DD')}`) // 创建一个响应式的ref对象来存储当前时间

  const weekday = ref(`${weeks[dayjs().day()]}`)

  // 定义一个函数来更新时间
  const updateCurrentTime = () => {
    currentTime.value = `${dayjs().format('HH:mm:ss')}` // 更新当前时间
    weekday.value = `${weeks[dayjs().day()]}`
  }

  let intervalId

  onMounted(() => {
    intervalId = setInterval(updateCurrentTime, 1000) // 每秒更新时间
  })

  onUnmounted(() => {
    clearInterval(intervalId) // 清除定时器
  })

  return {
    currentTime,
    currentDate,
    weekday,
  }
}

export default useCurrentTime
