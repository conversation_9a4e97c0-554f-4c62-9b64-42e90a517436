<template>
  <ScaleBox :style="{ width: '480px', height: '200px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup name="ProcessChart">
  import * as echarts from 'echarts/core'
  import dayjs from 'dayjs'
  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      function getCur() {
        // console.log('chart ******* 666666', newVal)
        if (newVal.map_class == 'FL') {
          return { field: 'flow', label: '流量' }
        } else if (newVal.map_class == 'SL') {
          return { field: 'flow', label: '流量' }
        } else {
          return { field: 'waterLevel', label: '水位' }
        }
      }
      const data = [
        // {
        //   name: '上游水位',
        //   color: '#EF8432',
        //   data: newVal.chartData.map(el => [el.dateTime.slice(0, 16), el.upWlv]),
        // },
        {
          name: '流量',
          color: '#09ECFF',
          yAxisIndex: 1,
          data: newVal.chartData?.map(el => [dayjs(el.dateTime).format('MM-DD HH:mm'), el.flow]),
        },
      ]
      nextTick(() => {
        setTimeout(() => {
          updateOptions(opt => {
            return getConfig(data)
          })
        })
      })
    },
    { immediate: true },
  )

  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    return {
      grid: {
        left: 25,
        right: 29,
        top: 45,
        bottom: 10,
        containLabel: true,
      },
      tooltip: {
        appendToBody: true,
        confine: true,
        position: (pos, params, dom, rect, size) => {
          let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
          obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
            pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
          return obj
        },
        trigger: 'axis',
        className: 'echart-tooltip',
        // backgroundColor: 'rgba(22,45,72,0.84)',
        // borderWidth: 0,
        // textStyle: {
        //   color: '#ffffff',
        // },
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: 'rgba(22,45,72,1)',
          },
        },
        formatter: params => {
          let str = `
            <div style="line-height:1.2">
              <span style="font-size:0.12rem;color:rgba(222, 242, 252, 1);">${params[0].value[0]}</span>
            </div>
          `
          params.forEach(el => {
            str += `
              <div style='display:flex;align-items:center; '>
                <span style='display:inline-block;margin-right:4px;width:8px;height:8px;border-radius:8px;background-color:${el.color};'>
                </span>
                 <div style="font-size:0.14rem;">${el.seriesName}: <span style="color:${el.color}">${el.value[1] ? el.value[1] + 'm³/s' : '-'}</span></div>
              </div>
              `
          })

          return str
        },
      },
      xAxis: {
        data: data.length ? data[0].data.map(i => i[0]) : [],
        nameTextStyle: {
          padding: [0, 0, 0, -5],
          color: 'rgba(222, 242, 252, 1)',
          fontSize: 12,
          fontWeight: 400,
        },
        axisLabel: {
          textStyle: {
            color: 'rgba(222, 242, 252, 1)',
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        z: 10,
      },
      yAxis: [
        {
          name: '水位(m)',
          nameTextStyle: {
            color: 'rgba(222, 242, 252, 1)',
          },
          axisPointer: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: 'rgba(105, 157, 178, 1)',
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
        },
        {
          name: '流量(m³/s)',
          position: 'right',
          nameTextStyle: {
            color: 'rgba(222, 242, 252, 1)',
          },
          axisPointer: {
            show: false,
          },
          axisLine: {
            show: false,
          },

          axisTick: {
            show: false,
          },
          axisLabel: {
            color: 'rgba(105, 157, 178, 1)',
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          alignTicks: true, // ！！配置多坐标轴标签对齐
        },
      ],
      dataZoom: [
        {
          type: 'inside',
        },
      ],
      legend: {
        show: true,
        left: 'center',
        top: 10,
        icon: 'rect',
        itemWidth: 15,
        itemHeight: 2,
        textStyle: {
          color: 'rgba(222, 242, 252, 1)',
        },
      },
      series: data.map((item, i) => {
        return {
          type: 'line',
          showBackground: true,
          smooth: true,
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          name: item.name,
          color: item.color,
          yAxisIndex: item?.yAxisIndex || 0,

          emphasis: {
            focus: 'series',
          },
          data: item.data,
        }
      }),
    }
  }
</script>

<style lang="scss" scoped></style>
