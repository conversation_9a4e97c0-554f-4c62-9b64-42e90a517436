<template>
  <div class="h-full w-full reactive">
    <div class="w-4.3rem absolute top-0.8rem left-0.2rem bottom-0.2rem flex-col z-2">
      <RealTimeMonitor class="mb-0.2rem" />
      <WaterForecast class="mb-0.2rem flex-1" />
    </div>
    <div class="w-4.3rem absolute top-0.8rem right-0.2rem bottom-0.2rem flex-col z-4">
      <DisasterPrediction class="mb-0.2rem" />
      <ChannelSimulation class="mb-0.2rem flex-1" />
    </div>
    <div class="w-2rem absolute top-0.86rem right-3.56rem flex-col z-4">
      <div
        v-for="(item, index) in state.modelList"
        :key="index"
        class="w-0.93rem h-0.32rem border-rd-0.08rem flex mb-0.1rem cursor-pointer"
        :class="item.isActive ? 'uno-bg_defense/model-bg-active.png' : 'uno-bg_defense/model-bg.png'"
        @click="changeTab(item, index)"
      >
        <div
          class="w-0.16rem h-0.16rem border-rd-0.08rem mt-0.08rem ml-0.08rem flex"
          :key="item.type"
          :class="
            item.isActive && item.type == 1
              ? `uno-bg_defense/tab1-active.png`
              : !item.isActive && item.type == 1
                ? `uno-bg_defense/tab1.png`
                : // : item.isActive && item.type == 2
                  //   ? `uno-bg_defense/tab2-active.png`
                  //   : !item.isActive && item.type == 2
                  //     ? `uno-bg_defense/tab2.png`
                  item.isActive && item.type == 2
                  ? `uno-bg_defense/tab3-active.png`
                  : !item.isActive && item.type == 2
                    ? `uno-bg_defense/tab3.png`
                    : item.isActive && item.type == 3
                      ? `uno-bg_defense/tab4-active.png`
                      : !item.isActive && item.type == 3
                        ? `uno-bg_defense/tab4.png`
                        : ''
          "
        ></div>
        <div
          class="w-0.56rem h-0.2rem text-0.14rem font-350 mt-0.08rem ml-0.04rem"
          :class="item.isActive ? `text-[#fff]` : `text-[#B2DAEA]`"
        >
          {{ item.name }}
        </div>
      </div>
    </div>
    <div class="w-3rem absolute left-4.6rem bottom-0.7rem flex-col z-4">
      <MapLegend v-model:activeItem="activeItem" v-model:allData="allData" />
    </div>

    <Map
      :isShow="isShow"
      :allData="allData"
      v-model:activeItem="activeItem"
      v-model:activeTabLevel2="activeTabLevel2"
      :currentDistrict="currentDistrict"
    />
    <RainfallModel ref="showRainfallModel" @close="state.modelList = state.modelList.map(el => ({ ...el, isActive: false }))" />
    <DispatchModel ref="showDispatchModel" @close="state.modelList = state.modelList.map(el => ({ ...el, isActive: false }))" />

    <RiverSimulation
      ref="riverSimulationRef"
      @close="state.modelList = state.modelList.map(el => ({ ...el, isActive: false }))"
    />
  </div>
</template>
<script setup lang="tsx" name="Defense">
  import { reactive, ref, onBeforeMount, onMounted } from 'vue'
  import { unmountLoading } from '@/core/loading'
  import RealTimeMonitor from './RealTimeMonitor'
  import DisasterPrediction from './DisasterPrediction'
  import WaterForecast from './WaterForecast'
  import ChannelSimulation from './ChannelSimulation'
  import Map from './Map'
  import MapLegend from './MapLegend'

  import DispatchModel from './components/DispatchModel.vue'
  import RainfallModel from './components/RainfallModel.vue'
  import RiverSimulation from './components/RiverSimulation/index.vue'

  const showDispatchModel = ref(false)
  const showRainfallModel = ref(false)
  const riverSimulationRef = ref(null)

  const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
  })

  let activeTabLevel2 = $ref(null)
  let allData = $ref(null)
  let activeItem = $ref(null)
  const state = $ref({
    activeItem: 1,
    modelList: [
      // { name: '降雨分布', type: 1, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
      { name: '降雨分布', type: 1, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
      { name: '调度模拟', type: 2, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
      { name: '渠道仿真', type: 3, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
    ],
    modelObj: {
      // 1: { name: '降雨分布', type: 1, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
      1: { name: '降雨分布', type: 1, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
      2: { name: '调度模拟', type: 2, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
      3: { name: '渠道仿真', type: 3, isActive: false, color: '#B2DAEA', activeColor: '#fff' },
    },
  })
  onMounted(() => {
    // unmountLoading()
  })
  const changeTab = (item, index) => {
    // state.activeItem = item.type
    state.modelList.forEach(i => {
      i.isActive = false
    })
    item.isActive = true

    //
    if (item.type === 1) {
      showRainfallModel.value.openModal()
    } else if (item.type === 2) {
      showDispatchModel.value.openModal()
    } else if (item.type === 3) {
      riverSimulationRef.value.openModal()
    }
    let currentDistrict = $ref(null)
  }
</script>
<style lang="scss" scoped></style>
