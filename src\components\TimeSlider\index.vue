<template>
  <div class="flex items-center">
    <div :class="`btn ${state.isPlay ? 'btn-pause' : 'btn-play'}`" @click="onPlay"></div>
    <n-slider
      id="time-slider"
      :key="state.sliderKey"
      v-model:value="state.slideVal"
      :min="0"
      :max="times.length - 1"
      :format-tooltip="tipFormatter"
      :show-tooltip="true"
      :show-arrow="true"
      style="flex: 1; --n-handle-size: 0.14rem; --n-fill-color: #ffd52f; --n-fill-color-hover: #ffd52f"
      @update:value="onChange"
    >
      <template #thumb>
        <div class="size-0.14rem border-rd-50% bg-#FFD52F border-(1px solid #fff)"></div>
      </template>
      <template #tooltip="{ value }">
        <div :class="['custom-tooltip']">
          {{ value }}
        </div>
      </template>
    </n-slider>
    <n-popselect
      v-model:value="state.speed"
      placement="top"
      size="small"
      trigger="hover"
      :show-checkmark="false"
      :options="[
        { label: 'X1', value: 'X1' },
        { label: 'X2', value: 'X2' },
      ]"
      @update:value="onSpeedChange"
    >
      <div class="speed">
        {{ state.speed }}
        <MyIcon class="i-material-symbols:keyboard-arrow-up-rounded text-0.24rem" />
      </div>
    </n-popselect>
  </div>
</template>
<script setup lang="tsx" name="NaSlider">
  import { debounce } from 'lodash-es'

  const emits = defineEmits(['onTimeChange'])
  // const { times = [] } = defineProps(['times'])
  // const { resetTime: Boolean = null } = defineProps(false)
  const props = defineProps({
    resetTime: {
      type: Number,
      default: 0,
    },
    times: {
      type: Array,
      default: [],
    },
  })

  const state = reactive({
    isPlay: false,
    slideVal: 0,
    timer: null,
    interval: 1000,
    speed: 'x1',
    sliderKey: 1,
  })

  onMounted(() => {
    setTimeout(() => {
      state.sliderKey += 1
      nextTick(() => {
        emits('onTimeChange', props.times[state.slideVal])
      })
    }, 500)
  })
  onUnmounted(() => {
    clearInterval(state.timer)
  })

  watch(
    () => props.resetTime,
    (newVal, oldVal) => {
      if (newVal > 1) {
        clearInterval(state.timer)
        state.slideVal = 0
        state.isPlay = false
        state.sliderKey = 1
      }
    },
  )

  const onPlay = () => {
    if (!state.isPlay) {
      state.isPlay = true

      state.timer = setInterval(() => {
        if (state.slideVal === props.times.length - 1) {
          state.isPlay = false
          state.slideVal = 0
          clearInterval(state.timer)
          state.timer = null
        } else {
          state.slideVal += 1
        }
        emits('onTimeChange', props.times[state.slideVal])
      }, state.interval)
    } else {
      clearInterval(state.timer)
      state.timer = null
      state.isPlay = false
    }
  }

  const onChange = debounce(function (val) {
    state.slideVal = val
    emits('onTimeChange', props.times[state.slideVal])
  }, 500)

  const onSpeedChange = val => {
    switch (val) {
      case 'X1':
        state.interval = 1000
        break
      case 'X2':
        state.interval = 500
        break
      default:
        break
    }

    state.speed = val
    if (state.timer) {
      clearInterval(state.timer)
      state.timer = null
      state.isPlay = false
      state.isPlay = false
      onPlay()
    }
  }
  const tipFormatter = index => {
    return props.times[index]
  }
</script>
<style lang="scss" scoped>
  .btn-play {
    background: url('@/assets/images/time-slider-play.png') no-repeat center / 100% 100%;
  }

  .btn-pause {
    background: url('@/assets/images/time-slider-pause.png') no-repeat center / 100% 100%;
  }

  .btn {
    margin-right: 0.08rem;
    width: 0.38rem;
    height: 0.38rem;
    cursor: pointer;
  }
  :deep(.n-slider-rail) {
    height: 0.16rem;
    background: rgba(0, 211, 255, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(0, 211, 255, 0.6);

    .n-slider-rail__fill {
      margin: 1px 0px;
    }
  }

  .speed {
    width: 0.54rem;
    height: 0.3rem;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    font-size: 0.16rem;
    margin-left: 0.08rem;
    padding: 0 0.02rem 0 0.06rem;
    cursor: pointer;
    color: #ffffff;

    background: rgba(0, 155, 255, 0.2);
    border-radius: 0.04rem;
    border: 1px solid rgba(0, 155, 255, 0.1);
  }
  :deep(.custom-tooltip) {
    background: #1e80ff !important;
    color: white;
  }
</style>
