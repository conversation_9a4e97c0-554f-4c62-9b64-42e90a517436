import { MapboxOverlay } from '@deck.gl/mapbox'
import { GeoJsonLayer } from '@deck.gl/layers'
// import { PathStyleExtension } from '@deck.gl/extensions'
import { hexToRgb } from '../utils/toDeckglRgb.js'
import * as turf from '@turf/turf'
import gcoord from 'gcoord'

import { shallowRef, ref } from 'vue'

export default ({ mapIns, id, geojson, zoom }) => {
  const stateRef = shallowRef({ deckOverlay: null })
  const lastZoom = ref(zoom)

  const updateProps = () => {
    stateRef.value.deckOverlay.setProps({
      layers: [
        new GeoJsonLayer({
          id: 'geojson-layer-polygon-' + id,
          data: geojson.value,
          filled: false,
          pickable: false,
          stroked: true,
          // 描边颜色
          getLineColor: d => {
            return [...hexToRgb('#D8E2EB'), 255]
          },
          lineWidthMaxPixels: 8, // 周围最大线宽、
          lineWidthMinPixels: 8, // 周围最小线宽
        }),
        new GeoJsonLayer({
          id: 'geojson-layer-polygon-' + id + 2,
          data: geojson.value,
          filled: false,
          pickable: false,
          stroked: true,
          // lineWidthUnits: 'pixels',
          // 描边颜色
          getLineColor: d => {
            return [...hexToRgb('#165DFF'), 255]
          },
          lineWidthMaxPixels: 3, // 周围最大线宽
          lineWidthMinPixels: 3, // 周围最小线宽

          // dashJustified: true,
          // getDashArray: [5, 5],
          // extensions: [new PathStyleExtension({ dash: true, highPrecisionDash: true, offset: true })],
        }),
      ],
    })
  }

  watch(mapIns, newVal => {
    if (!newVal) return

    stateRef.value.deckOverlay = new MapboxOverlay({
      interleaved: true,
      id: 'deck-geojson-interleaved-layer-overlay-' + id,
      layers: [],
    })
    newVal.addControl(stateRef.value.deckOverlay)
  })

  watch([mapIns, geojson], ([newMapIns, newGeojson]) => {
    if (!newMapIns || !newGeojson) return

    nextTick(() => {
      updateProps()
    })
  })

  // 缩放时
  // watch(zoom, newVal => {

  //   if (newVal < 8) {
  //     // stateRef.value.deckOverlay.setProps({ layers: [] })
  //   } else {
  //     // if (stateRef.value.deckOverlay?._props?.layers?.length > 0) return
  //   }

  //   lastZoom.value = newVal
  // })
}
