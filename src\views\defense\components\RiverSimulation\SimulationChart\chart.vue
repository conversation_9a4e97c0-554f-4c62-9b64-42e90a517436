<template>
  <ScaleBox :style="{ width: `${(attrs.width * 100).toFixed(2)}px`, height: '215px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="ts">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      nextTick(() => {
        updateOptions(opt => {
          return getConfig(newVal || [])
        })
      })
    },
    { immediate: true },
  )

  function getConfig(data) {
    return {
      animation: false,
      title: {
        top: 10,
        left: 10,
        textAlign: 'left',
        textStyle: {
          color: '#000',
          fontSize: 14,
          fontWeight: 400,
        },
      },
      grid: {
        left: '4%',
        right: '4%',
        bottom: 20,
        top: 40,
        containLabel: true,
      },
      tooltip: {
        appendToBody: true,
        confine: true,
        position: (pos, params, dom, rect, size) => {
          let obj = { bottom: size.viewSize[1] - pos[1] + 10 }
          obj[['right', 'left'][+(pos[0] < size.viewSize[0] / 2)]] =
            pos[0] < size.viewSize[0] / 2 ? pos[0] + 10 : size.viewSize[0] - pos[0] + 10
          return obj
        },
        trigger: 'axis',
        // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
        borderWidth: 0,
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        textStyle: {
          color: '#ffffff',
        },
        formatter: params => {
          let str = `<div>${attrs[`chart${attrs.type}Stakes`][params[0].dataIndex]}</div>`
          params.forEach(item => {
            str += `<div>
                      ${item.marker}
                      <span>${item.seriesName}: ${item.value[1]}</span>
                    </div>`
          })
          return str
        },
      },
      xAxis: {
        data: data.length ? data[0].data.map(i => i[0]) : [],
        name: '',
        nameTextStyle: {
          padding: [0, 0, 0, -5],
          color: '#000',
          fontSize: 12,
          fontWeight: 400,
        },
        axisLabel: {
          textStyle: {
            color: '#DEF2FC',
          },
          formatter: value => {
            return attrs[`chart${attrs.type}Stakes`][+value]
          },
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        z: 10,
      },
      yAxis: [
        {
          name: '水位(m)',
          nameGap: 20,
          axisPointer: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: 'dashed',
              color: 'rgba(105, 157, 178, 0.3)',
            },
          },

          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#699DB2',
            },
          },
          min: attrs.minWL || 0,
          max: attrs.maxWL || 120,
        },
        {
          name: '流量(m³/s)',
          nameGap: 20,
          position: 'right',
          axisPointer: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            textStyle: {
              color: '#699DB2',
            },
          },
        },
      ],
      legend: {
        show: false,
      },
      series: data.map((item, i) => {
        return {
          type: 'line',
          showBackground: true,
          // smooth: true,
          // showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          name: item.name,
          color: item.color,
          yAxisIndex: item?.yAxisIndex || 0,
          areaStyle: {
            opacity: item.name === '流量' ? 0 : 1,
            color: hexToRgba(item.color, 1),
          },
          emphasis: {
            focus: 'series',
          },
          markLine: {
            animation: false,
            lineStyle: {
              width: 2,
              color: '#158CA0',
            },
            label: {
              show: true,
              position: 'start',
              formatter: '狮山湾分水闸',
              color: '#158CA0',
              padding: [18, 12, 0, 12],
            },
            silent: true, // 鼠标悬停事件, true悬停不会出现实线
            symbol: 'none', // 去掉箭头
            data:
              i === 0
                ? [
                    {
                      xAxis: attrs.markLineXAxis1,
                      lineStyle: { color: '#158CA0' },
                      label: {
                        position: 'start',
                        formatter: attrs.type === '1' ? '狮山湾分水闸' : '子良岩村分水闸',
                        color: '#158CA0',
                        padding: [18, 12, 0, 12],
                      },
                    },
                    {
                      xAxis: parseInt(attrs.markLineXAxis1 / 2),
                      lineStyle: { color: 'transparent' },
                      label: {
                        position: 'end',
                        formatter: '总干渠',
                        color: '#F6CC57',
                        padding: [18, 12, 0, 12],
                      },
                    },
                    {
                      xAxis: parseInt(attrs.markLineXAxis1 / 2),
                      lineStyle: { color: 'transparent' },
                      label: {
                        position: 'end',
                        formatter: '总干渠',
                        color: '#F6CC57',
                        padding: [18, 12, 0, 12],
                      },
                    },
                  ]
                    .concat(
                      attrs.type === '3'
                        ? [
                            {
                              xAxis: attrs.markLineXAxis2,
                              lineStyle: { color: '#158CA0' },
                              label: {
                                position: 'start',
                                formatter: '陈家冲节制闸',
                                color: '#158CA0',
                                padding: [18, 12, 0, 12],
                              },
                            },
                            {
                              xAxis: parseInt((attrs.markLineXAxis1 + attrs.markLineXAxis2) / 2),
                              lineStyle: { color: 'transparent' },
                              label: {
                                position: 'end',
                                formatter: '西干渠',
                                color: '#F6CC57',
                                padding: [18, 12, 0, 12],
                              },
                            },
                            {
                              xAxis: parseInt((attrs.markLineXAxis2 + data[0].data.length) / 2),
                              lineStyle: { color: 'transparent' },
                              label: {
                                position: 'end',
                                formatter: '沾溪干渠',
                                color: '#F6CC57',
                                padding: [18, 12, 0, 12],
                              },
                            },
                          ]
                        : [],
                    )
                    .concat(
                      attrs.type === '1' || attrs.type === '2'
                        ? [
                            {
                              xAxis: parseInt((attrs.markLineXAxis1 + data[0].data.length) / 2),
                              lineStyle: { color: 'transparent' },
                              label: {
                                position: 'end',
                                formatter: attrs.type === '1' ? '东干渠' : '西干渠',
                                color: '#F6CC57',
                                padding: [18, 12, 0, 12],
                              },
                            },
                          ]
                        : [],
                    )
                : null,
          },
          data: item.data,
        }
      }),
    }
  }
</script>

<style lang="less" scoped></style>
