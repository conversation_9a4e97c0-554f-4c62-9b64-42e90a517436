<template>
  <MyCard name="桃花江水库" class="relative">
    <template #headerRight>
      <div
        class="absolute w-0.96rem h-0.24rem top-0.1rem right-0.05rem cursor-pointer"
        :style="{ background: `url(${getImageUrl('water-resource/reservoir-3d.png')}) no-repeat center / 100% 100%` }"
        @click="toTwinReservoir"
      ></div>
    </template>
    <div class="content pt-0.1rem h-6.4rem flex flex-col relative">
      <div class="flex mt-0.05rem">
        <div
          class="w-1.82rem h-0.99rem ml-0.16rem"
          :style="{ background: `url(${getImageUrl('water-resource/reservoir.png')}) no-repeat center / 100% 100%` }"
        >
          <img src="@/assets/images/water-resource/reservoir-bg.png" alt="" class="w-1.82rem h-0.99rem border-rd-0.04rem" />
        </div>

        <div
          class="w-2rem h-0.99rem flex text-[#fff] ml-0.16rem flex-col items-center"
          :style="{ background: `url(${getImageUrl('water-resource/water-supply.png')}) no-repeat center / 100% 100%` }"
        >
          <div class="text-0.4rem text-[#09FCC7] mt-0.02rem">
            {{ state.configInfo.waterSupplyPlan }}
            <span class="text-0.14rem text-[#fff]">天</span>
          </div>
          <div class="text-0.14rem text-[#B2DAEA] mt-0.08rem">当前水库可供用水天数</div>
          <div class="text-0.14rem text-[#09FCC7] mt-0.08rem cursor-pointer" @click="jumpTo(7)">供用水方案 ></div>
        </div>
      </div>

      <div class="flex mt-0.25rem flex-wrap">
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/reservoir-type.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-0.98rem text-0.14rem">水库类型</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.15rem">{{ state.configInfo.reservoirType }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">座</span> -->
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/water-quality-grade.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-0.98rem text-0.14rem">水质等级</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.15rem">{{ state.configInfo.waterQualityGrade }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">座</span> -->
            </div>
          </div>
        </div>

        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/water-level.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-0.98rem text-0.14rem">当前水位(m)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.reservoirInfo.waterLevel }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">m</span> -->
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/storage-capacity.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.76rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-1.2rem text-0.14rem">当前库容(百万m³)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.reservoirInfo.storageCapacity }}</span>
              <!-- <span class="text-[#ffffff] text-0.24rem">41.35</span> -->
            </div>
          </div>
        </div>

        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/gate-opening-height.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-1.08rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-1.18rem text-0.14rem">闸门开启高度(m)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.configInfo.gateOpeningHeight }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">m</span> -->
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/discharge-flowrate.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-1.18rem text-0.14rem">泄水流量(m³/s)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.configInfo.dischargeFlowRate }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">m³/s</span> -->
            </div>
          </div>
        </div>
      </div>

      <div class="flex flex-col flex-1 w-4.3rem h-2rem relative">
        <div class="absolute w-1rem right-0.14rem top-0.06rem flex items-center">
          <NaSelect style="width: 0.98rem" v-model:value="year" type="side" :show-checkmark="false" :options="state.yearList">
            <template #arrow>
              <MyIcon
                class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
              />
            </template>
          </NaSelect>
        </div>

        <div
          class="left-0.16rem w-1.9rem h-0.27rem text-0.15rem px-0.2rem font-bold py-0.06rem text-[#B2DAEA] z-2 ml-0.16rem"
          :style="{ background: `url(${getImageUrl('intelligent/title-icon.png')}) no-repeat center / 100% 100%` }"
        >
          水库放水统计
        </div>

        <LineChart v-if="state.lineChartData" :dataSource="state.lineChartData" :year="year" class="mt-0.36rem ml-0.06rem" />
        <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
        <!-- <LineChart /> -->
        <!--  -->
        <!-- <LineChart :dataSource="state.lineChartData" :year="year" class="mt-0.36rem ml-0.14rem" /> -->
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="ReservoirInformation">
  import { getDrawCount, getTankWaterLevel } from '../services'
  import { getValueByKey, getOptions } from '@/api'
  import LineChart from './LineChart.vue'
  import { useUserStore } from '@/store'
  const userStore = useUserStore()
  const state = $ref({
    linkList: [],
    lineChartData: [],
    reservoirInfo: [],
    configInfo: {},
    yearList: [],
  })
  const year = ref(null)
  const router = useRouter()
  onMounted(() => {
    // unmountLoading()
    year.value = new Date().getFullYear()
    generateYearList()
    init()
  })
  watch(
    year,
    newVal => {
      if (newVal) {
        init()
      }
    },
    { immediate: true },
  )
  //孪生水库
  const toTwinReservoir = () => {
    router.push('/twin-reservoir')
  }
  const generateYearList = () => {
    const currentYear = new Date().getFullYear()
    const startYear = 2023 // 生成从当前年份到前6年的年份数组
    for (let year = currentYear; year >= startYear; year--) {
      state.yearList.push({ label: String(year), value: String(year) })
    }
  }
  const init = () => {
    getValueByKey('thjParamConfig').then(res => {
      state.configInfo = JSON.parse(res.data)
    })
    getValueByKey('thjCapacity').then(res => {
      state.reservoirInfo.storageCapacity = res.data
    })
    getTankWaterLevel().then(res => {
      state.reservoirInfo.waterLevel = res.data?.waterLevel
      // state.reservoirInfo.storageCapacity = res.data?.storageCapacity
    })
    getOptions('screenJumpLink').then(res => {
      state.linkList = res.data
    })
    let param = { year: year.value }
    getDrawCount(param).then(res => {
      state.lineChartData = [
        {
          xData: res.data?.map(el => el.time),
          data: res.data?.map(el => [el.time, el.value]), // [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
        },
      ]
    })
    // state.lineChartData = [
    //   {
    //     xData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     data: [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
    //   },
    // ]
  }

  const jumpTo = val => {
    state.linkList.forEach(item => {
      if (item.key == val) {
        const url = `${item.value}?token=${userStore.token}`
        window.open(url, '_blank')
      }
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  .construction {
    margin-bottom: 0;
    width: 1.28rem;
    height: 0.32rem;
    padding: 0.02rem 0;
    border-radius: 4px;
    line-height: 0.32rem;
    justify-content: center;
    // background: red;
    background: linear-gradient(273deg, rgba(114, 204, 240, 0.1) 3%, rgba(98, 188, 219, 0) 99%);
  }
</style>
