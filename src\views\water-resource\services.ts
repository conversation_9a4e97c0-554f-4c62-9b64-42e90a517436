// 水库放水统计
export function getDrawCount(params) {
  return request({
    url: '/view/resource/getDrawCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水电站日发电量
export function getGeneratedEnergyCount(params) {
  return request({
    url: '/view/resource/getGeneratedEnergyCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 灌溉用水统计
export function getIrrigateCount(params) {
  return request({
    url: '/view/resource/getIrrigateCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 灌溉用水统计--按天
export function getDayIrrigateCount(params) {
  return request({
    url: '/view/resource/getDayIrrigateCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
// 灌溉用水统计--按管理所
export function getThatIrrigate(params) {
  return request({
    url: '/view/resource/getThatIrrigate',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水库调度概化图
export function getResourceConsumptionList(params) {
  return request({
    url: '/view/resource/getConsumptionList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 灌区水雨情--灌区水资源管理
export function getConsumptionList(data) {
  return request({
    url: '/view/home/<USER>',
    method: 'post',
    data,
  })
}

// 灌区水雨情--渠首水位监测
export function getHeadWaterLevel() {
  return request({
    url: '/view/home/<USER>',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 灌区水雨情--桃花江水库
export function getTankWaterLevel() {
  return request({
    url: '/view/home/<USER>',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 水利对象分类-根据分类统计对象
export function objectCategoryCountByFirstLevel(data) {
  return request({
    url: '/base/objectCategory/countByFirstLevel',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}
// 水利对象分类-一级分类
export function objectCategoryFirstLevelList() {
  return request({
    url: '/base/objectCategory/firstLevel/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//工程运行状态
export function getProjectDeviceList(params) {
  return request({
    url: '/view/intelligent/getProjectDeviceList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//进行中的工单--巡检记录
export function getPatrolList(data) {
  return request({
    url: '/view/intelligent/getPatrolList',
    method: 'post',
    data,
  })
}
//进行中的工单--维养记录
export function getMaintenanceRecordList(data) {
  return request({
    url: '/view/intelligent/getMaintenanceRecordList',
    method: 'post',
    data,
  })
}
//进行中的工单--巡检记录
export function getEmergencyList(data) {
  return request({
    url: '/view/intelligent/getEmergencyList',
    method: 'post',
    data,
  })
}
// 工程巡检及养护
export function getPatrolCount(params) {
  return request({
    url: '/view/intelligent/getPatrolCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 信息化工程信息总览--图表
export function getProjectChart() {
  return request({
    url: '/view/intelligent/getProjectChart',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
// 信息化工程信息总览--设备统计
export function getDeviceCount() {
  return request({
    url: '/view/intelligent/getDeviceCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//视频列表
export function getAllCameraList() {
  return request({
    url: '/base/camera/getAllCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 行政区划树
export function districtGetTree() {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 水利对象分类-列表分页查询
export function objectCategoryObjectPage(data) {
  return request({
    url: '/base/objectCategory/object/page',
    method: 'post',
    data,
  })
}

//视频监控-根据水利对象查询视频点信息
export function getVideoById(data: object) {
  return request({
    url: '/base/camera/object/getCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}

// 水利对象分类-根据对象统计监控指标
export function listByIds(data) {
  return request({
    url: '/base/objectCategory/object/listByIds',
    method: 'post',
    data,
  })
}

// 获取视频播放地址
export function getVideoAddress(params) {
  return request({
    url: '/external/easyCvr/getPlayOnUrl',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
