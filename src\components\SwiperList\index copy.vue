<template><component :is="render()"></component></template>

<script setup lang="tsx" name="SwiperList">
  import { debounce } from 'lodash-es'

  // Import Swiper Vue.js components
  import { Swiper, SwiperSlide } from 'swiper/vue'

  import { Autoplay, Scrollbar, Mousewheel } from 'swiper/modules'
  // Import Swiper styles
  import 'swiper/css'
  // import "swiper/css/scrollbar";

  //自动轮播的配置
  const autoplayOptions = {
    delay: 2000,
    disableOnInteraction: false,
    pauseOnMouseEnter: true,
    reverseDirection: false,
  }

  const modules = [Autoplay, Scrollbar, Mousewheel]

  const slidesPerView = ref(null)

  const props = withDefaults(
    defineProps<{
      dataSource: any[]
      columns: any[]
      isShowHeader?: boolean
    }>(),
    {
      dataSource: () => [],
      columns: () => [],
      isShowHeader: true,
    },
  )

  const attrs = useAttrs()

  const onTableResize = ({ contentRect }) => {
    const rootFontSize = +getComputedStyle(window.document.documentElement)['font-size'].slice(0, -2)

    // 当前单行高度
    const currentRowHeight = rootFontSize / (100 / 40)

    slidesPerView.value = Math.floor(contentRect.height / currentRowHeight)
  }

  const render = () => (
    <div class='size-full'>
      {props.isShowHeader && (
        <div class='flex mb-8px title text-[#699DB2]'>
          {props.columns.map(col => (
            <div class='px-0.04rem text-0.14rem' style={{ width: col.width, textAlign: col.align || 'left' }}>
              {col.title}
            </div>
          ))}
        </div>
      )}

      <div class='h-[calc(100%-0.32rem)] text-[#fff] text-0.12rem' v-resize={debounce(onTableResize, 500)}>
        {slidesPerView.value ? (
          <Swiper
            style='height: 100%; width: 100%'
            slidesPerView={slidesPerView.value}
            spaceBetween={0}
            modules={modules}
            direction='vertical'
            loop={true}
            autoplay={autoplayOptions}
            mousewheel={{ mousewheel: true, eventsTarged: '.swiper' }}
            scrollbar={{ draggable: false }}
          >
            {props.dataSource.map((item, index) => (
              <SwiperSlide>
                <div
                  class='h-full flex items-center'
                  style={{ background: index % 2 ? 'rgba(63, 94, 144, 0.14)' : 'transparent' }}
                >
                  {props.columns.map((col, j) =>
                    col.render ? (
                      <div
                        key={index + '-' + j}
                        class='px-0.04rem'
                        style={{ width: col.width, textAlign: col.align || 'center' }}
                      >
                        {col.render(item, index)}
                      </div>
                    ) : (
                      <div key={col.field}>{item[col.field]}</div>
                    ),
                  )}
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        ) : (
          ''
        )}
      </div>
    </div>
  )
</script>

<style lang="scss" scoped>
  .title {
    height: 0.32rem;
    line-height: 0.32rem;
    background: linear-gradient(92deg, rgba(63, 94, 144, 0.32) 12%, rgba(64, 88, 124, 0.25) 87%);
    box-shadow: inset 0px -1px 2px 0px rgba(255, 255, 255, 0.2);
  }
</style>
