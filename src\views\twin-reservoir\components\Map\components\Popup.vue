<template>
  <h3
    class="w-2.58rem h-0.42rem lh-0.42rem flex uno-bg_twin-reservoir/top-bg-258.png"
    :class="{ 'w-5.8rem': props.popupCode == 'rain-water', 'w-3.3rem': props.popupCode == 'video' }"
  >
    <label class="pl-0.16rem text-0.18rem font-500 c-#fff text-overflow1">{{ props?.popupName }}</label>
    <span class="text-0.18rem c-#fff font-500 ml-auto mr-0.1rem cursor-pointer" @click="close">×</span>
  </h3>
  <div class="w-5.8rem h-3.9rem popup-card" v-show="props.popupCode == 'rain-water'">
    <WaterRainChart :dataSource="state.list" :bottomIndexCodes="['waterLevel', 'flow']" class="mt-0.1rem" />
  </div>
  <div class="w-2.58rem min-h-1.04rem popup-card p-0.03rem pt-0.08rem" v-if="props.popupCode === 'hydropower-station'">
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">今日开启设备</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">{{ state.hydropower?.deviceNames || '--' }}</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">预计发电量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">
        {{ state.hydropower?.warnFlow == null ? '--' : state.hydropower?.warnFlow + ' kw·h' }}
      </span>
    </div>
  </div>
  <div class="w-2.58rem max-h-2.04rem popup-card p-0.08rem overflow-x-auto" v-else-if="props.popupCode === 'management-office'">
    <label class="text-0.14rem c-#B2DAEA font-350">简介：</label>
    <span class="text-0.14rem c-#fff font-350">{{ state.introduction }}</span>
  </div>

  <div class="w-2.58rem min-h-1.04rem popup-card p-0.03rem pt-0.08rem" v-else-if="props.popupCode === 'measure-water'">
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">当前流量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">3 m³/s</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">今日累计流量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">100 m³</span>
    </div>
  </div>

  <div
    class="w-2.58rem min-h-1.04rem popup-card p-0.03rem pt-0.08rem"
    v-else-if="props.popupCode === 'sluice-gate1' || props.popupCode === 'sluice-gate2'"
  >
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">闸门宽度</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">5 m</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">最大放水量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">330 m³/s</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">闸门型号</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">--</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">闸门材质</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">--</span>
    </div>
  </div>

  <div class="w-2.58rem min-h-1.04rem popup-card p-0.03rem pt-0.08rem" v-else-if="props.popupCode === 'generator-set'">
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">设备数量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">3台</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">最高日发电量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">330 kw.h</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">最高日发电量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">330 kw.h</span>
    </div>
    <div class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem">
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">最高日发电量</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">330 kw.h</span>
    </div>
  </div>

  <div class="w-2.58rem min-h-1.04rem popup-card p-0.03rem pt-0.08rem" v-else-if="props.popupCode.includes('AQJC')">
    <div
      class="h-0.28rem flex bg-[rgba(104,150,173,0.3)] border-rd-0.02rem mb-0.08rem p-0.06rem"
      v-for="(el, i) in state.sectionIndex"
      :key="i"
    >
      <label class="w-1.1rem text-0.14rem c-#B2DAEA font-350">{{ el?.indexName }}</label>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-auto">{{ el?.fieldValue }}</span>
      <span class="text-0.14rem c-#09FCC7 font-350 ml-0.06rem">{{ el?.unit }}</span>
    </div>
    <div class="text-0.14rem c-#B2DAEA">监测时间:{{ state.sectionIndex[0]?.dateTime }}</div>
  </div>

  <div class="w-3.3rem h-1.94rem popup-card p-0.03rem pt-0.08rem" v-show="props.popupCode === 'video'">
    <videoEasyPlayer class="w-2.58rem h-1.94rem" :channel="62" :device="62"></videoEasyPlayer>
  </div>
</template>
<script setup lang="ts" name="WaterForecast">
  import { getValueByKey } from '@/api/common'
  import { getVideoAddress } from '@/views/home/<USER>'
  import { getIndexByCode, getTodayHydropower, getSectionByCode } from '../../../services'
  import dayjs from 'dayjs'
  import WaterRainChart from './WaterRainChart.vue'
  import { dealNumber } from '@/utils'
  import { getFixedNum } from '@/utils/dealNumber.js'
  import videoEasyPlayer from '@/components/videoEasyPlayer/index.vue'

  const props = defineProps({
    popupCode: String,
    popupName: String,
    popupType: String,
  })

  watch(
    () => props.popupCode,
    newVal => {
      if (newVal === 'sluice-gate1') {
      } else if (newVal === 'hydropower-station') {
        getHydropower()
      } else if (newVal === 'management-office') {
        getIntroduction()
      } else if (newVal.includes('AQJC')) {
        getSection()
      }
    },
  )

  const state = reactive({
    list: [],
    hydropower: {},
    introduction: '',
    videoUrl: '',

    sectionIndex: {},
  })
  onMounted(() => {
    getRainWater()
  })

  const getRainWater = () => {
    getIndexByCode({
      endTime: dayjs().format('YYYY-MM-DD HH:mm'),
      indexCodes: ['waterLevel', 'rainfall'],
      siteId: 84,
      startTime: dayjs().subtract(24, 'hours').format('YYYY-MM-DD HH:mm'),
      type: 1,
    }).then(res => {
      const list = (res.data || []).map(el => {
        return {
          ...el,
          rain: getFixedNum(el.rain, 1),
          sumRain: getFixedNum(el.sumRain, 1),
          waterLevel: getFixedNum(el.waterLevel, 2),
          flow: dealNumber(el.flow, 3),
          flowWaterLevel: getFixedNum(el.flowWaterLevel, 2),
        }
      })
      state.list = list
    })
  }

  const getHydropower = () => {
    getTodayHydropower().then(res => {
      state.hydropower = res?.data
    })
  }

  const getIntroduction = () => {
    getValueByKey('managementOffice').then(res => {
      state.introduction = res?.data
    })
  }

  //
  const getSection = () => {
    getSectionByCode([{ objectCode: props.popupCode, objectType: 'MS' }]).then(res => {
      state.sectionIndex = res?.data[0]?.indexFields
    })
  }

  const emits = defineEmits(['close-popup'])
  const close = () => {
    emits('close-popup')
  }
</script>
<style lang="scss" scoped>
  .water-rain {
    background: url('@/assets/images/twin-reservoir/top-bg-430.png') no-repeat center / 100% 100%;
  }
  .popup-card {
    border-radius: 0.08rem;
    background: linear-gradient(180deg, rgba(12, 32, 41, 0.8) 0%, rgba(31, 83, 106, 0.8) 100%);
    border: 1px solid;
    border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%);
  }
</style>
