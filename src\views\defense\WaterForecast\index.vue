<template>
  <MyCard name="水旱灾害预警">
    <div class="content pt-0.1rem h-6.4rem flex flex-col relative">
      <div
        class="absolute w-0.96rem h-0.24rem top--0.27rem right-0.01rem cursor-pointer"
        :style="{ background: `url(${getImageUrl('defense/more.png')}) no-repeat center / 100% 100%` }"
        @click="jumpTo(1)"
      ></div>
      <div class="mt--0.1rem flex-col">
        <div class="h-1.44rem flex mt-0.1rem">
          <!-- :style="{ background: `url(${getImageUrl('defense/early-warning-bg.png')}) no-repeat center / 100% 100%` }" -->
          <div
            class="w-1.44rem h-1.1rem ml-0.16rem mt-0.2rem relative"
            :style="{ background: `url(${getImageUrl(`defense/drought-bg${state.level}.png`)}) no-repeat center / 100% 100%` }"
          >
            <div
              class="animation absolute h-1.12rem w-1.12rem top--0.01rem left-0.16rem"
              :style="{ background: `url(${getImageUrl(`defense/drought${state.level}.png`)}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="absolute flex flex-col w-0.8rem h-0.8rem top-0.25rem left-0.32rem text-center">
              <!-- text-0.32rem font-normal lh-normal -->
              <div class="text-0.32rem text-[#fff]">
                {{ state.day }}
                <!-- text-0.14rem font-500 lh-normal text-[#fff] -->
                <span class="text-0.14rem font-500 lh-normal text-[#fff]">天</span>
              </div>
              <div class="text-0.15rem lh-normal text-[#B2DAEA]">预警时长</div>
            </div>
            <!-- <PieChart :dataSource="state.pieChartData" key="1" class="absolute top--0.15rem left--0.1rem" /> -->
          </div>
          <div class="w-2.34rem h-1.1rem flex flex-col ml-0.2rem mt-0.2rem">
            <div class="w-2.34rem h-0.28rem bg-[rgba(128,177,197,0.1)] flex items-center my-0.05rem relative">
              <div
                class="absolute left-0px top-0.1rem w-0.075rem h-0.18rem"
                :style="{ background: `url(${getImageUrl('defense/early-warning-bg-icon.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="text-[#B2DAEA] text-0.14rem ml-0.1rem">预警编码</div>
              <div class="text-[#fff] text-0.16rem ml-auto mr-0.1rem">{{ state.drought?.warnCode }}</div>
            </div>
            <div class="w-2.34rem h-0.28rem bg-[rgba(128,177,197,0.1)] flex items-center my-0.05rem relative">
              <div
                class="absolute left-0px top-0.1rem w-0.075rem h-0.18rem"
                :style="{ background: `url(${getImageUrl('defense/early-warning-bg-icon.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <div class="text-[#B2DAEA] text-0.14rem ml-0.1rem">预警类型</div>
              <div class="text-[#fff] text-0.16rem ml-auto mr-0.1rem">{{ state.drought?.warnType }}</div>
            </div>
            <!-- background: #80B1C5;  bg-[rgba(128,177,197,0.3)]-->
            <div class="w-2.34rem h-0.28rem bg-[rgba(128,177,197,0.1)] flex items-center my-0.05rem relative">
              <div class="absolute left-0px top-0.1rem w-0.075rem h-0.18rem uno-bg_defense/early-warning-bg-icon.png"></div>
              <div class="text-[#B2DAEA] text-0.14rem ml-0.1rem">预警时间</div>
              <div class="text-[#fff] text-0.16rem ml-auto mr-0.1rem">{{ state.drought?.dt }}</div>
            </div>
          </div>
        </div>

        <div class="h-2.4rem w-4rem m-0.15rem border-rd-0.04rem relative uno-bg_defense/map-bg.png">
          <!-- h-1.4rem -->
          <div
            class="absolute bottom-0.1rem left-0.1rem w-0.7rem h-1.2rem border-rd-4px op-100 bg-[rgba(18,33,41,0.4)] flex flex-col items-center z-999"
          >
            <div class="text-[#699DB2] text-0.12rem text-center mt-0.1rem">旱情等级</div>
            <div
              class="text-[#B2DAEA] text-0.14rem text-center mt-0.06rem flex"
              v-for="(el, i) in state.droughtLevelOptions"
              :key="i"
            >
              <!-- :style="{ background: defenseColor[i] }" -->
              <span class="w-0.16rem h-0.1rem border-rd-0.02rem mt-0.03rem mr-0.05rem" :style="{ background: el.option1 }"></span>
              {{ el.value }}
            </div>
          </div>
          <div
            class="absolute w-1rem h0.73rem top-0.56rem left-2.74rem uno-bg_defense/drought5-icon.png z-9999999"
            v-if="state.level == 5"
          ></div>
          <!-- 地图加载图层 -->

          <div class="w-full h-full">
            <!-- <Mapbox :onMapMounted="onMapMounted" /> -->
            <Mapbox :onMapMounted="onMapMounted" :onMapZoomEnd="onMapZoomEnd" :mapZoom="state.zoom" />
          </div>
        </div>
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="WaterForecast">
  import { getModelDroughtPage, getModelDrought } from '../services'
  import initMap from './initMap.js'
  import { getOptions, getValueByKey } from '@/api'
  import axios from 'axios'
  import { defenseColor, defenses } from '@/constants'
  import dayjs from 'dayjs'
  import * as turf from '@turf/turf'
  import { useUserStore } from '@/store'
  const userStore = useUserStore()
  const state = $ref({
    linkList: [],
    day: 0,
    level: 1,
    pieChartData: [],
    drought: {
      warnCode: '-',
      dt: '-',
      warnType: '气象干旱',
    },
    param: {
      endTime: '',
      pageNum: 1,
      pageSize: 10,
      sort: [],
      startTime: '',
      subtp: 7,
      tp: 1,
    },
    zoom: 4,
    droughtLevelOptions: [],
    // center: [112.032323, 28.437094], // [111.990857, 28.456976], //[112.032323, 28.437094],
    mapIns: null,
    layers: {
      MultiPolygon: [],
      MultiLineString: [],
      Point: [],
    },
  })

  const jumpTo = val => {
    state.linkList.forEach(item => {
      if (item.key == val) {
        const url = `${item.value}?token=${userStore.token}`
        window.open(url, '_blank')
      }
    })
  }
  const onMapMounted = mapIns => {
    state.mapIns = mapIns
    nextTick(() => {
      initMap(state.mapIns)
      state.mapIns.resize()
    })
    // initMap(state.mapIns)
  }
  const onMapZoomEnd = currentZoom => {
    state.zoom = currentZoom
  }
  onMounted(() => {
    init()
  })
  const init = () => {
    getOptions('screenJumpLink').then(res => {
      state.linkList = res.data
    })
    getOptions('droughtLevel').then(resLevel => {
      state.droughtLevelOptions = resLevel.data
      console.log('气象干旱 level ', state.droughtLevelOptions)
    })
    getModelDroughtPage(state.param).then(res => {
      // console.log('气象干旱 179 droughtLevelOptions', res, state.drought)
      state.drought = res?.data?.data?.[0]

      let param = {
        dt: state.drought?.dt,
        subtp: state.drought?.subtp,
        tp: state.drought?.tp,
      }

      if (!state.drought?.dt || !state.drought?.subtp) {
        return
      }

      getModelDrought(param).then(res2 => {
        let startTime = dayjs(state.drought?.dt)
        let day = dayjs().diff(startTime, 'day')

        state.day = day <= 7 ? day : 0

        let lvSet = new Set(res2.data?.map(item => item.lv))

        if (day > 7) {
          state.level = 5
          state.drought = {
            warnCode: '-',
            dt: '-',
            warnType: '-',
          }
        } else {
          if (lvSet.has(1)) {
            state.level = 1
          } else if (lvSet.has(2) && !lvSet.has(1)) {
            state.level = 2
          } else if (lvSet.has(3) && !lvSet.has(1) && !lvSet.has(2)) {
            state.level = 3
          } else {
            state.level = 4
          }
          state.drought.warnType = '气象干旱'
        }

        state.pieChartData = [
          { value: 60 },
          { value: 40, name: defenses[state.level]?.name, itemStyle: { color: defenses[state.level]?.color } },
        ]
      })
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);

    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  .animation {
    animation: rotate 16s linear infinite;
  }
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
