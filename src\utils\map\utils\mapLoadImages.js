export default (mapIns, urls, isAddImg, callback) => {
  let results = {}
  let isAdd = isAddImg === false ? false : true

  if (Object.keys(urls).every(el => mapIns.hasImage(el))) {
    callback()
    return
  }
  for (let name in urls) {
    mapIns.loadImage(urls[name], makeCallback(name))
  }
  function makeCallback(name) {
    return function (err, image) {
      results[name] = err ? null : image
      // if all images are loaded, call the callback
      if (Object.keys(results).length === Object.keys(urls).length) {
        if (isAdd === false) {
          callback(results)
        } else {
          Object.keys(results).forEach(el => {
            if (!mapIns.hasImage(el)) mapIns.addImage(el, results[el])
          })
          callback(results)
        }
      }
    }
  }
}

// // 图片URL数组
// const imageURLs = [
//   'https://example.com/image1.png',
//   'https://example.com/image2.png',
//   'https://example.com/image3.png',
// ];

// // 创建一个 Promise 数组
// const imagePromises = imageURLs.map(url => {
//   return new Promise((resolve, reject) => {
//     const img = new Image();
//     img.onload = () => resolve(img);
//     img.onerror = () => reject(new Error(`Failed to load image ${url}`));
//     img.src = url;
//   });
// });

// // 并行加载图片
// Promise.all(imagePromises)
//   .then(images => {
//     // 所有图片加载完成后的处理逻辑
//     // 在这里可以使用 Mapbox API 的 loadImage 方法加载地图纹理等
//     // 例如：map.loadImage(images[0], (error, image) => { ... });
//   })
//   .catch(error => {
//     // 处理加载失败的情况
//     console.error(error);
//   });
