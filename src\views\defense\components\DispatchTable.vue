<template>
  <h4 class="text-0.16rem font-500 c-#09C8FC mb-0.08rem">调度结果</h4>
  <ScaleBox :style="{ width: '1330px', height: '250px' }">
    <n-data-table :columns="columns" :data="attrs?.tableData" max-height="250px" />
  </ScaleBox>
</template>
<script setup lang="ts" name="DispatchTable">
  import { h } from 'vue'
  import { NButton, NTag, useMessage } from 'naive-ui'

  const attrs = useAttrs()

  const columns = [
    {
      title: '预报时间',
      key: 'tm',
      minWidth: 50,
    },
    {
      title: '水位(m)',
      key: 'wlv',
      minWidth: 120,
      sorter: 'default',
      renderSorterIcon: ({ order }) => {
        const style =
          'width: 72px;height: 24px;border-radius: 2px;text-align:center;color: #F6CC57;background: rgba(246, 204, 87, 0.2);border: 1px solid #F6CC57;'
        if (attrs?.dispatchInfo?.dispathMode === 1) return h('div', { style }, ['控制变量'])
      },
    },

    {
      title: '库容(百万m³)',
      key: 'storage',
      minWidth: 50,
    },
    {
      title: '时段降雨量(mm)',
      key: 'rain',
      minWidth: 50,
    },
    {
      title: '入库流量(m³/s)',
      key: 'inflow',
      minWidth: 50,
    },
    {
      title: '入库水量(万m³)',
      key: 'inWater',
      minWidth: 50,
    },
    {
      title: '出库流量(m³/s)',
      key: 'outflow',
      minWidth: 150,
      sorter: 'default',
      renderSorterIcon: ({ order }) => {
        const style =
          'width:72px;height:24px;border-radius: 2px;text-align:center;color: #F6CC57;background: rgba(246, 204, 87, 0.2);border: 1px solid #F6CC57;'

        if (attrs?.dispatchInfo?.dispathMode === 2) return h('div', { style }, ['控制变量'])
      },
    },
  ]
</script>
<style lang="scss" scoped>
  :deep(.n-data-table.n-data-table--bordered .n-data-table-wrapper) {
    border-color: rgba(105, 157, 178, 0.6) !important;
  }
  :deep(.n-data-table .n-data-table-thead) {
    border-color: rgba(105, 157, 178, 0.5) !important;
  }
  :deep(.n-data-table .n-data-table-th, .n-data-table .n-data-table-td) {
    border-color: rgba(105, 157, 178, 0.4) !important;
  }
  :deep(.n-data-table .n-data-table-tr, .n-data-table .n-data-table-td) {
    border-color: rgba(105, 157, 178, 0.2) !important;
  }
  :deep(.n-data-table .n-data-table-td) {
    border-color: rgba(105, 157, 178, 0.2) !important;
  }
</style>
