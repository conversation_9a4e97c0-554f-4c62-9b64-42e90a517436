<template>
  <ScaleBox :style="{ width: '480px', height: '280px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="ts" name="DispatchChart">
  import * as echarts from 'echarts/core'
  import dayjs from 'dayjs'
  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      // console.log('雨水情 chart', newVal)
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  function getConfig(list) {
    const serArr = []

    list.forEach((item, index) => {
      if (item.name === '雨量') {
        serArr.push({
          name: '雨量',
          color: '#09FCC7',
          type: 'bar',
          xAxisIndex: 0,
          yAxisIndex: 0,
          barMaxWidth: 10,
          showBackground: false,
          hoverAnimation: true, // 悬浮的动画加上
          data: item.data,
        })
      }

      if (item.name === '累计雨量') {
        serArr.push({
          name: '累计雨量',
          color: '#F6CC57',
          type: 'line',
          xAxisIndex: 0,
          yAxisIndex: 1,
          smooth: true,
          symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          data: item.data,
        })
      }

      if (item.name === '水位') {
        serArr.push({
          name: '水位',
          color: '#09C8FC',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 2,
          smooth: true,
          symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          data: item.data,
        })
      }
    })

    return {
      grid: [
        // 配置第一个柱状图的位置
        {
          left: '80',
          right: '80',
          top: '30',
          height: '30%',
        },
        // 配置第二个折线图位置
        {
          left: '80',
          right: '80',
          top: '53%',
          height: '30%',
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
            height: 10,
          },
          crossStyle: { color: '#1664FF' },
          lineStyle: { color: '#1664FF' },
        },

        // formatter函数动态修改tooltip样式
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '/') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = param.color //图例颜色

              function getUnit(seriesName) {
                switch (seriesName) {
                  case '雨量':
                    return 'mm'
                  case '累计雨量':
                    return 'mm'
                  case '水位':
                    return 'm'
                  default:
                    return
                }
              }
              // background:rgba(85, 130, 148,0.8);
              htmlStr += `
                    <div style=" border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        // backgroundColor: 'rgba(85, 130, 148, 0.3)',
        // borderWidth: 0,
        // borderColor: '#699DB2',
        // padding: 10,
        // textStyle: {
        //   color: '#DEF2FC',
        //   fontSize: 14,
        //   fontWeight: 500,
        //   align: 'left',
        // },
      },
      legend: {
        show: true,
        x: 'center',
        y: '1',
        data: ['雨量', '累计雨量', '水位'],

        textStyle: {
          fontSize: 12,
          color: 'rgba(222, 242, 252, 1)',
        },
      },
      // 将上下两个tootip合成一个
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      xAxis: [
        {
          type: 'category',
          position: 'top',
          scale: true,
          axisLabel: {
            show: false,
          },
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
            lineStyle: {
              // type: 'dashed',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
        },
        {
          gridIndex: 1,
          type: 'category',
          scale: true,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          axisLabel: {
            color: 'rgba(222, 242, 252, 1)',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '雨量(mm)',
          nameLocation: 'middle',
          nameGap: 60,
          nameRotate: 270,
          inverse: true,
          nameTextStyle: {
            fontSize: 12,
            color: 'rgba(222, 242, 252, 1)',
            // padding: [0, 0, 0, -100], // 上右下左与原位置距离
          },
          axisLabel: {
            fontSize: 12,
            color: 'rgba(105, 157, 178, 1)',
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              // type: 'dashed',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          alignTicks: true, // 配置多坐标轴标签对齐
          // splitNumber: 4, //设置坐标轴的分割段数
        },
        {
          type: 'value',
          name: '累计雨量(mm)',
          inverse: true,
          nameLocation: 'middle',
          nameGap: 60,
          nameRotate: 270,
          nameTextStyle: {
            // padding: [-140, 0, 0, 0], // 上右下左与原位置距离
            fontSize: 12,
            color: 'rgba(222, 242, 252, 1)',
          },
          axisLabel: {
            fontSize: 12,
            color: 'rgba(105, 157, 178, 1)',
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          alignTicks: true, // 配置多坐标轴标签对齐
          splitNumber: 4, //设置坐标轴的分割段数
        },
        {
          type: 'value',
          name: '水位(m)',
          nameLocation: 'middle',
          nameGap: 60,
          nameRotate: 270,
          nameTextStyle: {
            // padding: [0, 0, 0, -115], // 上右下左与原位置距离
            fontSize: 12,
            color: 'rgba(222, 242, 252, 1)',
          },
          gridIndex: 1,
          axisLabel: {
            fontSize: 12,
            color: 'rgba(105, 157, 178, 1)',
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },

          alignTicks: true, // 配置多坐标轴标签对齐
          splitNumber: 4, //设置坐标轴的分割段数
        },
      ],
      dataZoom: [
        {
          show: false,
          type: 'inside',
          xAxisIndex: [0, 1], // 显示 0 1 的数据，这个要加，不加的话，悬浮提示就会出问题
        },
      ],

      series: serArr,
    }
  }
</script>

<style lang="scss" scoped></style>
