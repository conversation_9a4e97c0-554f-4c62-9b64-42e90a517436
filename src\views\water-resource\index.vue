<template>
  <div class="h-full w-full reactive">
    <div class="w-4.3rem absolute top-0.8rem left-0.2rem bottom-0.2rem flex-col z-2">
      <ReservoirInformation class="mb-0.2rem" />
      <PowerGenerationStatistics class="mb-0.2rem flex-1" />
    </div>
    <div class="w-4.3rem absolute top-0.8rem right-0.2rem bottom-0.2rem flex-col z-4">
      <ReservoirGeneralizedMap class="mb-0.2rem" />
      <IrrigationWaterStatistics class="mb-0.2rem flex-1" />
    </div>

    <div class="w-3rem absolute left-4.6rem bottom-0.7rem flex-col z-4">
      <MapLegend v-model:activeItem="activeItem" v-model:allData="allData" />
    </div>

    <Map
      :isShow="isShow"
      :allData="allData"
      v-model:activeItem="activeItem"
      v-model:activeTabLevel2="activeTabLevel2"
      :currentDistrict="currentDistrict"
    />
    <!-- :isDataMode="isDataMode" -->
  </div>
</template>
<script setup lang="tsx" name="WaterResource">
  import { reactive, ref, onBeforeMount, onMounted } from 'vue'
  import { unmountLoading } from '@/core/loading'

  import Map from './Map'
  import MapLegend from './MapLegend'
  import ReservoirInformation from './ReservoirInformation'
  import ReservoirGeneralizedMap from './ReservoirGeneralizedMap'
  import PowerGenerationStatistics from './PowerGenerationStatistics'
  import IrrigationWaterStatistics from './IrrigationWaterStatistics'
  const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
  })

  let activeTabLevel2 = $ref(null)
  // 所有数据--右侧选中变化
  let allData = $ref(null)
  let activeItem = $ref(null)
  onMounted(() => {
    // unmountLoading()
  })
  let currentDistrict = $ref(null)
</script>
<style lang="scss" scoped></style>
