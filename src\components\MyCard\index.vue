<template><component :is="render()"></component></template>

<script setup lang="jsx" name="MyCard">
  const attrs = useAttrs()
  const slots = useSlots()

  const render = () => (
    <div class=' box overflow-hidden flex' style={{ 'flex-direction': slots.headerLeft || attrs.name ? 'column' : 'row' }}>
      {(slots.headerLeft || attrs.name) && (
        <div flex flex-center-between>
          {slots.headerLeft
            ? slots.headerLeft()
            : attrs.name && (
                <div class='name font-[KwangLiangWine]'>
                  <div class='name-icon'></div>
                  <div>{attrs.name}</div>
                </div>
              )}

          {slots.headerRight && slots.headerRight()}
        </div>
      )}

      {slots.default()}
    </div>
  )
</script>

<style lang="scss" scoped>
  .box {
    // background: rgba(12, 23, 33, 0.6);
    // background: rgba(12, 23, 33, 0.1);
    // position: absolute;
    // opacity: 1;

    // background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);

    // box-sizing: border-box;
    // border: 1px solid rgba(54, 96, 103, 0.5);

    // backdrop-filter: blur(0.2rem);
    // border-radius: 0 0 0.08rem 0.08rem;
    // padding: 0.16rem;
    // padding-top: 0.2rem;
    padding-top: 0rem;
    z-index: 5;
    // z-index: 999;

    .name {
      font-family: SourceHanSansCN-Medium;

      // font-family: SourceHanSansCN-Normal SourceHanSansCN-Medium PangMenZhengDao;
      font-size: 0.18rem;
      font-weight: normal;
      line-height: normal;
      letter-spacing: 0.09em;

      display: flex;
      align-items: center;
      padding-top: 0.1rem;
      // padding: 0.06rem;
      color: #fff;

      // margin-top: -0.06rem;
      // background: green;
      background: url('@/assets/images/card-title-bg.png') no-repeat center / 100% 100%;
      height: 0.36rem;
      width: 100%;
      // z-index: 5;
      // border-bottom: 1px solid #363e5b;
      .name-icon {
        width: 0.21rem;
        height: 0.34rem;
        margin-right: 0.12rem;
      }
    }
  }
</style>
