<template>
  <MyCard name="水电站日发电量" class="relative">
    <template #headerRight>
      <div class="absolute w-1.8rem right-0.14rem top-0.05rem flex items-center">
        <NaSegmentTabs
          class="w-0.8rem h-0.3rem mr-0.1rem mt-0.04rem"
          v-model:value="state.timeType"
          @update:value="onTimeTypeChange"
          :options="[
            { key: 0, title: '月' },
            { key: 1, title: '年' },
          ]"
        ></NaSegmentTabs>
        <NaSelect
          v-if="state.timeType == 1"
          class="w-0.98rem mt-0.03rem"
          v-model:value="year"
          type="side"
          :show-checkmark="false"
          :options="state.yearList"
        >
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
        <NaSelect
          v-if="state.timeType == 0"
          class="w-0.98rem mt-0.04rem"
          v-model:value="month"
          type="side"
          :show-checkmark="false"
          :options="state.monthList"
        >
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
      </div>
    </template>
    <div class="content pt-0.1rem w-4.3rem h-2.4rem flex relative">
      <BarChart v-if="state.barChartData" :dataSource="state.barChartData" :year="year" />
      <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="PowerGenerationStatistics">
  import { getGeneratedEnergyCount } from '../services'
  import BarChart from './BarChart.vue'
  import dayjs from 'dayjs'

  const state = $ref({
    timeType: 1,
    yearList: [],
    monthList: [
      { label: '1月', value: 1 },
      { label: '2月', value: 2 },
      { label: '3月', value: 3 },
      { label: '4月', value: 4 },
      { label: '5月', value: 5 },
      { label: '6月', value: 6 },
      { label: '7月', value: 7 },
      { label: '8月', value: 8 },
      { label: '9月', value: 9 },
      { label: '10月', value: 10 },
      { label: '11月', value: 11 },
      { label: '12月', value: 12 },
    ],
    barChartData: [],
    reservoirInfo: [],
    configInfo: {},
  })
  const year = ref(null)
  const month = ref(null)
  onMounted(() => {
    // unmountLoading()
    year.value = new Date().getFullYear()
    month.value = new Date().getMonth() + 1
    generateYearList()
    init()
  })

  watch(
    month,
    newVal => {
      if (newVal) {
        init()
      }
    },
    { immediate: true },
  )
  watch(
    year,
    newVal => {
      if (newVal) {
        init()
      }
    },
    { immediate: true },
  )
  const init = () => {
    let param = { time: state.timeType == 1 ? year.value : month.value, type: state.timeType }
    getGeneratedEnergyCount(param).then(res => {
      state.barChartData = [
        {
          type: 0,
          xData: res.data?.map(el => el.time),
          data: res.data?.map(el => [el.time, el.value]), // [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
        },
      ]
    })
  }
  const generateYearList = () => {
    const currentYear = new Date().getFullYear()
    const startYear = 2023 // 生成从当前年份到前6年的年份数组
    for (let year = currentYear; year >= startYear; year--) {
      state.yearList.push({ label: String(year), value: String(year) })
    }
  }
  const onTimeTypeChange = type => {
    state.timeType = type
    if (state.timeType == 0 && !month.value) {
      return ''
    }
    init()
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    // background-color: transparent;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
</style>
