<template>
  <div style="height: 100%; display: flex; position: relative">
    <div style="flex: 1; display: flex; flex-direction: column">
      <div style="flex: 1" class="map-content">
        <div class="m-1px size-[calc(100%-2px)] border-rd-0.08rem overflow-hidden">
          <Mapbox :onMapMounted="onMapMounted" :onMapZoomEnd="onMapZoomEnd" :mapZoom="state.zoom" />
        </div>
      </div>
      <div class="h-0.34rem mt-0.2rem">
        <TimePlaySlider v-if="state.times.length && !!state.mapIns" :times="state.times" @onTimeChange="onTimeChange" />
      </div>
    </div>
    <div style="width: 5rem; margin-left: 0.12rem">
      <SimulationChart
        :width="5"
        v-if="!!state.chartData && !!state.markLineXAxis1"
        :dataSource="state.chartData"
        :markLineXAxis1="state.markLineXAxis1"
        :markLineXAxis2="state.markLineXAxis1 + state.westMainCanalForkIndex"
      />
    </div>
  </div>
</template>

<script setup name="Simulation">
  import TimePlaySlider from '@/components/TimeSlider/index.vue'
  import { MapboxOverlay } from '@deck.gl/mapbox'
  import { getOptions, getValueByKey } from '@/api/common.ts'
  import { mapboxPopup } from './popup/index.js'
  import initLayer from './initLayer.js'
  // import { getChSimResList } from '../../services.ts'
  import SimulationChart from './SimulationChart/index.vue'

  const { chSimId, dataSource, mapData } = defineProps(['chSimId', 'dataSource', 'mapData'])

  const state = reactive({
    zoom: 9,
    mapIns: null,
    mapOverlayIns: null,
    times: [...new Set(mapData[0].resVOS.map(el => el.tm))],
    geojson: null,

    showPopupItem: [],
    currentTime: null,

    chartData: null,
    markLineXAxis1: null,
    westMainCanalForkIndex: null,
  })

  getOptions('scaleProjectCode').then(res => {
    state.showPopupItem = res.data.map(el => ({ projectCode: el.key, projectName: el.value }))
    onTimeChange(state.currentTime)
  })
  getValueByKey('westMainCanalForkIndex').then(res => {
    state.westMainCanalForkIndex = +res.data
  })

  const onMapMounted = mapIns => {
    state.mapIns = mapIns

    nextTick(() => {
      state.mapIns.resize()
    })
    state.mapOverlayIns = new MapboxOverlay({
      id: 'deck-geojson-layer-overlay',
      layers: [],
    })
    state.mapIns.addControl(state.mapOverlayIns)
    dealLayers()
  }

  const onMapZoomEnd = currentZoom => {
    state.zoom = currentZoom
  }

  const onTimeChange = time => {
    state.currentTime = time

    if (mapData.length > 0) {
      let arr = []
      mapData.forEach(el => {
        arr = arr.concat(el.resVOS.find(ele => ele.tm == time)?.records)
      })
      const factArr = arr.filter(el => !!+el?.longitude && !!+el?.latitude)
      if (factArr.length === 0) return
      state.geojson = {
        type: 'FeatureCollection',
        features: factArr.map(el => {
          return {
            type: 'Feature',
            geometry: {
              type: 'Point',
              coordinates: [+el.longitude, +el.latitude],
            },
            properties: {
              ...el,
            },
          }
        }),
      }

      if (!state.mapOverlayIns?._props?.layers?.length > 0) {
        dealLayers()
      }

      handleOpenPopup(factArr)
    }
    if (!!dataSource) {
      state.markLineXAxis1 = dataSource['GQ2701'].bottom.length - 1

      const stakes = dataSource['GQ2701'].stakes.concat(
        dataSource['**********'].stakes
          .slice(0, state.westMainCanalForkIndex)
          .map(el => el + dataSource['GQ2701'].stakes[dataSource['GQ2701'].stakes.length - 1]),
      )

      state.chartData = {
        chart1: [
          {
            name: '水位',
            color: '#72C5F5',
            data: dataSource['GQ2701'].data[state.currentTime + ':00'].wlevel
              .concat(
                dataSource['**********'].data[state.currentTime + ':00'].wlevel.slice(0, dataSource['**********'].bottom.length),
              )
              .map((el, idx) => [idx, el]),
          },
          {
            name: '渠底',
            color: '#D1CBA8',
            data: dataSource['GQ2701'].bottom.concat(dataSource['**********'].bottom).map((el, idx) => [idx, el]),
          },
          {
            name: '流量',
            color: '#F7BA1E',
            yAxisIndex: 1,
            data: dataSource['GQ2701'].data[state.currentTime + ':00'].q
              .concat(dataSource['**********'].data[state.currentTime + ':00'].q.slice(0, dataSource['**********'].bottom.length))
              .map((el, idx) => [idx, el]),
          },
        ],
        chart1Stakes: dataSource['GQ2701'].stakes.concat(
          dataSource['**********'].stakes.map(el => el + dataSource['GQ2701'].stakes[dataSource['GQ2701'].stakes.length - 1]),
        ),
        chart2: [
          {
            name: '水位',
            color: '#72C5F5',
            data: dataSource['GQ2701'].data[state.currentTime + ':00'].wlevel
              .concat(
                dataSource['**********'].data[state.currentTime + ':00'].wlevel.slice(0, dataSource['**********'].bottom.length),
              )
              .map((el, idx) => [idx, el]),
          },
          {
            name: '渠底',
            color: '#D1CBA8',
            data: dataSource['GQ2701'].bottom.concat(dataSource['**********'].bottom).map((el, idx) => [idx, el]),
          },
          {
            name: '流量',
            color: '#F7BA1E',
            yAxisIndex: 1,
            data: dataSource['GQ2701'].data[state.currentTime + ':00'].q
              .concat(dataSource['**********'].data[state.currentTime + ':00'].q.slice(0, dataSource['**********'].bottom.length))
              .map((el, idx) => [idx, el]),
          },
        ],
        chart2Stakes: dataSource['GQ2701'].stakes.concat(
          dataSource['**********'].stakes.map(el => el + dataSource['GQ2701'].stakes[dataSource['GQ2701'].stakes.length - 1]),
        ),
        chart3: [
          {
            name: '水位',
            color: '#72C5F5',
            data: dataSource['GQ2701'].data[state.currentTime + ':00'].wlevel
              .concat(dataSource['**********'].data[state.currentTime + ':00'].wlevel.slice(0, state.westMainCanalForkIndex))
              .concat(dataSource['**********'].data[state.currentTime + ':00'].wlevel)
              .map((el, idx) => [idx, el]),
          },
          {
            name: '渠底',
            color: '#D1CBA8',
            data: dataSource['GQ2701'].bottom
              .concat(dataSource['**********'].bottom.slice(0, state.westMainCanalForkIndex))
              .concat(dataSource['**********'].bottom)
              .map((el, idx) => [idx, el]),
          },
          {
            name: '流量',
            color: '#F7BA1E',
            yAxisIndex: 1,
            data: dataSource['GQ2701'].data[state.currentTime + ':00'].q
              .concat(dataSource['**********'].data[state.currentTime + ':00'].q.slice(0, state.westMainCanalForkIndex))
              .concat(dataSource['**********'].data[state.currentTime + ':00'].q)
              .map((el, idx) => [idx, el]),
          },
        ],
        chart3Stakes: stakes.concat(dataSource['**********'].stakes.map(el => el + stakes[stakes.length - 1])),
      }
    }
  }

  const handleOpenPopup = factArr => {
    if (state.showPopupItem.length > 0 && !!state.mapIns) {
      state.showPopupItem.forEach(el => {
        el?.popupIns?.remove()

        if (factArr.some(ele => ele.projectCode === el.projectCode)) {
          dealPopup(factArr.find(ele => ele.projectCode === el.projectCode))
        }
      })
    }
  }

  const dealLayers = () => {
    if (!!state.mapIns && !!state.geojson) {
      initLayer(state.mapIns, state.mapOverlayIns, state.geojson, item => {
        if (state.showPopupItem.every(el => el.projectCode !== item.projectCode)) {
          dealPopup(item)
        }
      })
    }
  }

  const dealPopup = curr => {
    const popupIns = mapboxPopup(state.mapIns, {
      ...curr,
      lngLat: [+curr.longitude, +curr.latitude],
      onPopupClose: item => {
        const index = state.showPopupItem.findIndex(el => el.projectCode === item.projectCode)
        state.showPopupItem[index].popupIns.remove()
        state.showPopupItem = state.showPopupItem.filter((el, i) => i !== index)
      },
    })

    popupIns.getElement().style['z-index'] = '11'

    let index = state.showPopupItem.findIndex(el => el.projectCode === curr.projectCode)
    if (index === -1) {
      state.showPopupItem.push({ projectCode: curr.projectCode, popupIns })
    } else {
      state.showPopupItem[index] = { ...state.showPopupItem[index], popupIns }
    }
  }
</script>

<style lang="scss" scoped>
  .map-content {
    position: relative;
    border-radius: 0.09rem;
    overflow: hidden;
    padding: 1px;
    &::before {
      content: '';
      position: absolute;
      z-index: 0;
      width: calc(100% - 2px);
      height: calc(100% - 2px);
      border-radius: 0.07rem;
      background: conic-gradient(#09c8fc, rgba(9, 200, 252, 0.2), #09c8fc);
      padding: 1px;
      -webkit-mask:
        linear-gradient(#fff 0 100%) content-box,
        linear-gradient(#fff 0 100%);
      -webkit-mask-composite: xor;
    }
  }
</style>
