import axios from 'axios'
import mapboxgl from 'mapbox-gl'
// styleID should be in the form "mapbox/satellite-v9"
export function switchMapStyle(map, styleID) {
  const currentStyle = map.getStyle()
  axios.get(`https://api.mapbox.com/styles/v1/${styleID}?access_token=${mapboxgl.accessToken}`).then(({ data: newStyle }) => {
    // ensure any sources from the current style are copied across to the new style
    newStyle.sources = Object.assign({}, currentStyle.sources, newStyle.sources)
    // find the index of where to insert our layers to retain in the new style
    let labelIndex = newStyle.layers.findIndex(el => {
      return el.id == 'waterway-label'
    })
    // default to on top
    if (labelIndex === -1) {
      labelIndex = newStyle.layers.length
    }
    const appLayers = currentStyle.layers.filter(el => {
      // app layers are the layers to retain, and these are any layers which have a different source set
      return el.source && el.source != 'mapbox://mapbox.satellite' && el.source != 'mapbox' && el.source != 'composite'
    })
    newStyle.layers = [...newStyle.layers.slice(0, labelIndex), ...appLayers, ...newStyle.layers.slice(labelIndex, -1)]
    map.setStyle(newStyle)
  })
}

// 清空source和layer
export function clearSourceAndLayer(mapIns, sourceIdArr, layerIdArr) {
  //删除图层
  if (layerIdArr.length) {
    layerIdArr.forEach(id => {
      if (mapIns.getLayer(id)) {
        mapIns.removeLayer(id)
      }
    })
  }
  //删除数据源
  if (sourceIdArr.length) {
    sourceIdArr.forEach(id => {
      if (mapIns.getSource(id)) {
        mapIns.removeSource(id)
      }
    })
  }
}
