import { MapboxOverlay } from '@deck.gl/mapbox'
import { GeoJsonLayer } from '@deck.gl/layers'
import { hexToRgb } from '@/utils/map/utils/toDeckglRgb.js'
import * as turf from '@turf/turf'
import { getValueByKey } from '@/api/common'
import axios from 'axios'
import { mapBoundGeo } from '@/utils/map/utils/mapBounds.js'

export default function initMap(mapIns) {
  // 天地图
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-base-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${import.meta.env.VITE_TIANDI_BASE}/img_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[0].id,
  )
  // 天地图标注
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-label-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${import.meta.env.VITE_TIANDI_BASE}/cia_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[1].id,
  )

  // 灌区渐变图
  mapIns.addLayer({
    id: 'wms-test-layer',
    type: 'raster',
    source: {
      type: 'raster',
      tiles: [
        `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq_drought/wms?service=wms&version=1.1.0&request=GetMap&layers=thjgq_drought:20240920&bbox={bbox-epsg-3857}&width=768&height=720&srs=EPSG:3857&styles=&format=image/png&TRANSPARENT=TRUE&exceptions=application/vnd.ogc.se_inimage`,
      ],
      tileSize: 256,
    },
  })

  Promise.all([
    axios(
      // 灌区的边界
      `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
    ),
    axios(
      // 水库边界
      `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=thjgq:HP001&maxFeatures=50&outputFormat=application/json&filter=<PropertyIsEqualTo><PropertyName>object_name</PropertyName><Literal>桃花江水库</Literal></PropertyIsEqualTo>`,
    ),
    axios(
      // 沟渠
      `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo></Or>`,
    ),
  ]).then(res => {
    mapBoundGeo(res[0].data, mapIns, { top: 20, bottom: 20, left: 60, right: 10 })
    const all = {
      type: 'FeatureCollection',
      features: [
        ...res[0].data.features.map(el => ({ ...el, properties: { ...el.properties, name: 'guanqu' } })),
        ...res[1].data.features.map(el => ({ ...el, properties: { ...el.properties, name: 'shuiku' } })),
        ...res[2].data.features.map(el => ({ ...el, properties: { ...el.properties, name: 'gouqu' } })),
      ],
    }
    // 给每个区域加质点
    const geojson = {
      ...all,
      features: [
        ...all.features.map(el => {
          return turf.centerOfMass({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
        }),
        ...all.features,
      ],
    }

    mapIns.addSource('allGeojson', {
      type: 'geojson',
      data: geojson,
    })

    mapIns.addLayer({
      id: 'guanqu-line',
      type: 'line',
      source: 'allGeojson',
      paint: {
        'line-color': '#0AC8FC',
        'line-width': 2,
      },
      filter: ['all', ['==', '$type', 'LineString'], ['==', 'name', 'guanqu']],
    })

    mapIns.addLayer({
      id: 'shuiku-area-fill',
      type: 'fill',
      source: 'allGeojson',
      paint: {
        'fill-color': '#1CE951',
        'fill-outline-color': '#F6CC58',
      },
      filter: ['all', ['==', '$type', 'Polygon'], ['==', 'name', 'shuiku']],
    })

    mapIns.addLayer({
      id: 'gouqu-line',
      type: 'line',
      source: 'allGeojson',
      paint: {
        'line-color': '#29F934',
        'line-width': 2,
      },
      filter: ['all', ['==', '$type', 'LineString'], ['==', 'name', 'gouqu']],
    })

    mapIns.addLayer({
      id: 'all-point',
      type: 'symbol',
      source: 'allGeojson',
      layout: {
        'text-size': 12,
        'text-field': ['get', 'object_name'],
        // 'text-offset': [0, 1.25],
        'text-anchor': 'center',
      },
      filter: ['==', '$type', 'Point'],
    })
  })
}
// export default function initMap(mapIns, geojson, id) {
//   const json = JSON.parse(JSON.stringify(geojson))
//   // 给每个区域加质点
//   const geojsonData = {
//     ...json,
//     features: [
//       ...json.features.map(el => {
//         return turf.centroid({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
//       }),
//       ...json.features,
//     ],
//   }

//   const deckOverlay = new MapboxOverlay({
//     id: 'deck-geojson-layer-overlay' + id,
//     layers: [
//       new GeoJsonLayer({
//         id: 'geojson-layer' + id,
//         data: geojsonData,
//         // filled: true,
//         pickable: false,
//         stroked: true,
//         getLineWidth: 6,
//         lineWidthMaxPixels: 10, //线条的最大宽度（以像素为单位）
//         lineWidthMinPixels: 2, //线条的最小宽度（以像素为单位）
//         getLineColor: [245, 221, 126, 255],

//         pointType: 'text',
//         getText: d => {
//           d.properties?.object_name
//         },
//         getTextColor: [29, 33, 41, 255],
//         textCharacterSet: 'auto',
//         getTextSize: 12,
//         textOutlineColor: [255, 255, 255, 255],
//         textOutlineWidth: 7,
//         textFontSettings: { sdf: true, smoothing: 0.3 },
//       }),
//     ],
//   })

//   mapIns.addControl(deckOverlay)
// }
