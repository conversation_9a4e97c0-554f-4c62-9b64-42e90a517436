<template>
  <div class="overlay-manager-demo">
    <h3>全局 Overlay 管理器演示</h3>
    <div class="controls">
      <button @click="addTestLayer">添加测试图层</button>
      <button @click="removeTestLayer">移除测试图层</button>
      <button @click="showLayerInfo">显示图层信息</button>
    </div>
    <div class="info">
      <p>当前图层数量: {{ layerCount }}</p>
      <p>图层ID列表: {{ layerIds.join(', ') }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useOverlayManager } from './overlayManager.js'
import { GeoJsonLayer } from '@deck.gl/layers'

const props = defineProps(['mapInstance'])

const { addLayer, removeLayer, getLayerCount, getLayerIds } = useOverlayManager()

const layerCount = ref(0)
const layerIds = ref([])
const testLayerCounter = ref(0)

const updateInfo = () => {
  layerCount.value = getLayerCount()
  layerIds.value = getLayerIds()
}

const addTestLayer = () => {
  if (!props.mapInstance) {
    console.warn('地图实例未准备好')
    return
  }

  testLayerCounter.value++
  const layerId = `test-layer-${testLayerCounter.value}`
  
  // 创建一个简单的测试图层
  const testLayer = new GeoJsonLayer({
    id: layerId,
    data: {
      type: 'FeatureCollection',
      features: [
        {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [111.989204 + Math.random() * 0.01, 28.453133 + Math.random() * 0.01]
          },
          properties: {
            name: `测试点 ${testLayerCounter.value}`,
            color: '#ff0000'
          }
        }
      ]
    },
    filled: true,
    pickable: true,
    pointType: 'circle',
    getPointRadius: 50,
    getFillColor: [255, 0, 0, 200]
  })

  addLayer(props.mapInstance, layerId, testLayer)
  updateInfo()
  console.log(`添加了测试图层: ${layerId}`)
}

const removeTestLayer = () => {
  if (!props.mapInstance) {
    console.warn('地图实例未准备好')
    return
  }

  const layerId = `test-layer-${testLayerCounter.value}`
  removeLayer(props.mapInstance, layerId)
  updateInfo()
  console.log(`移除了测试图层: ${layerId}`)
}

const showLayerInfo = () => {
  updateInfo()
  console.log('当前图层信息:', {
    count: layerCount.value,
    ids: layerIds.value
  })
}

onMounted(() => {
  updateInfo()
})
</script>

<style scoped>
.overlay-manager-demo {
  position: absolute;
  top: 10px;
  right: 10px;
  background: white;
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  z-index: 1000;
  min-width: 250px;
}

.controls {
  margin-bottom: 10px;
}

.controls button {
  margin: 2px;
  padding: 5px 10px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 3px;
  cursor: pointer;
  font-size: 12px;
}

.controls button:hover {
  background: #0056b3;
}

.info p {
  margin: 5px 0;
  font-size: 12px;
}
</style>
