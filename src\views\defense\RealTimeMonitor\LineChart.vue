<template>
  <ScaleBox :style="{ width: '430px', height: '196px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="<PERSON><PERSON><PERSON>">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()

  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )
  const hexToRgba = (hex, opacity) => {
    let rgbaColor = ''
    let reg = /^#[\da-f]{6}$/i
    if (reg.test(hex)) {
      rgbaColor = `rgba(${parseInt('0x' + hex.slice(1, 3))},${parseInt(
        '0x' + hex.slice(3, 5),
      )},${parseInt('0x' + hex.slice(5, 7))},${opacity})`
    }
    return rgbaColor
  }
  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  let colors = ['rgba(108,230,103,1)', 'rgba(30,89,115,1)', 'rgba(9, 200, 252, 0.8)']
  function getConfig(data) {
    let type = data[0].type
    let xData = data[0].xData // [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    let seriesData = data[0].data //[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    let bgColor = '#fff'
    let colorList = ['#507EF7', '#74C3F8', '#EF8432', '#0FC6C2']
    return {
      tooltip: {
        show: true,
        trigger: 'axis',

        // appendToBody: true,
        // confine: true,
        // // alwaysShowContent: true,
        // className: 'echart-tooltip',
        // backgroundColor: 'rgba(22,45,72,0.84)',
        // axisPointer: {
        //   type: 'shadow', // shadow cross
        //   label: {
        //     backgroundColor: 'rgba(152,224,255,0.15)',
        //   },
        // },
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '-') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //
              console.log('seriesName', params, param)
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = colorList[0] //图例颜色

              function getUnit(seriesName) {
                switch (seriesName) {
                  case '降雨量':
                    return 'mm'
                  case '累计雨量':
                    return 'mm'
                  case '水位':
                    return 'm'
                  case '预报来水流量':
                    return 'm³/s'
                  default:
                    return
                }
              }
              htmlStr += `
                    <div style="border: 1px solid rgba(152,224,255,0.3) border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
        // formatter: params => {
        //   let dataStr = `
        //     <div style="line-height:1.2">
        //       <span style="font-size:0.12rem;color:var(--text-md);">${params[0].axisValue}</span>
        //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[0].seriesName}:<span style="color:var(--primary-color)">${params[0].value[1]}台时</span></div>
        //     </div>
        //   `
        //   return dataStr
        // },
      },
      grid: {
        // left: 20,
        left: 18,
        right: 15,
        top: 30,
        bottom: 5,
        containLabel: true,
      },
      legend: {
        show: true,
        textStyle: {
          color: '#8092AC',
        },
        itemWidth: 12,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#54608A',
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        axisLabel: {
          // color: '#8092AC',
          color: 'rgba(222, 242, 252, 1)',

          // color: 'rgba(105, 157, 178, 1)',
          // interval: 0,
        },
        data: xData,
      },
      yAxis: {
        type: 'value',
        name: '单位: mm',
        // nameGap: 12,
        minInterval: 1, //自动计算坐标轴最小间隔，例：设置成1，刻度没有小数
        // maxInterval: '', //自动计算坐标轴最大间隔
        nameTextStyle: {
          // color: '#C0DDFF',
          color: 'rgba(222, 242, 252, 1)',
          padding: [0, 15, 0, 20],
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          // color: '#8092AC',
          color: 'rgba(105, 157, 178, 1)',
        },
        splitLine: {
          lineStyle: {
            // color: '#363E5B',
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        // max: 3,
        min: 0,
        max: function (value) {
          let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

          return val
        },
      },
      series: {
        type: 'bar',
        barMaxWidth: 8,
        name: '降雨量',
        color: colorList[0],
        smooth: true,
        // showSymbol: false,/
        symbolSize: 8,
        zlevel: 3,
        // lineStyle: {
        //   normal: {
        //     color: color[0],
        //     shadowBlur: 3,
        //     shadowColor: hexToRgba(color[0], 0.5),
        //     shadowOffsetY: 8,
        //   },
        // },
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: hexToRgba(colorList[0], 0.3),
            },
            {
              offset: 0.2,
              color: hexToRgba(colorList[0], 0.5),
            },
            {
              offset: 1,
              color: hexToRgba(colorList[0], 0.9),
            },
          ]),
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: hexToRgba(colorList[0], 0.3),
                },
                {
                  offset: 1,
                  color: hexToRgba(colorList[0], 0.1),
                },
              ],
              false,
            ),
            shadowColor: hexToRgba(colorList[0], 0.1),
            shadowBlur: 10,
          },
        },
        data: seriesData,
        // itemStyle: {
        //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //     {
        //       offset: 0,
        //       color: 'rgba(108, 230, 103, 1)',
        //     },
        //     {
        //       offset: 1,
        //       color: 'rgba(108, 230, 103, 0)',
        //     },
        //   ]),
        // },
      },
    }
  }
</script>

<style lang="scss" scoped></style>
