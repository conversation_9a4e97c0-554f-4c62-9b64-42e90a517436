//水库今日值班人员
export function getDutyOfficer() {
  return request({
    url: '/view/twinReservoir/getTodayNames',
    method: 'post',
  })
}
//水电站详情
export function getTodayHydropower() {
  return request({
    url: '/view/twinReservoir/getTodayHydropower',
    method: 'post',
  })
}
//自动预报查询
export function getAutoForecast(data: object) {
  return request({
    url: '/model/in-water/auto-forecast/query',
    method: 'post',
    data,
  })
}
//模型-调度模拟
export function getSchedulingSimulation(data: object) {
  return request({
    url: '/model/resvr-disp/page',
    method: 'post',
    data,
  })
}
//模型-调度模拟-详情
export function getSimulationResults(params) {
  return request({
    url: '/model/resvr-disp/get',
    method: 'post',
    params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}
//siteId: 84
// 站点终端列表--全部指标
export function getIndexByCode(data: object) {
  return request({
    url: '/war/rainWater/allList',
    method: 'post',
    data,
  })
}
//站点弹窗
export function getSectionByCode(data: object) {
  return request({
    url: '/view/twinReservoir/object/listByIds',
    method: 'post',
    data,
  })
}
