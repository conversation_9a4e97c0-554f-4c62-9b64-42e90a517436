<template><component :is="render()"></component></template>

<script setup lang="tsx" name="TextScroll">
  const props = withDefaults(
    defineProps<{
      text: string | null
    }>(),
    {
      text: '',
    },
  )

  const time = ref(0)
  const widthControl = ref({
    currentWidth: 0,
    textWidth: 0,
    scrollWidth: 0,
  })

  const TextBoxRef = ref(null)
  const TextRef = ref(null)

  const translateX = computed(() => `translateX(-${widthControl.value.scrollWidth}px)`)
  const animation = computed(() => `textScroll ${time.value}s linear infinite`)

  onMounted(() => {
    setTimeout(() => {
      nextTick(() => {
        const currentWidth = TextBoxRef.value.clientWidth
        const textWidth = +TextRef.value.offsetWidth

        widthControl.value = {
          currentWidth,
          textWidth,
          scrollWidth: textWidth + 20,
        }
        if (currentWidth < textWidth) {
          time.value = textWidth / 35
        }
      })
    }, 100)
  })

  const render = () => (
    <div class='scroll-text-box' ref={TextBoxRef}>
      <div class='scroll-text'>
        <span ref={TextRef}>{props.text}</span>

        {widthControl.value.currentWidth < widthControl.value.textWidth && (
          <>
            <i style={{ marginRight: '20px' }}></i>
            <span>{props.text}</span>
          </>
        )}
      </div>
    </div>
  )
</script>
<style lang="scss">
  .scroll-text-box {
    width: 100%;
    // color: #fff;
    overflow: hidden;

    @keyframes textScroll {
      0% {
        transform: translateX(0px);
      }
      100% {
        transform: v-bind(translateX);
      }
    }

    .scroll-text {
      white-space: nowrap;
      animation: v-bind(animation);
    }
  }
</style>
