<template><component :is="render()"></component></template>

<script setup lang="tsx" name="NaSelect">
  const attrs = useAttrs()
  const slots = useSlots()

  // show
  const render = () => (
    <n-select
      placeholder='请选择'
      class={`${attrs.type === 'side' ? 'my-custom-naive-select-side' : 'my-custom-naive-select'}`}
      {...attrs}
    >
      {slots}
    </n-select>
  )
</script>
<style lang="scss">
  .my-custom-naive-select-side.n-select {
    display: flex;
    .n-base-selection {
      font-size: 0.14rem;
      min-height: 0.25rem;
      border-radius: 0.06rem;
      background: linear-gradient(266deg, rgba(85, 130, 148, 0.5) 1%, rgba(85, 130, 148, 0) 99%);
      // background-color: #004080 !important; /* 深蓝色背景 */
      color: #ffffff !important; /* 白色字体 */
      // color: red;
      .n-base-selection-label {
        height: 0.24rem;
        // background: url('@/assets/images/select-bg.png') no-repeat center / 100% 100%;
        background: linear-gradient(267deg, rgba(85, 130, 148, 0.5) 1%, rgba(85, 130, 148, 0) 99%);

        box-sizing: border-box;
        border: 1px solid #09fcc7;
        // border-radius: 8px;
        // border-radius: 6px;
        // opacity: 1;

        // /* 自动布局 */
        // display: flex;
        // flex-direction: row;
        // align-items: center;
        // // padding: 2px 10px;
        // gap: 4px;

        // background: linear-gradient(267deg, rgba(85, 130, 148, 0.5) 1%, rgba(85, 130, 148, 0) 99%);

        // box-sizing: border-box;
        // border: 1px solid #09fcc7;

        // box-sizing: border-box;
        // border: 1px solid #09fcc7;
        .n-base-selection-placeholder {
          padding: 0 0.26rem 0 0.12rem;
        }
        .n-base-selection__border {
          border: none;
        }
      }
    }
    .n-base-selection-item.n-base-selection-item--selected {
      background-color: #09fcc7 !important; /* 浅色背景 */

      color: #09fcc7 !important; /* 绿色字体 */
    }

    .n-base-selection-input__content {
      // padding: 0 0.26rem 0 0.12rem;
      color: #b2daea;
    }

    .n-base-selection--focus,
    .n-base-selection--hover,
    .n-base-selection--active {
      // border-color: transparent !important; /* 无色边框 */
      border: 1px solid #09fcc7;
      border-radius: 0.08rem;
      // background: #158ca0 !important;
    }

    .n-base-select-menu.n-select-menu {
      // background-color: rgba(12, 38, 49, 0.9);
      background: #158ca0 !important;
      // background-color: rgba(104, 150, 173, 0.6); // rgba(9, 252, 199, 0.9);
      // background-color: rgba(154, 209, 161, 0.9);
      // background-color: rgba(198, 198, 198, 0.5);
      // color: rgba(9, 26, 44, 0.8) !important;
      min-height: 0.4rem;
      .n-base-select-option {
        padding: 0 0.12rem;
        min-height: 0.34rem;
        font-size: 0.14rem;
        background: #158ca0 !important;
        // color: rgba(14, 85, 56, 1) !important;
        // color: rgba(94, 141, 56, 1);
        // color: rgba(59, 90, 252, 0.8) !important;
      }
      .n-base-select-option {
        color: #b2daea;
        background: #158ca0 !important;
      }
      .n-base-select-option--selected,
      .n-base-select-option--pending,
      .n-base-select-option--pending::before {
        background: #158ca0 !important;
        color: #12e952 !important;
      }
    }
  }

  .my-custom-naive-select.n-select {
    .n-base-selection {
      font-size: 0.14rem;
      min-height: 0.24rem;
      border-radius: 0.08rem;

      .n-base-selection-label {
        height: 0.24rem;
        // background: rgba(9, 26, 44, 0.8);
        // border: 1px solid rgba(152, 224, 255, 0.3);
        // border: 1px solid rgba(9, 252, 199, 0.3);
        .n-base-selection-placeholder {
          padding: 0 0.26rem 0 0.12rem;
        }
      }
    }
    .n-base-selection--focus,
    .n-base-selection:hover,
    .n-base-selection:active {
      border-color: transparent !important; /* 无色边框 */
      // border: 1px solid #09fcc7;
      border-radius: 0.08rem;
    }
  }

  .v-vl-items {
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    border: 1px solid rgba(54, 96, 103, 0.5);
  }

  .n-base-select-menu.n-select-menu {
    // background-color: rgba(12, 38, 49, 0.9);
    background: #158ca0 !important;
    // background-color: rgba(104, 150, 173, 0.6); // rgba(9, 252, 199, 0.9);
    // background-color: rgba(154, 209, 161, 0.9);
    // background-color: rgba(198, 198, 198, 0.5);
    // color: rgba(9, 26, 44, 0.8) !important;
    min-height: 0.4rem;
    .n-base-select-option {
      padding: 0 0.12rem;
      min-height: 0.34rem;
      font-size: 0.14rem;
      // color: rgba(14, 85, 56, 1) !important;
      // color: rgba(94, 141, 56, 1);
      // color: rgba(59, 90, 252, 0.8) !important;
    }
    .n-base-select-option {
      color: #b2daea;
    }
    .n-base-select-option--selected,
    .n-base-select-option--pending {
      color: #12e952 !important;
    }
  }

  .n-base-select-menu .n-base-select-option.n-base-select-option--pending::before {
    background: #158ca0 !important;
    color: #12e952 !important;
  }
</style>
