<template>
  <MyCard class="mt-0.1rem mb-0.1rem" name="水库来水预报">
    <div class="w-4.29rem h-1.9rem chart-bg">
      <LineChart v-show="forecastFlow?.length" :dataSource="forecastFlow" />
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="WaterForecast">
  import { getOptions } from '@/api/common'
  import { getAutoForecast } from '../../../services'
  import dayjs from 'dayjs'

  import LineChart from './LineChart.vue'

  const forecastFlow = ref([])

  onMounted(() => {
    getOptions('fcstRange').then(res => {
      getAutoForecast({
        endTime: dayjs().format('YYYY-MM-DD HH') + ':00',
        fcstRange: res?.data[0].key,
        startTime: dayjs().subtract(72, 'hours').format('YYYY-MM-DD HH') + ':00',
      }).then(res => {
        forecastFlow.value = res?.data?.fcsts
      })
    })
  })
</script>
<style lang="scss" scoped>
  .chart-bg {
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    border: 1px solid rgba(54, 96, 103, 0.5);
  }
</style>
