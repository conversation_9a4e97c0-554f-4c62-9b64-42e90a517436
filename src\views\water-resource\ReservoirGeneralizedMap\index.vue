<template>
  <MyCard name="水库调度概化图">
    <div class="content pt-0.1rem h-4rem flex flex-col relative">
      <div
        class="flow-map w-4.06rem h-2.8rem mx-0.1rem relative text-0.14rem"
        :style="{ background: `url(${getImageUrl('water-resource/flow-map-bg2.png')}) no-repeat center / 100% 100%` }"
      >
        <!-- left-branch1 -->
        <!-- <div class="left-branch1" v-if="state.projects['430922203000_CJCJZZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div> -->
        <div class="left-branch1-top" v-if="state.projects['430922103000_XSDFSZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="left-branch1-center" v-if="state.projects['430922103000_LTSXHZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="left-branch1-bottom" v-if="state.projects['430922203000_CJCJZZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <!-- left-branch2 -->
        <!-- <div class="left-branch2" v-if="state.projects['430922203000_GQJZZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div> -->
        <div class="left-branch2-top" v-if="state.projects['430922212000_LJAFSZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="left-branch2-center" v-if="state.projects['430922212000_WJXHZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="left-branch2-bottom" v-if="state.projects['430922203000_GQJZZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="left-bottom" v-if="state.projects['430922203000_ZLYCFSZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <!-- <div class="right">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div> -->
        <div class="right-top" v-if="state.projects['430922108000_GLCDHX']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="right-middle" v-if="state.projects['430922108000_DFLDH']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="right-bottom" v-if="state.projects['430922108000_SSWFSZ']?.isOpen == 1">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>
        <div class="center">
          <img :src="getImageUrl('water-resource/flow-map-bg-line.png')" alt="Dynamic Image" />
        </div>

        <div class="absolute w-0.8rem h-0.2rem title-type" :style="{ top: '1.22rem', left: '0.02rem' }">沾溪干渠</div>
        <!-- 小石洞 -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.26rem', left: '0.48rem' }">
          {{ state.projects['430922103000_XSDFSZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '0.05rem', left: '0.5rem' }">
          分水闸
          <!-- {{ state.projects['430922103000_XSDFSZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.26rem', left: '1.02rem' }">
          {{ state.projects['430922103000_XSDFSZ']?.flow }}
        </div>
        <!-- 龙头山 -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.76rem', left: '0.48rem' }">
          {{ state.projects['430922103000_LTSXHZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '0.56rem', left: '0.5rem' }">
          泄洪闸
          <!-- {{ state.projects['430922103000_LTSXHZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.76rem', left: '1.02rem' }">
          {{ state.projects['430922103000_LTSXHZ']?.flow }}
        </div>
        <!-- 陈家冲 -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.4rem', left: '0.57rem' }">
          {{ state.projects['430922203000_CJCJZZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '1.62rem', left: '0.62rem' }">
          节制闸
          <!-- {{ state.projects['430922203000_CJCJZZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '1.4rem', left: '-0.02rem' }">
          {{ state.projects['430922203000_CJCJZZ']?.flow }}
        </div>

        <div class="absolute w-0.8rem h-0.2rem title-type" :style="{ top: '2rem', left: '0.66rem' }">西干渠</div>

        <!-- 李家坳分水闸（西） -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.26rem', left: '2.22rem' }">
          {{ state.projects['430922212000_LJAFSZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '0.06rem', left: '2.2rem' }">
          分水闸
          <!-- {{ state.projects['430922212000_LJAFSZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.26rem', left: '2.74rem' }">
          {{ state.projects['430922212000_LJAFSZ']?.flow }}
        </div>
        <!-- 万金泄洪闸  （西） -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.86rem', left: '2.28rem' }">
          {{ state.projects['430922212000_WJXHZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '0.66rem', left: '2.2rem' }">
          泄洪闸
          <!-- {{ state.projects['430922212000_WJXHZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '0.86rem', left: '2.74rem' }">
          {{ state.projects['430922212000_WJXHZ']?.flow }}
        </div>
        <!-- 高桥节制闸 -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.4rem', left: '1.36rem' }">
          {{ state.projects['430922203000_GQJZZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '1.62rem', left: '1.34rem' }">
          节制闸
          <!-- {{ state.projects['430922203000_GQJZZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '1.4rem', left: '1.86rem' }">
          {{ state.projects['430922203000_GQJZZ']?.flow }}
        </div>
        <!-- 子良岩村分水闸 -->
        <div class="absolute w-0.6rem h-0.2rem text-[#ffffff]" :style="{ top: '2.2rem', left: '1.35rem' }">
          {{ state.projects['430922203000_ZLYCFSZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '2.44rem', left: '1.44rem' }">
          分水闸
          <!-- {{ state.projects['430922203000_ZLYCFSZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '2.22rem', left: '0.72rem' }">
          {{ state.projects['430922203000_ZLYCFSZ']?.flow }}
        </div>

        <!-- 狮山湾分水闸（东） -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.78rem', left: '2.48rem' }">
          {{ state.projects['430922108000_SSWFSZ']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '1.56rem', left: '2.52rem' }">
          分水闸
          <!-- {{ state.projects['430922108000_SSWFSZ']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC]" :style="{ top: '1.78rem', left: '3rem' }">
          {{ state.projects['430922108000_SSWFSZ']?.flow }}
        </div>
        <!-- 大伏岭倒虹（东） -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '1.44rem', left: '3.55rem' }">
          {{ state.projects['430922108000_DFLDH']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model" :style="{ top: '1.2rem', left: '3.64rem' }">
          倒虹吸
          <!-- {{ state.projects['430922108000_DFLDH']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '1.64rem', left: '3.53rem' }">
          {{ state.projects['430922108000_DFLDH']?.flow }}
        </div>
        <!-- 郭里冲倒虹吸（东） -->
        <div class="absolute w-0.5rem h-0.2rem text-[#ffffff]" :style="{ top: '0.066rem', left: '3.54rem' }">
          {{ state.projects['430922108000_GLCDHX']?.name }}
        </div>
        <div class="absolute w-0.5rem h-0.2rem title-model text-right" :style="{ top: '0.32rem', left: '3.52rem' }">
          倒虹吸
          <!-- {{ state.projects['430922108000_GLCDHX']?.type }} -->
        </div>
        <div class="absolute w-0.5rem h-0.2rem text-[#09C8FC] text-right" :style="{ top: '0.46rem', left: '3.5rem' }">
          {{ state.projects['430922108000_GLCDHX']?.flow }}
        </div>
        <div class="absolute w-0.8rem h-0.2rem title-type" :style="{ top: '2rem', left: '3.54rem' }">东干渠</div>

        <div class="absolute w-0.8rem h-0.2rem title-type" :style="{ top: '2.4rem', left: '2.26rem' }">总干渠</div>
        <!-- 430922000000_ZGQFSZ -->
      </div>
      <div
        class="reservior-map w-4.3rem h-1.2rem relative flex"
        :style="{ background: `url(${getImageUrl('water-resource/reservior-map.png')}) no-repeat center / 100% 100%` }"
      >
        <div
          class="absolute w-0.27rem h-0.27rem left-1.66rem top-0.4rem"
          :style="{ background: `url(${getImageUrl('water-resource-icon.png')}) no-repeat center / 100% 100%` }"
        ></div>
        <div
          class="absolute w-0.92rem h-0.28rem left-2rem top-0.4rem"
          :style="{ background: `url(${getImageUrl('water-resource/water-resource-title.png')}) no-repeat center / 100% 100%` }"
        ></div>
        <span class="text-[#DEF2FC] text-0.16rem ml-0.16rem">流量单位:</span>
        <span class="text-[#699DB2] text-0.14rem ml-0.05rem">m³/s</span>
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="ReservoirGeneralizedMap">
  import { getResourceConsumptionList } from '../services'
  import { getValueByKey } from '@/api'
  const state = $ref({
    codes: '',
    projects: {},
  })
  onMounted(() => {
    // unmountLoading()

    init()
  })
  const init = () => {
    getValueByKey('thj.map.codes').then(res => {
      state.codes = res.data
      getList()
    })
  }
  const transferData = list => {
    const obj = list.reduce((accumulator, currentItem) => {
      // 根据 projectName 确定 type
      let type
      if (currentItem.projectName.includes('分水闸')) {
        type = '分水闸'
      } else if (currentItem.projectName.includes('倒虹')) {
        type = '倒虹'
      } else if (currentItem.projectName.includes('倒虹吸')) {
        type = '倒虹吸'
      } else {
        type = '未知类型' // 默认类型
      }

      // 构建新对象
      const newObj = {
        code: currentItem.projectCode,
        name: currentItem.projectName,
        isOpen: currentItem.isOpen,
        flow: currentItem.flow,
        type: type,
      }

      // 添加到累加器
      accumulator[currentItem.projectCode] = newObj

      return accumulator
    }, {})
  }
  const getList = () => {
    let param = { projectCodes: state.codes }

    getResourceConsumptionList(param).then(res => {
      // state.projects = transferData(res.data)
      // let newObj = {};
      let list = res.data
      list.forEach(item => {
        let type, name
        if (item.projectName.includes('分水闸')) {
          type = '分水闸'
          name = item.projectName.replace('分水闸', '')
        } else if (item.projectName.includes('倒虹吸')) {
          type = '倒虹吸'
          name = item.projectName.replace('倒虹吸', '')
          // name = item.projectName
        } else if (item.projectName.includes('倒虹')) {
          type = '倒虹吸'
          name = item.projectName.replace('倒虹', '')
          // name = item.projectName
        } else if (item.projectName.includes('节制闸')) {
          type = '节制闸'
          name = item.projectName.replace('节制闸', '')
        } else if (item.projectName.includes('泄洪闸')) {
          type = '泄洪闸'
          name = item.projectName.replace('泄洪闸', '')
        } else {
          type = '未知类型' // 默认类型
        }
        state.projects[item.projectCode] = {
          code: item.projectCode,
          name: name,
          isOpen: item.isOpen,
          flow: item.flow,
          type: type, // 这里假设所有项目都是分水闸类型，如果需要动态判断可以根据item的其他属性
        }
      })

      //   state.projects = {
      //   //东干渠
      //   '430922108000_SSWFSZ': { code: '430922108000_SSWFSZ', name: '狮山湾', flow: 12, type: '分水闸', isOpen: 1 },
      //   '430922108000_DFLDH': { code: '430922108000_DFLDH', name: '大伏岭', flow: 11.2, type: '倒虹吸', isOpen: 1 },
      //   '430922108000_GLCDHX': { code: '430922108000_GLCDHX', name: '郭里冲', flow: 11.5, type: '倒虹吸', isOpen: 1 },

      //   //西干渠
      //   '430922203000_ZLYCFSZ': { code: '430922203000_ZLYCFSZ', name: '子良岩村', flow: 12, type: '分水闸', isOpen: 1 },
      //   '430922203000_GQJZZ': { code: '430922203000_GQJZZ', name: '高桥', flow: 11.2, type: '节制闸', isOpen: 1 },
      //   '430922212000_WJXHZ': { code: '430922212000_WJXHZ', name: '万金', flow: 11.5, type: '泄洪闸', isOpen: 0 },
      //   '430922212000_LJAFSZ': { code: '430922212000_LJAFSZ', name: '李家坳', flow: 11.5, type: '分水闸', isOpen: 1 },

      //   //沾溪干渠
      //   '430922203000_CJCJZZ': { code: '430922203000_CJCJZZ', name: '陈家冲', flow: 21, type: '节制闸', isOpen: 1 },
      //   '430922103000_LTSXHZ': { code: '430922103000_LTSXHZ', name: '龙头山', flow: 11.2, type: '泄洪闸', isOpen: 1 },
      //   '430922103000_XSDFSZ': { code: '430922103000_XSDFSZ', name: '小石洞', flow: 13, type: '分水闸', isOpen: 1 },
      // }
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;

    .flow-map {
      img {
        width: 4.06rem;
        height: 2.8rem;
        position: absolute;
        top: 0;
        left: 0;
      }
      .left-branch1 img {
        // margin-top: 0.01rem;
        margin-left: -0.02rem;
        /* 左上  右上 右下 左下*/
        clip-path: polygon(1% 1%, 27% 1%, 27% 47%, 1% 47%);
        /* background-color: red; */
      }
      /* 显示左边 左分支上面部分 */
      .left-branch1-top img {
        margin-top: -0.07rem;
        margin-left: -0.02rem;
        /* 左上  右上 右下 左下*/
        clip-path: polygon(0% 6%, 8% 6%, 8% 12%, 0% 12%);
        // background-color: red;
      }

      /* 显示左边 左分支中间部分 */
      .left-branch1-center img {
        margin-left: -0.02rem;
        clip-path: polygon(1% 16%, 14% 16%, 14% 27%, 1% 27%);
        // background-color: red;
      }
      /* 显示左边 左分支下面部分 */
      .left-branch1-bottom img {
        margin-top: -0.01rem;
        margin-left: -0.02rem;
        /* clip-path: polygon(1% 30%, 49% 30%, 49% 45%, 1% 45%); */
        clip-path: polygon(1% 34%, 22% 34%, 22% 45%, 1% 45%);
        // background-color: red;
      }
      /* 显示左边 右分支 */
      .left-branch2 img {
        // margin-top: 0.01rem;
        margin-left: -0.01rem;
        /* 左上  右上 右下 左下*/
        clip-path: polygon(28% 6%, 52% 6%, 52% 47%, 28% 47%);
        /* background-color: red; */
      }
      /* 显示左边 右分支上面部分 */
      .left-branch2-top img {
        margin-top: -0.08rem;
        margin-left: -0.01rem;
        /* 左上  右上 右下 左下*/
        clip-path: polygon(46% 6%, 49% 6%, 49% 14%, 46% 14%);
        // background-color: red;
      }

      /* 显示左边 右分支中间部分 */
      .left-branch2-center img {
        margin-left: -0.01rem;
        clip-path: polygon(46% 16%, 51.5% 16%, 51.5% 32%, 46% 32%);
        // background-color: red;
      }
      /* 显示左边 左分支下面部分 */
      .left-branch2-bottom img {
        // margin-top: 4px;
        margin-left: -0.01rem;
        /* clip-path: polygon(1% 30%, 49% 30%, 49% 45%, 1% 45%); */
        clip-path: polygon(35% 38%, 48.6% 37%, 48.6% 45%, 35% 45%);
        // background-color: red;
      }

      /* 显示左边下面部分 */
      .left-bottom img {
        /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
        margin-top: 0.01rem;
        margin-left: -0.02rem;
        clip-path: polygon(1% 45%, 43% 45%, 44% 78%, 1% 78%);
        // background-color: red;
      }

      /* 显示右上部分 */
      .right img {
        // margin-top: 8.5px;
        margin-left: 1px;
        clip-path: polygon(52% 0%, 100% 0%, 100% 78%, 52% 78%);
        background-color: aqua;
      }
      /* 显示右上部分 */
      .right-top img {
        margin-left: 1px;
        clip-path: polygon(50% 0, 100% 0, 100% 12%, 50% 12%);
        // background-color: aqua;
      }

      /* 显示右边中间部分 */
      .right-middle img {
        margin-left: 1px;
        /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
        clip-path: polygon(54% 18%, 100% 18%, 100% 40%, 54% 40%);
        // background-color: red;
      }
      /* 显示右边下面部分 */
      .right-bottom img {
        /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
        // margin-top: 8.2px;
        margin-left: 1px;
        clip-path: polygon(59% 46%, 100% 46%, 100% 78%, 58.8% 78%);
        // background-color: red;
      }
      .center img {
        /* clip-path: polygon(51% 25%, 100% 0, 100% 46%, 51% 46%); */
        // margin-top: 12px;
        margin-left: -0.5px;
        clip-path: polygon(46% 73.4%, 60% 73.4%, 60% 98%, 46% 98%);
        /* background-color: red; */
      }
    }
  }
  .title-east {
    color: #09fcc7;
    font-size: 0.14rem;
  }
  .title-west {
    color: #09c8fc;
    font-size: 0.14rem;
  }
  .title-value {
    color: #09c8fc;
    font-size: 0.14rem;
  }
  .title-type {
    color: #699db2;
    font-size: 0.12rem;
  }
  .title-model {
    color: rgba(105, 157, 178, 0.6);
    font-size: 0.12rem;
  }
</style>
