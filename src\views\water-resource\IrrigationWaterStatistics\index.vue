<template>
  <MyCard name="灌溉用水统计" class="relative">
    <template #headerRight>
      <div class="absolute w-1.94rem right-0.05rem top-0.06rem flex items-center">
        <NaSegmentTabs
          class="w-0.8rem mr-0.1rem"
          v-model:value="state.timeType"
          @update:value="onTimeTypeChange"
          :options="[
            { key: 0, title: '月' },
            { key: 1, title: '年' },
          ]"
        ></NaSegmentTabs>
        <NaSelect
          v-if="state.timeType == 1"
          class="w-0.98rem mt-0.04rem"
          v-model:value="year"
          type="side"
          :show-checkmark="false"
          :options="state.yearList"
        >
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
        <NaSelect
          v-if="state.timeType == 0"
          class="w-0.98rem mt-0.04rem"
          v-model:value="month"
          type="side"
          :show-checkmark="false"
          :options="state.monthList"
        >
          <template #arrow>
            <MyIcon
              class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
            />
          </template>
        </NaSelect>
      </div>
    </template>
    <div class="content pt-0.1rem h-5.83rem flex flex-col relative">
      <div class="flex mt-0.25rem flex-wrap">
        <div class="row-item flex flex-1 mb-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/reservoir-type.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.9rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-0.98rem text-0.14rem">需水量(万m³)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.irrigationInfo.waterRate }}</span>

              <!-- <span class="text-[#699DB2] "></span> -->
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/water-rate.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.9rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-0.98rem text-0.14rem">调度水量(万m³)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.irrigationInfo.dispatchWaterRate }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">万m³</span> -->
            </div>
          </div>
        </div>

        <div class="row-item flex flex-1 mb-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{
              background: `url(${getImageUrl(`water-resource/use-water-rate.png`)}) no-repeat center / 100% 100%`,
            }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-1.08rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-1.18rem text-0.14rem">实际用水量(万m³)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.irrigationInfo.useWaterRate }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">万m³</span> -->
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.54rem w-0.54rem ml-0.1rem mt-0.06rem"
            :style="{ background: `url(${getImageUrl(`water-resource/water-loss.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.9rem">
            <div class="text-[#B2DAEA] mb-0.1rem w-0.98rem text-0.14rem">损耗水量(万m³)</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.24rem">{{ state.irrigationInfo.lossWaterRate }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">万m³</span> -->
            </div>
          </div>
        </div>
      </div>
      <div class="flex flex-col flex-1 w-4.3rem h-2rem relative">
        <div
          class="absolute right-0.02rem w-1.9rem h-0.27rem text-0.15rem px-0.12rem font-bold py-0.06rem text-[#ffffff] z-2 mb-0.2rem"
        >
          <!-- <div class="tabs">
            <div class="tab" :class="{ active: state.type === 1 }" @click="onTypeChange(1)">按天</div>
            <div class="tab" :class="{ active: state.type === 2 }" @click="onTypeChange(2)">按管理所</div>
          </div> -->
          <!-- <div class="tab-links">
            <div class="tab-link" :class="{ active: state.type == 1 }" @click="onTypeChange(1)">月</div>
            <div class="tab-link" :class="{ active: state.type == 2 }" @click="onTypeChange(2)">年</div>
          </div> -->
          <NaSegmentTabs
            class="w-1.6rem"
            v-model:value="state.type"
            @update:value="onTypeChange"
            :options="[
              { key: 1, title: '按天' },
              { key: 2, title: '按管理所' },
            ]"
          ></NaSegmentTabs>
        </div>
        <div class="mt-0.4rem w-4.3rem h-1.8rem">
          <BarChart v-if="state.type == 2 && state.barChartData" :dataSource="state.barChartData" class="mt-0.16rem" />
          <LineChart v-else-if="state.type == 1 && state.lineChartData" :dataSource="state.lineChartData" class="mt-0.16rem" />
          <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
        </div>

        <!-- <LineChart /> -->
        <!--  -->
        <!-- <LineChart :dataSource="state.lineChartData" :year="year" class="mt-0.36rem ml-0.14rem" /> -->
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="IrrigationWaterStatistics">
  import { getIrrigateCount, getDayIrrigateCount, getThatIrrigate } from '../services'
  import BarChart from './BarChart.vue'
  import LineChart from './LineChart.vue'

  const year = ref(null)
  const month = ref(null)
  const state = $ref({
    // year: 2024,
    // month: null,
    tabsData: [
      { key: 1, title: '按天' },
      { key: 2, title: '按管理所' },
    ],
    yearList: [],
    monthList: [
      { label: '1月', value: 1 },
      { label: '2月', value: 2 },
      { label: '3月', value: 3 },
      { label: '4月', value: 4 },
      { label: '5月', value: 5 },
      { label: '6月', value: 6 },
      { label: '7月', value: 7 },
      { label: '8月', value: 8 },
      { label: '9月', value: 9 },
      { label: '10月', value: 10 },
      { label: '11月', value: 11 },
      { label: '12月', value: 12 },
    ],
    type: 1,
    timeType: 1,
    barChartData: [],
    lineChartData: [],
    irrigationInfo: [],
    configInfo: {},
  })
  onMounted(() => {
    // unmountLoading()
    year.value = new Date().getFullYear()
    month.value = new Date().getMonth() + 1
    generateYearList()
    init()
  })

  watch(
    month,
    newVal => {
      if (newVal) {
        getList(state.type)
      }
    },
    { immediate: true },
  )
  watch(
    year,
    newVal => {
      if (newVal) {
        getList(state.type)
      }
    },
    { immediate: true },
  )
  const init = () => {
    // let param = { time: 11, type: 0 }
    // param.time = state.timeType == 1 ? year.value : month.value
    // param.type = state.timeType

    getList(1)
  }
  const generateYearList = () => {
    const currentYear = new Date().getFullYear()
    const startYear = 2023 // 生成从当前年份到前6年的年份数组
    for (let year = currentYear; year >= startYear; year--) {
      state.yearList.push({ label: String(year), value: String(year) })
    }
  }
  const getList = type => {
    let param = { time: 11, type: 0 }
    state.lineChartData = []
    state.barChartData = []
    param.time = state.timeType == 1 ? year.value : month.value
    param.type = state.timeType
    getIrrigateCount(param).then(res => {
      state.irrigationInfo = res.data
    })
    if (type === 1) {
      getDayIrrigateCount(param).then(res => {
        state.lineChartData = [
          {
            type: state.timeType,
            xData: res.data?.map(el => el.time),
            data: res.data?.map(el => [el.time, el.waterRate]),
            data2: res.data?.map(el => [el.time, el.dispatchWaterRate]),
            data3: res.data?.map(el => [el.time, el.useWaterRate]), // [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
          },
        ]
      })
    } else if (type === 2) {
      getThatIrrigate(param).then(res => {
        state.barChartData = [
          {
            type: state.timeType,
            xData: res.data?.map(el => el.deptName),
            data: [
              { name: '需水量', data: res.data?.map(item => [item.deptName, item.waterRate]) },
              { name: '实际用水量', data: res.data?.map(item => [item.deptName, item.useWaterRate]) },
            ],
          },
        ]
      })
    }
  }
  const onTypeChange = type => {
    state.type = type
    // console.log('用水统计  type', state.type, type)
    getList(type)
  }
  const onTimeTypeChange = type => {
    state.timeType = type
    // console.log('用水统计 time type', state.timeType, type)
    if (state.timeType == 0 && !month.value) {
      return ''
    }
    getList(state.type)
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  .construction {
    width: 1.2rem;
    height: 0.32rem;
    padding: 0.02rem 0;
    border-radius: 4px;
    opacity: 1;
    // background: red;
    background: linear-gradient(273deg, rgba(114, 204, 240, 0.1) 3%, rgba(98, 188, 219, 0) 99%);
  }
  .n-tabs {
    height: 0.24rem !important;
  }
  // .tabs {
  //   width: 165px;
  //   height: 32px;
  //   border-radius: 4px;
  //   opacity: 1;

  //   background: linear-gradient(180deg, #0c2029 0%, #1f536a 100%);

  //   box-sizing: border-box;
  //   border: 1px solid;
  //   border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%) 1;
  //   display: flex;
  //   background: red;

  //   .tab {
  //   }
  //   .active {
  //     background: linear-gradient(180deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 255, 255, 0) 100%);
  //     border: 1px solid;
  //   }
  // }

  // .tab-links {
  //   // position: relative;
  //   margin-right: 10px;
  //   display: flex;
  //   // justify-content: space-around;
  //   background-color: #007bff; /* 蓝色背景 */
  //   width: 165px;
  //   height: 32px;
  //   border-radius: 4px;
  //   opacity: 1;

  //   background: linear-gradient(180deg, #0c2029 0%, #1f536a 100%);

  //   box-sizing: border-box;
  //   border: 1px solid;
  //   border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%) 1;
  //   // padding: 5px;
  //   // border-radius: 5px 5px 0 0;
  //   .tab-link {
  //     background: none;
  //     border: none;
  //     color: white; /* 默认白色文本 */
  //     // padding: 10px 20px;
  //     padding: 5px 10px;
  //     cursor: pointer;
  //     transition: all 0.3s ease;
  //     outline: none;
  //     width: 81px;
  //     height: 32px;
  //     border-radius: 4px;
  //     opacity: 1;

  //     text-align: center;
  //     font-size: 14px;
  //     font-weight: 350;
  //     line-height: normal;
  //     letter-spacing: 0em;

  //     font-variation-settings: 'opsz' auto;
  //     font-feature-settings: 'kern' on;
  //     /* 文本/一级 */
  //     color: #ffffff;

  //     text-shadow: 0px 4px 10px #000000;
  //   }

  //   .tab-link:hover {
  //     color: #17a2b8; /* 淡蓝色 */
  //   }

  //   .tab-link.active {
  //     // color: #17a2b8; /* 淡蓝色 */
  //     // background-color: #0056b3; /* 深蓝色背景 */
  //     // border-radius: 5px 5px 0 0;
  //     width: 81px;
  //     height: 32px;
  //     border-radius: 4px;
  //     opacity: 1;

  //     background: radial-gradient(74% 74% at 44% 100%, #00c8ff 0%, rgba(9, 200, 252, 0) 99%),
  //       linear-gradient(180deg, #0c2029 0%, #1f536a 100%), rgba(146, 183, 202, 0.302);

  //     box-sizing: border-box;
  //     border: 2px solid;
  //     border-image: linear-gradient(180deg, #09c8fc 0%, #09c8fc 100%) 2;

  //     text-align: center;
  //     font-size: 15px;
  //     font-weight: 500;
  //     line-height: normal;
  //     letter-spacing: 0em;

  //     font-variation-settings: 'opsz' auto;
  //     // font-feature-settings: 'kern' on;
  //     color: #09c8fc;

  //     // text-shadow: 0px 4px 10px #000000;
  //   }
  // }
</style>
