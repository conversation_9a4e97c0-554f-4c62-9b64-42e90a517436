<template>
  <div class="detail-info " v-if="activeItem && state.dataInfo">
    
    <div class="size-full">
      <div class="text-0.16rem font-[SourceHanSansCN-Medium] mb-0.16rem relative flex-center-between">
        <n-performant-ellipsis class="w-92% flex items-center">
          {{ activeItem?.name }}
        </n-performant-ellipsis>

        <MyIcon
          class="i-material-symbols:close-small-outline-rounded absolute right-0 top-0 text-0.2rem cursor-pointer"
          @click="activeItem = null"
        />
      </div>

      <div class="flex-col">
        <div v-for="el in state.current.params">
          <div v-if="el.customRender">
            <component :is="el.customRender(el, state.dataInfo)"></component>
          </div>

          <div v-else class="flex items-center mb-0.12rem text-0.14rem">
            <n-performant-ellipsis class="c-text_md mr-0.16rem w-0.92rem">
              {{ el.label }}
            </n-performant-ellipsis>

            <n-performant-ellipsis style="flex: 1">
              {{
                el.optionsField ? el.options.find(ele => ele.key === state.dataInfo?.[el.key])?.value : state.dataInfo?.[el.key]
              }}
            </n-performant-ellipsis>
          </div>
        </div>
      </div>

      <div class="flex justify-end b-t-(1px solid #E5E6EB) py-0.08rem">
        <n-button strong secondary class="mr-0.1rem" @click="onVideoClick">视频</n-button>

        <n-button strong secondary @click="onMonitorClick">监测</n-button>

        <!-- <n-button type="primary">详情</n-button> -->
      </div>
    </div>

    <div class="monitor-modal-box">
      <Monitor ref="monitorRef" v-model:monitorIndex="monitorIndex" v-model:videoIndex="videoIndex" />
    </div>
    <MyVideo ref="videoRef" v-model:monitorIndex="monitorIndex" v-model:videoIndex="videoIndex" />
  </div>
</template>
<script setup lang="tsx" name="DetailInfo">
  import { configs } from './config'
  import { getOptions } from '@/api'
  import Monitor from './Monitor.vue'
  import MyVideo from './Video.vue'

  const attrs = useAttrs()
  const activeItem = defineModel('activeItem')
  const state = reactive({
    current: null,
    dataInfo: null,
  })

  const monitorRef = ref()
  const videoRef = ref()

  const monitorIndex = ref(999)
  const videoIndex = ref(Number.MAX_SAFE_INTEGER)

  watch(
    () => activeItem.value,
    newVal => {
      if (!newVal) return
      state.dataInfo = null
      state.current = configs[newVal.tabVal2]

      state.current?.params?.forEach(el => {
        if (el?.optionsField) {
          getOptions(el.optionsField).then((res: any) => {
            el.options = res.data
          })
        }
      })

      state.current?.api &&
        state.current.api(newVal.id).then((res: any) => {
          state.dataInfo = state.current?.dealDataInfo(res.data)
        })
    },
  )

  const onMonitorClick = () => {
    if (activeItem.value.isLeftSource) {
      monitorRef.value.openModal({
        ...activeItem.value,
        objectName: activeItem.value.name,
        objectId: activeItem.value.id,
        objectType: activeItem.value.tabVal2.slice(0, 2),
      })
    } else {
      monitorRef.value.openModal({
        ...activeItem.value,
        objectName: activeItem.value.object_name,
        objectId: activeItem.value.object_id,
        objectType: activeItem.value.object_type,
      })
    }
  }
  //视频
  const onVideoClick = () => {
    videoRef.value.openModal({
      ...activeItem.value,
      objectName: activeItem.value.name,
      objectId: activeItem.value.id,
      objectType: activeItem.value.tabVal2.slice(0, 2),
    })
  }
</script>
<style lang="scss" scoped>
  .detail-info {
    position: absolute;
    right: 3.52rem;
    top: 0.2rem;
    z-index: 999;
    max-height: calc(100% - 0.4rem);
    width: 3.2rem;
    border-radius: 0.04rem;
    overflow: hidden;
    border: 1px solid #f2f3f5;
    background: #ffffff;
    box-shadow: 0px 0.04rem 0.1rem 0px rgba(0, 0, 0, 0.1);
    padding: 0.12rem;
  }

  :deep(.n-button) {
    font-size: 0.14rem;
    padding: 0 0.14rem;
    height: 0.32rem;
  }
</style>
