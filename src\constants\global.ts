import { title } from 'process'

export const colors = [
  '#3491FA',
  '#0FC6C2',
  '#A871E3',
  '#FAC859',
  '#91CB74',
  '#FC8452',
  '#6DC8EC',
  '#A871E3',
  '#1E9493',
  '#FF99C3',
  '#5D7092',
]
export const chartsBgColor = '#f0f0f0'
export const gradientColors = [
  { color: ['#248ff7', '#6851f1'] },
  { color: ['#8bd46e', '#09bcb7'] },
  { color: ['#dccb05', '#d5804d'] },
  { color: ['#61e210', '#4ab705'] },
  { color: ['#d1fa33', '#b5e20b'] },
  { color: ['#e8de1b', '#c5bb07'] },
  { color: ['#dccb05', '#d5804d'] },
  { color: ['#8bd46e', '#09bcb7'] },
  { color: ['#029863', '#d1e6eb'] },
  { color: ['#93CE07', '#FBDB0F'] },
  { color: ['#dC7D02', '#FD0100'] },
  { color: ['#AA069F', '#AC3B2A'] },
]
export const serviceColor = ['#20E5CB', '#F6CC57', '#DF2F2F']

export const projectColor = ['#09C8FC', '#F6CC57', '#09FCC7', '#097EFC', '#8209FC']
export const platforms = {
  weatherCode: '101250703', //天气编码  430922
  districtCode: '430922', //行政区划编码
  title: '桃花江灌区数字孪生平台',
}

export const map = {
  center: [113.5, 27.6],
  zoom: 10,
  minZoom: 3,
  maxZoom: 18,
  pitch: 45,
  bearing: 0,
  style: 'mapbox://styles/mapbox/streets-v11',
}

export const routeMenus = {
  reformation: { title: '农业水价改革', path: '/model' },
  'water-resource': { title: '水资源管理', path: '/data' },
  home: { title: '首页', path: '/home' },
  defense: { title: '水旱灾害防御', path: '/model' },
  intelligent: { title: '智能化管理及维护', path: '/model' },
}

export const waterSourceTypes = {
  1: { name: '灌溉调度', color: '#0091ea' },
  2: { name: '排水调度', color: '#00c853' },
}
//维养状态
export const maintenanceTypes = {
  1: { name: '待维养', color: '#0091ea' },
  2: { name: '维养中', color: '#00c853' },
  3: { name: '已完成', color: '#00c853' },
}
//巡检状态
export const patrolTypes = {
  1: { name: '未巡检', color: '#0091ea' },
  2: { name: '巡检中', color: '#00c853' },
  3: { name: '已巡检', color: '#00c853' },
  9: { name: '已作废', color: '#00c853' },
}
//应急响应状态
export const emergencyTypes = {
  1: { name: '待处置', color: '#0091ea' },
  2: { name: '已撤回', color: '#00c853' },
  3: { name: '处置中', color: '#0091ea' },
  4: { name: '待复核', color: '#00c853' },
  5: { name: '已完成', color: '#0091ea' },
}
//应急响应类型 1隐患问题 2异常情况
export const emergencyStatuses = {
  1: { name: '隐患问题', color: '#0091ea' },
  2: { name: '异常情况', color: '#00c853' },
}

//首页左侧基础信息
export const overview = {
  //水利工程概况
  project: [
    {
      codes: [5],
      code: 'HP002',
      title: '水库大坝',
      url: '/totalCanal',
      count: 77,
      gisLayer: 'thjgq:HP002',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [6],
      code: 'HP003',
      title: '水电站',
      url: '/totalCanal',
      count: 3,
      gisLayer: 'thjgq:HP003',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1],
      code: 'HP004',
      title: '灌区',
      url: '/totalCanal',
      count: 1,
      gisLayer: 'thjgq:HP004',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1, 2, 3, 4, 5, 6],
      code: 'HP005',
      title: '渠(沟)道',
      url: '/totalCanal',
      count: 899,
      gisLayer: 'thjgq:HP005',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1, 2, 3, 4, 5, 6],
      code: 'HP007',
      title: '水闸',
      url: '/totalCanal',
      count: 197,
      gisLayer: 'thjgq:HP007',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [9],
      code: 'HP008',
      title: '渡槽',
      url: '/totalCanal',
      count: 74,
      gisLayer: 'thjgq:HP008',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1],
      code: 'HP009',
      title: '倒虹吸',
      url: '/totalCanal',
      count: 19,
      gisLayer: 'thjgq:HP009',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [2],
      code: 'HP010',
      title: '泵站',
      url: '/totalCanal',
      count: 12,
      gisLayer: 'thjgq:HP010',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1, 2, 3, 4],
      code: 'HP011',
      title: '涵洞',
      url: '/totalCanal',
      count: 1615,
      gisLayer: 'thjgq:HP011',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [],
      code: 'HP015',
      title: '塘坝',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [],
      code: 'HP034',
      title: '闸站',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1],
      code: 'HP048',
      title: '跌水',
      url: '/totalCanal',
      count: 28,
      gisLayer: 'thjgq:HP048',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1],
      code: 'HP049',
      title: '陡坡',
      url: '/totalCanal',
      count: 4,
      gisLayer: 'thjgq:HP049',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1],
      code: 'HP050',
      title: '斗门',
      url: '/totalCanal',
      count: 189,
      gisLayer: 'thjgq:HP050',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1, 2, 3, 4],
      code: 'HP051',
      title: '渠首',
      url: '/totalCanal',
      count: 67,
      gisLayer: 'thjgq:HP051',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [1, 2, 3],
      code: 'HP023',
      title: '隧洞',
      url: '/totalCanal',
      count: 58,
      gisLayer: 'thjgq:HP023',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [5],
      code: 'HP040',
      title: '山塘',
      url: '/totalCanal',
      count: 126,
      gisLayer: 'thjgq:HP040',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
    {
      codes: [3],
      code: 'HP000',
      title: '骨干工程',
      url: '/totalCanal',
      count: 1,
      gisLayer: '',
      isActive: false,
      unit: '座',
      color: '#fff',
    },
  ],
  //物联感控体系站点
  site: [
    {
      code: 'SS',
      title: '墒情监测',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '处',
      color: '#fff',
    },
    {
      code: 'ZZ',
      title: '闸站水位监测',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '处',
      color: '#fff',
    },
    {
      code: 'ZQ',
      title: '雨水情监测',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '处',
      color: '#fff',
    },
    {
      code: 'EL',
      title: '安全监测',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '处',
      color: '#fff',
    },
    {
      code: 'FL',
      title: '量测水监测',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '处',
      color: '#fff',
    },
    {
      code: 'SL',
      title: '视频监测',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
    },
    {
      code: 'SLJC',
      title: '水量监测站',
      url: '/totalCanal',
      count: 0,
      gisLayer: '',
      isActive: false,
      unit: '处',
      color: '#fff',
    },
  ],
  //水库建设
  reservoir: [
    {
      code: '3',
      name: '中型',
      count: 0,
      title: '中型',
      url: '/totalCanal',
      gisLayer: 'thjgq:HP001',
      isActive: true,
      unit: '座',
      color: '#fff',
    },
    {
      code: '4',
      title: '小型',
      url: '/totalCanal',
      gisLayer: 'thjgq:HP001',
      isActive: false,
      count: 58,
      unit: '座',
      color: '#fff',
    },
  ],
  //渠系概况
  canal: [
    {
      code: '1',
      title: '总干渠',
      url: '/totalCanal',
      count: 1,
      gisLayer: 'thjgq:HP005',
      isActive: true,
      unit: '条',
      color: '#fff',
    },
    {
      code: '2',
      title: '干渠',
      url: '/totalCanal',
      count: 185,
      gisLayer: 'thjgq:HP005',
      isActive: true,
      unit: '条',
      color: '#fff',
    },
    {
      code: '3',
      title: '以河代渠_干',
      url: '/totalCanal',
      count: 1,
      gisLayer: 'thjgq:HP005',
      isActive: false,
      unit: '公里',
      color: '#fff',
    },
    {
      code: '4',
      title: '干沟',
      url: '/totalCanal',
      count: 35,
      gisLayer: 'thjgq:HP005',
      isActive: false,
      unit: '条',
      color: '#fff',
    },
    {
      code: '5',
      title: '支渠',
      url: '/totalCanal',
      count: 640,
      gisLayer: 'thjgq:HP005',
      isActive: false,
      unit: '条',
      color: '#fff',
    },
    {
      code: '6',
      title: '退水渠',
      url: '/totalCanal',
      count: 37,
      gisLayer: 'thjgq:HP005',
      isActive: false,
      unit: '条',
      color: '#fff',
    },
  ],
}

export const thjDrainageStatuses = {
  0: { name: '开启', color: '#09fcc7' },
  1: { name: '关闭', color: '#FF0000' },
}
export const waterWarningTypes = {
  1: { name: '临界预警', color: '#FFD035' },
  3: { name: '超限预警', color: '#DF2F2F' },
}
export const defenseColor = ['#09FCC7', '#09C8FC', '#F6CC57', '#FF7D00', '#DF2F2F']
export const defenses = {
  1: { value: 1, name: '特旱', color: '#DF2F2F' },
  2: { value: 2, name: '重旱', color: '#FF7D00' },
  3: { value: 3, name: '中旱', color: '#F6CC57' },
  4: { value: 4, name: '轻旱', color: '#09C8FC' },
  5: { value: 5, name: '无旱', color: '#09FCC7' },
}
