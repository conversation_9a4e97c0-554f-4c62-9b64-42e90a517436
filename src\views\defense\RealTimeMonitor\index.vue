<template>
  <MyCard name="实时气象监测">
    <div class="content pt-0.1rem h-4.5rem flex flex-col relative">
      <div class="flex mt-0.25rem flex-wrap">
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.5rem w-0.5rem ml-0.1rem"
            :style="{ background: `url(${getImageUrl(`defense/weather.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem text-0.14rem">天气</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.16rem">{{ state.configInfo.textDay }}</span>

              <!-- <span class="text-[#699DB2] text-0.14rem">座</span> -->
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.5rem w-0.5rem"
            :style="{ background: `url(${getImageUrl(`defense/temperature.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem text-0.14rem">温度</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.16rem">{{ state.configInfo.tempMin }} /{{ state.configInfo.tempMax }}</span>

              <span class="text-[#699DB2] text-0.14rem ml-0.05rem">℃</span>
            </div>
          </div>
        </div>

        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.5rem w-0.5rem"
            :style="{ background: `url(${getImageUrl(`defense/rainfall.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem text-0.14rem w-0.88rem">降雨量</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.16rem">{{ state.configInfo.precip || '-' }}</span>

              <span class="text-[#699DB2] text-0.14rem ml-0.05rem">mm</span>
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.5rem w-0.5rem ml-0.1rem"
            :style="{ background: `url(${getImageUrl(`defense/wind.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem text-0.14rem">风力</div>
            <div class="construction row-item-value w-0.88rem">
              <span class="text-[#ffffff] text-0.16rem w-0.88rem">
                {{ state.configInfo.windDirDay }}
                {{ state.configInfo.windScaleDay }}
              </span>
              <!-- <span class="text-[#699DB2] text-0.14rem">万m³</span> -->
            </div>
          </div>
        </div>

        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.5rem w-0.5rem"
            :style="{ background: `url(${getImageUrl(`defense/pressure.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem text-0.14rem">气压</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.16rem">{{ state.configInfo.pressure }}</span>

              <span class="text-[#699DB2] text-0.14rem ml-0.05rem">hpa</span>
            </div>
          </div>
        </div>
        <div class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3">
          <div
            class="h-0.5rem w-0.5rem"
            :style="{ background: `url(${getImageUrl(`defense/humidity.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="flex flex-col ml-0.1rem w-0.68rem">
            <div class="text-[#B2DAEA] mb-0.1rem text-0.14rem">湿度</div>
            <div class="construction row-item-value">
              <span class="text-[#ffffff] text-0.16rem">{{ state.configInfo.humidity }}</span>

              <span class="text-[#699DB2] text-0.14rem ml-0.05rem">%</span>
            </div>
          </div>
        </div>
      </div>

      <div class="flex flex-col flex-1 w-4.3rem h-2rem relative">
        <div class="absolute today-rainfall w-1.9rem h-0.25rem right-0.1rem top-0.02rem flex text-[#def2fc]">
          <div
            class="h-0.2rem w-0.2rem mx-0.06rem"
            :style="{ background: `url(${getImageUrl(`defense/today-rainfall.png`)}) no-repeat center / 100% 100%` }"
          ></div>
          今日累计降雨量
          <span class="text-[#ffffff] text-0.15rem mx-0.06rem">{{ twoDecimalFilter(state.rainSum, 2) }}</span>
          mm
        </div>

        <div
          class="left-0.16rem w-1.9rem h-0.27rem text-0.15rem px-0.2rem font-bold py-0.06rem text-[#B2DAEA] z-2 ml-0.16rem"
          :style="{ background: `url(${getImageUrl('intelligent/title-icon.png')}) no-repeat center / 100% 100%` }"
        >
          降雨趋势
        </div>

        <LineChart v-if="state.lineChartData" :dataSource="state.lineChartData" :year="year" class="mt-0.36rem ml-0.06rem" />
        <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
        <!-- <LineChart /> -->
        <!--  -->
        <!-- <LineChart :dataSource="state.lineChartData" :year="year" class="mt-0.36rem ml-0.14rem" /> -->
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="ReservoirInformation">
  import { getDrawCount, futureRainList } from '../services'
  import { getValueByKey, getWeatherForecast } from '@/api'
  import { twoDecimalFilter } from '@/utils/dealNumber.js'
  import LineChart from './LineChart.vue'
  import { platforms } from '@/constants'
  import dayjs from 'dayjs'

  const state = $ref({
    lineChartData: [],
    rainSum: 0,
    configInfo: {},
    yearList: [],
  })
  const year = ref(null)
  const router = useRouter()
  onMounted(() => {
    // unmountLoading()
    year.value = new Date().getFullYear()
    generateYearList()
    init()
  })
  watch(
    year,
    newVal => {
      if (newVal) {
        init()
      }
    },
    { immediate: true },
  )
  //
  const toTwinReservoir = () => {
    router.push('/twin-reservoir')
  }
  const generateYearList = () => {
    const currentYear = new Date().getFullYear()
    const startYear = 2023 // 生成从当前年份到前6年的年份数组
    for (let year = currentYear; year >= startYear; year--) {
      state.yearList.push({ label: String(year), value: String(year) })
    }
  }
  const init = () => {
    getWeatherForecast({ locationCode: platforms.weatherCode }).then(res => {
      state.configInfo = res.data[0]
      // state.configInfo = JSON.parse(res.data[0])
    })

    // let param = { year: year.value }
    // getDrawCount(param).then(res => {
    //   state.lineChartData = [
    //     {
    //       xData: res.data?.map(el => el.time),
    //       data: res.data?.map(el => [el.time, el.value]), // [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
    //     },
    //   ]
    //   console.log('state.lineChartData', state.lineChartData)
    // })
    // let param2 = `type=1&startTime=2024-12-25+17:00:00&endTime=2024-12-26+17:59:59`
    // let startTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    // let endTime = dayjs().format('YYYY-MM-DD HH:mm:ss')
    // const currentTime = ref(dayjs())
    // const startTime = computed(() => {
    //   // 检查当前时间是否早于8点
    //   if (currentTime.value.hour() < 8) {
    //     // 如果在早上8点之前，返回昨天的8点
    //     return dayjs().subtract(1, 'day').startOf('day')
    //   } else {
    //     // 否则，返回今天的8点
    //     return dayjs().startOf('day')
    //   }
    // })

    const now = dayjs()
    const isBefore8AM = now.hour() < 8
    let startTime = null
    let yesterday8AM = null
    let today8AM = null
    if (isBefore8AM) {
      // 如果当前时间在早上8点之前
      yesterday8AM = now.subtract(1, 'day').hour(8).minute(0).second(0).millisecond(0)
      startTime = yesterday8AM.format('YYYY-MM-DD HH:mm:ss')
    } else {
      // 如果当前时间在早上8点之后
      today8AM = now.hour(8).minute(0).second(0).millisecond(0)
      startTime = today8AM.format('YYYY-MM-DD HH:mm:ss')
    }

    // let param2 = dayjs(startTime).format('YYYY-MM-DD HH:mm:ss')
    // console.log('获取当前的降雨量 startTime', startTime.value, param2)
    futureRainList({
      type: 1,
      startTime: isBefore8AM ? yesterday8AM.format('YYYY-MM-DD HH:mm:ss') : today8AM.format('YYYY-MM-DD HH:mm:ss'),
      endTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    }).then(res => {
      state.lineChartData = [
        {
          xData: res.data?.map(el => dayjs(el.dateTime).format('MM-DD HH')),
          data: res.data?.map(el => {
            const site = el.sites[0]
            return [dayjs(el.dateTime).format('MM-DD HH'), site.rain]
          }), // [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
        },
      ]
      state.rainSum = res.data?.reduce((accumulator, item) => {
        const site = item.sites[0] // 假设每个 dateTime 只有一个站点
        if (site && site.rain !== null) {
          accumulator += parseFloat(site.rain)
        }
        return accumulator
      }, 0)
      // console.log('获取当前的降雨量 222', state.lineChartData)
    })
    // state.lineChartData = [
    //   {
    //     xData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    //     data: [6, 2, 3, 4, 5, 2, 7, 8, 9, 4, 11, 12],
    //   },
    // ]
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  .row-item {
    width: 0.96rem;
  }
  .today-rainfall {
    border-radius: 2px;
    opacity: 1;

    background: linear-gradient(0deg, #15799b -41%, rgba(21, 111, 141, 0) 90%);

    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 0%, rgba(255, 255, 255, 0) 100%) 1;

    font-family: Source Han Sans;
    font-size: 0.12rem;
    font-weight: 350;
    line-height: 0.25rem;
    // line-height: normal;
    // letter-spacing: 0em;
    // color: #def2fc;
  }
  .construction {
    // width: 0.9rem;
    // width: 1.1rem;
    // height: 0.32rem;
    // padding: 0.02rem 0;
    // border-radius: 4px;
    // opacity: 1;
    // background: red;
    // background: linear-gradient(273deg, rgba(114, 204, 240, 0.1) 3%, rgba(98, 188, 219, 0) 99%);
  }
</style>
