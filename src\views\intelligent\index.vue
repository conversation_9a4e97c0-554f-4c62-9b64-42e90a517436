<template>
  <div class="h-full w-full reactive">
    <div class="w-4.3rem absolute top-0.8rem left-0.2rem bottom-0.2rem flex-col z-2">
      <DeviceInfo class="mb-0.2rem" />
      <ProjectDevice class="mb-0.2rem flex-1" />
    </div>
    <div class="w-4.3rem absolute top-0.8rem right-0.2rem bottom-0.2rem flex-col z-4">
      <PatrolMaintenance class="mb-0.2rem" v-model:activeItem="activeItem" />
    </div>

    <!-- <AreaAndToolControl
      v-model="isShow"
      v-model:currentDistrict="currentDistrict"
      v-model:isDataMode="isDataMode"
      :isExpand="isExpand"
    /> -->

    <!-- <Right
      v-show="isShow.isShowRight"
      :activeTabLevel2="activeTabLevel2"
      :tabLevelOptions1="tabLevelOptions1"
      v-model:allData="allData"
    /> -->
    <div class="w-3rem absolute left-4.6rem bottom-0.7rem flex-col z-4">
      <MapLegend v-model:activeItem="activeItem" v-model:allData="allData" />
    </div>

    <Map
      :isShow="isShow"
      :allData="allData"
      v-model:activeItem="activeItem"
      v-model:activeTabLevel2="activeTabLevel2"
      :currentDistrict="currentDistrict"
      
    />
    <!-- :isDataMode="isDataMode" -->
    <!-- <Map /> -->
  </div>
</template>
<script setup lang="tsx" name="Intelligent">
  import { unmountLoading } from '@/core/loading'

  import Map from './Map'
  import PatrolMaintenance from './PatrolMaintenance'
  import AreaAndToolControl from './AreaAndToolControl'
  import DeviceInfo from './DeviceInfo'
  import ProjectDevice from './ProjectDevice'
  import MapLegend from './MapLegend'

  // import { objectCategoryFirstLevelList } from './services'

  const isShow = $ref({
    isShowLeft: true,
    isShowRight: true,
    isShowTool: true,
    isShowLabel: false,
  })

  let activeTabLevel2 = $ref(null)

  // 所有数据--右侧选中变化
  let allData = $ref(null)

  let activeItem = $ref(null)
  onMounted(() => {
    // unmountLoading()
  })
  let currentDistrict = $ref(null)
</script>
<style lang="scss" scoped></style>
