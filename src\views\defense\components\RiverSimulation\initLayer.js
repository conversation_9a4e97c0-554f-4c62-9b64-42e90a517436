import { MapboxOverlay } from '@deck.gl/mapbox'
import { GeoJsonLayer } from '@deck.gl/layers'
import * as turf from '@turf/turf'
import { getValueByKey } from '@/api/common'
import axios from 'axios'
import { mapBoundGeo } from '@/utils/map/utils/mapBounds.js'
import { hexToRgb } from '@/utils/map/utils/toDeckglRgb.js'

export default function initLayer(mapIns, mapOverlayIns, pointsGeojson, callback) {
  // 天地图
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-base-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${import.meta.env.VITE_TIANDI_BASE}/img_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[0].id,
  )
  // 天地图标注
  mapIns.addLayer(
    {
      id: 'mapbox-wmts-label-layer',
      type: 'raster',
      source: {
        type: 'raster',
        tiles: [
          `${import.meta.env.VITE_TIANDI_BASE}/cia_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
        ],
        tileSize: 256,
      },
    },
    mapIns.getStyle().layers[1].id,
  )

  Promise.all([
    axios(
      // 灌区的边界
      `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
    ),
    axios(
      // 水库边界
      `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&typeName=thjgq:HP001&maxFeatures=50&outputFormat=application/json&filter=<PropertyIsEqualTo><PropertyName>object_name</PropertyName><Literal>桃花江水库</Literal></PropertyIsEqualTo>`,
    ),
    axios(
      // 沟渠
      `${import.meta.env.VITE_GEOSERVER_BASE}/thjgq/ows?service=WFS&version=1.0.0&request=GetFeature&outputFormat=application/json&typeName=thjgq:HP005&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>2</Literal></PropertyIsEqualTo></Or>`,
    ),
  ]).then(res => {
    mapBoundGeo(res[0].data, mapIns, { top: 10, bottom: 10, left: 10, right: 10 })

    const guanquGeo = {
      ...res[0].data,
      features: [
        ...res[0].data.features.map(el => {
          return turf.centerOfMass({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
        }),
        ...res[0].data.features,
      ],
    }

    // 蒙版图层   //通过边界数据反选 达到挖洞效果
    mapIns.addLayer({
      id: 'mb-tag',
      type: 'fill',
      source: {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [
              [
                [-180, 90],
                [180, 90],
                [180, -90],
                [-180, -90],
              ],
              res[0].data.features[0].geometry.coordinates[0],
            ],
          },
        },
      },
      paint: { 'fill-color': 'rgba(0,0,0,0.55)' },
      layout: { visibility: 'visible' },
    })

    const shuikuGeo = {
      ...res[1].data,
      features: [
        ...res[1].data.features.map(el => {
          return turf.centerOfMass({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
        }),
        ...res[1].data.features,
      ],
    }

    const gouquGeo = {
      ...res[2].data,
      features: [
        ...res[2].data.features.map(el => {
          return turf.centerOfMass({ features: [el], type: 'FeatureCollection' }, { properties: el.properties })
        }),
        ...res[2].data.features,
      ],
    }

    mapOverlayIns.setProps({
      layers: [
        new GeoJsonLayer({
          id: 'geojson-layer-guanqu',
          data: guanquGeo,
          filled: true,
          pickable: false,
          stroked: true,
          // 描边颜色
          getLineColor: d => {
            return [...hexToRgb('#2BBCFF'), 255]
          },
          lineWidthMaxPixels: 1, // 周围最大线宽
          lineWidthMinPixels: 1, // 周围最小线宽

          pointType: 'text',
          getText: d => {
            return d.properties?.object_name
          },
          getTextColor: [29, 33, 41, 255],
          textCharacterSet: 'auto',
          getTextSize: 12,
          textOutlineColor: [255, 255, 255, 255],
          textOutlineWidth: 7,
          textFontSettings: { sdf: true, smoothing: 0.3 },
        }),
        new GeoJsonLayer({
          id: 'geojson-layer-shuiku',
          data: shuikuGeo,
          filled: true,
          pickable: false,
          stroked: false,

          getFillColor: d => {
            return [...hexToRgb('#12E952'), 255]
          },

          pointType: 'text',
          getText: d => {
            return d.properties?.object_name
          },
          getTextColor: [29, 33, 41, 255],
          textCharacterSet: 'auto',
          getTextSize: 12,
          textOutlineColor: [255, 255, 255, 255],
          textOutlineWidth: 7,
          textFontSettings: { sdf: true, smoothing: 0.3 },
        }),
        new GeoJsonLayer({
          id: 'geojson-layer-gouqu',
          data: gouquGeo,
          filled: true,
          pickable: false,
          stroked: true,
          // 描边颜色
          getLineColor: d => {
            return [...hexToRgb('#2374FF'), 255]
          },
          lineWidthMaxPixels: 1, // 周围最大线宽
          lineWidthMinPixels: 1, // 周围最小线宽

          pointType: 'text',
          getText: d => {
            return d.properties?.object_name
          },
          getTextColor: [29, 33, 41, 255],
          textCharacterSet: 'auto',
          getTextSize: 12,
          textOutlineColor: [255, 255, 255, 255],
          textOutlineWidth: 7,
          textFontSettings: { sdf: true, smoothing: 0.3 },
        }),
        new GeoJsonLayer({
          id: 'geojson-layer-points',
          data: pointsGeojson,
          filled: true,
          pickable: true,
          pointType: 'circle+text',

          // 处理 circle 描边
          stroked: true, // 是否描边
          getLineColor: [255, 255, 255, 255], // 描边颜色
          lineWidthMaxPixels: 1, // 周围最大线宽
          lineWidthMinPixels: 1, // 周围最小线宽
          // 处理 circle 实心
          getFillColor: d => {
            // if (this.activeItem?.projectId === d.properties.projectId) {
            //   return [...hexToRgb('#F5DD7E'), 255]
            // }
            return [...hexToRgb('#40a9ff'), 255]
          },
          getPointRadius: d => {
            return 8
          },
          pointRadiusMinPixels: 6, // 最小半径
          pointRadiusMaxPixels: 10, // 最大半径

          // 处理 text
          getText: d => {
            return d.properties?.projectName
          },
          getTextAnchor: 'middle',
          getTextPixelOffset: [0, 15],
          getTextColor: [29, 33, 41, 255],
          textCharacterSet: 'auto',
          getTextSize: 12,
          textOutlineColor: [255, 255, 255, 255],
          textOutlineWidth: 7,
          textFontSettings: { sdf: true, smoothing: 0.3 },

          onClick: opt => {
            const options = JSON.parse(JSON.stringify(opt.object))

            callback(options.properties)
          },
        }),
      ],
    })
  })
}
