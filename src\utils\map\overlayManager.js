import { MapboxOverlay } from '@deck.gl/mapbox'
import { ref, reactive } from 'vue'

// 全局 overlay 管理器
class OverlayManager {
  constructor() {
    this.overlays = new Map() // 存储每个地图实例的 overlay
    this.layers = new Map() // 存储所有图层
  }

  // 获取或创建地图的 overlay
  getOverlay(mapInstance) {
    if (!this.overlays.has(mapInstance)) {
      const overlay = new MapboxOverlay({
        id: 'global-deck-overlay',
        layers: [],
      })
      mapInstance.addControl(overlay)
      this.overlays.set(mapInstance, overlay)
    }
    return this.overlays.get(mapInstance)
  }

  // 添加图层
  addLayer(mapInstance, layerId, layer) {
    const overlay = this.getOverlay(mapInstance)
    this.layers.set(layerId, layer)
    this.updateOverlay(mapInstance)
  }

  // 移除图层
  removeLayer(mapInstance, layerId) {
    this.layers.delete(layerId)
    this.updateOverlay(mapInstance)
  }

  // 更新图层
  updateLayer(mapInstance, layerId, layer) {
    this.layers.set(layerId, layer)
    this.updateOverlay(mapInstance)
  }

  // 更新 overlay 的所有图层
  updateOverlay(mapInstance) {
    const overlay = this.getOverlay(mapInstance)
    const allLayers = Array.from(this.layers.values())
    overlay.setProps({
      layers: allLayers,
    })
  }

  // 清理地图相关的资源
  cleanup(mapInstance) {
    if (this.overlays.has(mapInstance)) {
      const overlay = this.overlays.get(mapInstance)
      mapInstance.removeControl(overlay)
      this.overlays.delete(mapInstance)
    }
    // 清理该地图相关的图层
    for (const [layerId, layer] of this.layers.entries()) {
      if (layerId.includes(mapInstance.id) || layerId.includes('map')) {
        this.layers.delete(layerId)
      }
    }
  }

  
  // 获取图层数量
  getLayerCount() {
    return this.layers.size
  }

  // 获取所有图层ID
  getLayerIds() {
    return Array.from(this.layers.keys())
  }
}

// 创建全局单例实例
export const overlayManager = new OverlayManager()

// Vue 组合式函数
export function useOverlayManager() {
  return {
    overlayManager,
    addLayer: (mapInstance, layerId, layer) => overlayManager.addLayer(mapInstance, layerId, layer),
    removeLayer: (mapInstance, layerId) => overlayManager.removeLayer(mapInstance, layerId),
    updateLayer: (mapInstance, layerId, layer) => overlayManager.updateLayer(mapInstance, layerId, layer),
    cleanup: (mapInstance) => overlayManager.cleanup(mapInstance),
    getLayerCount: () => overlayManager.getLayerCount(),
    getLayerIds: () => overlayManager.getLayerIds(),
  }
}
