<template>
  <div>
    <div class="chart-box" :style="{ width: chart1Width + 'rem' }">
      <Chart
        :width="chart1Width"
        v-if="dataSource.chart1.length > 0 && dataSource.chart1Stakes.length > 0"
        type="1"
        :minWL="minWL"
        :maxWL="maxWL"
        :dataSource="dataSource.chart1"
        :chart1Stakes="dataSource?.chart1Stakes"
        v-bind="$attrs"
      />
    </div>
    <div class="chart-box" :style="{ width: chart2Width + 'rem' }">
      <Chart
        :width="chart2Width"
        v-if="dataSource.chart2.length > 0 && dataSource.chart2Stakes.length > 0"
        type="2"
        :minWL="minWL"
        :maxWL="maxWL"
        :dataSource="dataSource.chart2"
        :chart2Stakes="dataSource?.chart2Stakes"
        v-bind="$attrs"
      />
    </div>
    <div class="chart-box" :style="{ width: chart3Width + 'rem' }">
      <Chart
        :width="chart3Width"
        v-if="dataSource.chart3.length > 0 && dataSource.chart3Stakes.length > 0"
        type="3"
        :minWL="minWL"
        :maxWL="maxWL"
        :dataSource="dataSource.chart3"
        :chart3Stakes="dataSource?.chart3Stakes"
        v-bind="$attrs"
      />
    </div>
  </div>
</template>

<script>
  import Chart from './chart.vue'

  export default {
    name: 'ProcessChart',
    components: { Chart },
    props: ['dataSource', 'width'],
    data() {
      return {
        chart1Width: null,
        chart2Width: null,
        chart3Width: null,
        minWL: 0,
        maxWL: 120,
      }
    },

    created() {
      const getIndexMAx = arr => {
        const maxNum = Math.max(...arr)
        return arr.indexOf(maxNum)
      }
      const getMin = arr => {
        const minNum = Math.min(...arr)
        return minNum
      }
      const getMax = arr => {
        const maxNum = Math.max(...arr)
        return maxNum
      }

      const num =
        getIndexMAx([
          this.dataSource.chart1Stakes[this.dataSource.chart1Stakes.length - 1],
          this.dataSource.chart2Stakes[this.dataSource.chart2Stakes.length - 1],
          this.dataSource.chart3Stakes[this.dataSource.chart3Stakes.length - 1],
        ]) + 1

      this.chart1Width =
        (
          this.dataSource.chart1Stakes[this.dataSource.chart1Stakes.length - 1] /
          this.dataSource[`chart${num}Stakes`][this.dataSource[`chart${num}Stakes`].length - 1]
        ).toFixed(2) * this.width

      this.chart2Width =
        (
          this.dataSource.chart2Stakes[this.dataSource.chart2Stakes.length - 1] /
          this.dataSource[`chart${num}Stakes`][this.dataSource[`chart${num}Stakes`].length - 1]
        ).toFixed(2) * this.width

      this.chart3Width =
        (
          this.dataSource.chart3Stakes[this.dataSource.chart3Stakes.length - 1] /
          this.dataSource[`chart${num}Stakes`][this.dataSource[`chart${num}Stakes`].length - 1]
        ).toFixed(2) * this.width

      const minNum = getMin([
        this.dataSource.chart1[1].data[this.dataSource.chart1[1].data.length - 1][1],
        this.dataSource.chart2[1].data[this.dataSource.chart2[1].data.length - 1][1],
        this.dataSource.chart3[1].data[this.dataSource.chart3[1].data.length - 1][1],
      ])
      this.minWL = +(minNum - 5).toFixed(0)

      const maxNum = getMax([
        this.dataSource.chart1[1].data[0][1],
        this.dataSource.chart2[1].data[0][1],
        this.dataSource.chart3[1].data[0][1],
      ])
      this.maxWL = +(maxNum + 10).toFixed(0)
    },
    methods: {},
  }
</script>

<style lang="scss" scoped>
  .chart-box {
    margin-bottom: 0.12rem;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.3);
    border: 1px solid rgba(54, 96, 103, 0.5);
    border-radius: 0.08rem;
    position: relative;
    height: 2.15rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
</style>
