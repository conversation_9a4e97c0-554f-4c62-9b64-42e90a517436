/* 覆盖uno.css */
.container {
  max-width: unset;
}

.mapboxgl-ctrl-logo {
  display: none !important;
}
.mapboxgl-ctrl-attrib-inner {
  display: none;
}

.mapboxgl-popup-anchor-right .mapboxgl-popup-tip {
  border-left-color: rgba(37, 89, 103, 1) !important;
}
.mapboxgl-popup-anchor-left .mapboxgl-popup-tip {
  border-right-color: rgba(37, 89, 103, 1) !important;
}
.mapboxgl-popup-anchor-top .mapboxgl-popup-tip,
.mapboxgl-popup-anchor-top-left .mapboxgl-popup-tip,
.mapboxgl-popup-anchor-top-right .mapboxgl-popup-tip {
  border-bottom-color: rgba(37, 89, 103, 1) !important;
}
.mapboxgl-popup-anchor-bottom .mapboxgl-popup-tip,
.mapboxgl-popup-anchor-bottom-left .mapboxgl-popup-tip,
.mapboxgl-popup-anchor-bottom-right .mapboxgl-popup-tip {
  border-top-color: rgba(37, 89, 103, 1) !important;
}

.mapboxgl-popup {
  .mapboxgl-popup-content {
    padding: 0;
    border-radius: 0;
    background-color: transparent;
  }
}

.n-button:not(.n-button--disabled):hover,
.n-button:not(.n-button--disabled):active,
.n-button:not(.n-button--disabled):focus {
  color: #fff;
}
.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active {
  background: #699db2;
  color: #09fcc7;
  border: #699db2;
}
.n-data-table .n-data-table-table {
  background-color: transparent;
}
.n-data-table .n-data-table-thead {
  background: rgba(85, 130, 148, 0.2);
  border: 1px solid;
  border-image: linear-gradient(180deg, #558294 0%, rgba(85, 130, 148, 0) 100%) 1;
}
.n-data-table .n-data-table-th .n-data-table-sorter {
  width: 72px;
}
.n-data-table .n-data-table-th,
.n-data-table .n-data-table-td {
  background-color: transparent;
  padding: 0.05rem 0.06rem;
}
.n-data-table .n-data-table-tr:nth-child(even) {
  background-color: rgba(85, 130, 148, 0.2);
}
.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover {
  background-color: rgba(85, 130, 148, 0.2);
  box-sizing: border-box;
  border: 1px solid;
  border-image: linear-gradient(90deg, rgba(85, 130, 148, 0) 0%, rgba(85, 130, 148, 0.5512) 44%, rgba(85, 130, 148, 0) 99%) 1;
}
.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover > .n-data-table-td {
  background-color: rgba(85, 130, 148, 0.2);
}
.n-data-table .n-data-table-th .n-data-table-th__title-wrapper .n-data-table-th__title {
  flex: inherit;
}
