<template><component :is="render()"></component></template>

<script setup lang="tsx" name="SwiperList">
  import { debounce } from 'lodash-es'
  import TextScroll from '@/components/TextScroll/index.vue'

  // Import Swiper Vue.js components
  import { Swiper, SwiperSlide } from 'swiper/vue'

  import { Autoplay, Scrollbar } from 'swiper/modules'
  // Import Swiper styles
  import 'swiper/css'
  // import "swiper/css/scrollbar";

  //自动轮播的配置
  const autoplayOptions = {
    delay: 12000, //8000,
    disableOnInteraction: false,
    pauseOnMouseEnter: true,
    reverseDirection: false,
  }

  const modules = [Autoplay, Scrollbar]

  const props = withDefaults(
    defineProps<{
      dataSource: any[]
      columns: any[]
      isShowHeader?: boolean
    }>(),
    {
      dataSource: () => [],
      columns: () => [],
      isShowHeader: true,
    },
  )

  const render = () => (
    <Swiper
      style='height: 100%; width: 100%'
      slidesPerView={1}
      spaceBetween={0}
      modules={modules}
      direction='vertical'
      loop={true}
      autoplay={autoplayOptions}
      scrollbar={{ draggable: false }}
    >
      {props.dataSource.map((item, index) => (
        <SwiperSlide>
          <TextScroll class='h-full flex items-center text-0.14rem text-[#CF3625]' text={item}></TextScroll>
        </SwiperSlide>
      ))}
    </Swiper>
  )
</script>

<style lang="scss" scoped></style>
