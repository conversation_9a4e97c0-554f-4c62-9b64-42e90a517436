# 全局 Overlay 管理器

## 问题背景

在使用 Mapbox 和 Deck.gl 时，每个图层组件都会创建自己的 `MapboxOverlay` 实例，这会导致创建多个 WebGL 上下文。浏览器对 WebGL 上下文有限制（通常最多 16 个），当图层过多时会出现 "WARNING: Too many active WebGL contexts. Oldest context will be lost" 错误。

## 解决方案

创建了一个全局的 overlay 管理器，所有图层共享同一个 `MapboxOverlay` 实例，从而避免 WebGL 上下文限制问题。

## 使用方法

### 1. 在图层组件中使用

```javascript
// 替换原来的方式
// import { MapboxOverlay } from '@deck.gl/mapbox'
// const stateRef = shallowRef({ deckOverlay: null })
// stateRef.value.deckOverlay = new MapboxOverlay({...})

// 使用全局管理器
import { useOverlayManager } from '@/utils/map/overlayManager.js'

const { addLayer, removeLayer, updateLayer } = useOverlayManager()
const layerId = 'your-unique-layer-id'

// 创建图层
const layer = new GeoJsonLayer({
  id: layerId,
  // ... 其他配置
})

// 添加/更新图层
updateLayer(mapInstance, layerId, layer)

// 组件卸载时清理
onUnmounted(() => {
  removeLayer(mapInstance, layerId)
})
```

### 2. 在地图组件中清理资源

```javascript
import { useOverlayManager } from '@/utils/map/overlayManager.js'

onUnmounted(() => {
  if (mapInstance) {
    const { cleanup } = useOverlayManager()
    cleanup(mapInstance)
  }
})
```

## API 文档

### `useOverlayManager()`

返回一个包含以下方法的对象：

- `addLayer(mapInstance, layerId, layer)` - 添加图层
- `removeLayer(mapInstance, layerId)` - 移除图层
- `updateLayer(mapInstance, layerId, layer)` - 更新图层
- `cleanup(mapInstance)` - 清理地图相关资源
- `getLayerCount()` - 获取当前图层数量
- `getLayerIds()` - 获取所有图层ID

### 参数说明

- `mapInstance`: Mapbox 地图实例
- `layerId`: 唯一的图层标识符
- `layer`: Deck.gl 图层实例

## 已更新的组件

以下组件已经更新为使用全局 overlay 管理器：

- `src/utils/map/layers/GeoJsonPolygon.vue`
- `src/utils/map/layers/GeoJsonLine.vue`
- `src/utils/map/layers/GeoJsonPoint.vue`
- `src/views/home/<USER>/index.vue`
- `src/views/defense/Map/index.vue`
- `src/views/intelligent/Map/index.vue`

## 测试

可以使用 `src/utils/map/overlayManagerDemo.vue` 组件来测试 overlay 管理器的功能。

## 注意事项

1. 确保每个图层都有唯一的 `layerId`
2. 组件卸载时记得调用 `removeLayer` 清理图层
3. 地图组件卸载时调用 `cleanup` 清理所有相关资源
4. 现在 `onLayerMounted` 回调传递的是 `layerId` 而不是 overlay 实例

## 优势

1. **解决 WebGL 上下文限制** - 所有图层共享一个 overlay
2. **更好的性能** - 减少 WebGL 上下文创建和销毁
3. **统一管理** - 集中管理所有图层
4. **易于调试** - 可以轻松查看当前图层状态
5. **向后兼容** - 最小化对现有代码的修改
