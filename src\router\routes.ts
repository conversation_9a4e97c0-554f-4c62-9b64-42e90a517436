import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/',
    name: 'root',
    component: Layout,
    children: [
      // {
      //   path: '/reservoir',
      //   name: 'reservoir',
      //   component: () => import('@/views/reservoir/index.vue'),
      //   meta: {
      //     title: '孪生水库',
      //     requiresAuth: true,
      //   },
      // },
      {
        path: '/water-resource',
        name: 'water-resource',
        component: () => import('@/views/water-resource/index.vue'),
        meta: {
          title: '水资源管理',
          requiresAuth: true,
        },
      },
      {
        path: '/',
        name: 'home',
        component: () => import('@/views/home/<USER>'),
        meta: {
          title: '首页',
          requiresAuth: true,
        },
      },
      {
        path: '/defense',
        name: 'defense',
        component: () => import('@/views/defense/index.vue'),
        meta: {
          title: '水旱灾害防御',
          requiresAuth: true,
        },
      },
      {
        path: '/intelligent',
        name: 'intelligent',
        component: () => import('@/views/intelligent/index.vue'),
        meta: {
          title: '智能化管理及维护',
          requiresAuth: true,
        },
      },
      {
        path: '/reformation',
        name: 'reformation',
        component: () => import('@/views/reformation/index.vue'),
        meta: {
          title: '农业水价改革',
          requiresAuth: true,
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/login/index.vue'),
  },
  {
    path: '/403',
    name: '403',
    component: () => import('@/views/error-pages/403.vue'),
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error-pages/404.vue'),
  },
  {
    path: '/500',
    name: '500',
    component: () => import('@/views/error-pages/500.vue'),
  },
  {
    path: '/demo',
    component: () => import('@/views/demo/index.vue'),
  },
  {
    path: '/twin-reservoir',
    name: 'TwinReservoir',
    component: () => import('@/views/twin-reservoir/index.vue'),
    meta: {
      title: '孪生水库',
      requiresAuth: true,
    },
  },
]

export default routes
