// 农业水价改革--行政区划列表
export function getBaseDistricts() {
  return request({
    url: '/view/user/water/getBaseDistricts',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  })
}

// 灌区用水户--用水户分布
export function getDistrictUserWater() {
  return request({
    url: '/view/user/water/getDistrictUserWater',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  })
}

// 灌区用水户--用水量趋势图
export function getMonthWaterList(params) {
  return request({
    url: '/view/user/water/getMonthWaterList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 农业用水实时监测
export function getRealUserList() {
  return request({
    url: '/view/user/water/getRealUserList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  })
}
// 灌区用水户--用水户列表
export function getUserWaterList(params) {
  return request({
    url: '/view/user/water/getUserWaterList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 用水异常预警
export function getWarningList() {
  return request({
    url: '/view/user/water/getWarningList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    }
  })
}

// 水利对象分类-根据分类统计对象
export function objectCategoryCountByFirstLevel(data) {
  return request({
    url: '/base/objectCategory/countByFirstLevel',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}
// 水利对象分类-一级分类
export function objectCategoryFirstLevelList() {
  return request({
    url: '/base/objectCategory/firstLevel/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//视频列表
export function getAllCameraList() {
  return request({
    url: '/base/camera/getAllCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}


// 行政区划树
export function districtGetTree() {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 水利对象分类-列表分页查询
export function objectCategoryObjectPage(data) {
  return request({
    url: '/base/objectCategory/object/page',
    method: 'post',
    data,
  })
}

//视频监控-根据水利对象查询视频点信息
export function getVideoById(data: object) {
  return request({
    url: '/base/camera/object/getCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}

// 水利对象分类-根据对象统计监控指标
export function listByIds(data) {
  return request({
    url: '/base/objectCategory/object/listByIds',
    method: 'post',
    data,
  })
}

// 获取视频播放地址
export function getVideoAddress(params) {
  return request({
    url: '/external/easyCvr/getPlayOnUrl',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}