<template>
  <div ref="domRef" class="w-full h-full"></div>
</template>

<script setup lang="tsx" name="WaterRainChart">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    (newVal: any) => {
      const data = [
        {
          name: '雨量',
          yAxisIndex: 0,
          data: newVal.map(el => [el.dateTime.substring(5), el.rain]),
        },
        {
          name: '累计雨量',
          yAxisIndex: 1,
          type: 'line',
          data: newVal.map(el => [el.dateTime.substring(5), el.sumRain]),
        },
      ]
      if (attrs.bottomIndexCodes.includes('waterLevel')) {
        data.push({
          name: '水位',
          yAxisIndex: 0,
          type: 'line',
          data: newVal.map(el => [el.dateTime.substring(5), el.waterLevel]),
        })
        if (attrs.bottomIndexCodes.includes('flow')) {
          data.push({
            name: '流量',
            yAxisIndex: 1,
            type: 'line',
            data: newVal.map(el => [el.dateTime.substring(5), el.flow]),
          })
        }
      } else {
        if (attrs.bottomIndexCodes.includes('flow')) {
          data.push({
            name: '流量',
            yAxisIndex: 0,
            type: 'line',
            data: newVal.map(el => [el.dateTime, el.flow]),
          })
        }
      }

      updateOptions(opt => {
        return getConfig(data)
      })
    },
  )

  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    const option = {
      grid: [
        // 配置第一个柱状图的位置
        {
          left: '8.9%',
          right: '6.5%',
          top: '11%',
          height: '25%',
        },
        // 配置第二个折线图位置
        {
          left: '8.9%',
          right: '6.5%',
          top: '48%',
          height: '42%',
        },
      ],
      tooltip: {
        show: true,
        trigger: 'axis',
        appendToBody: true,
        confine: true,
        // alwaysShowContent: true,
        className: 'echart-tooltip',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        padding: 10,
        textStyle: {
          fontWeight: 500,
          align: 'left',
          color: '#fff',
        },
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: 'rgba(22,45,72,1)',
            height: 10,
          },
          // crossStyle: { color: '#1664FF' },
          // lineStyle: { color: '#1664FF' },
        },

        // formatter函数动态修改tooltip样式
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += `<div style="line-height:1.2">
              <span style="font-size:0.12rem;color:var(--text-md);">${params[0].name.replace(/\-/g, '/')}</span>
            </div>`

            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = param.color //图例颜色

              function getUnit(seriesName) {
                switch (seriesName) {
                  case '雨量':
                    return 'mm'
                  case '累计雨量':
                    return 'mm'
                  case '水位':
                    return 'm'
                  case '流量':
                    return 'm³/s'
                  default:
                    return
                }
              }
              htmlStr += `
                <div style='display:flex;align-items:center; justify-content:space-between'>
                  <div style='display:flex;align-items:center;margin-right:0.2rem'>
                    <div style='display:inline-block;margin-right:4px;width:8px;height:8px;border-radius:8px;background-color:${color};'>
                    </div>
                    <div>${seriesName}:</div>
                  </div>
                  <div style="font-size:0.14rem;">
                    <span style="color:${color}"> ${value} ${getUnit(seriesName)}</span>
                  </div>
                </div>
                `
            }
            return htmlStr
          } else {
            return
          }
        },
      },
      legend: [
        {
          show: true,
          left: 530,
          top: 0,
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            color: '#C0DDFF',
          },
          data: [
            {
              name: '雨量',
              icon: 'rect',
            },
          ],
        },
        {
          show: true,
          left: 580,
          top: 0,
          itemWidth: 15,
          itemHeight: 2,
          textStyle: {
            color: '#C0DDFF',
          },
          data: [
            {
              name: '累计雨量',
            },
            {
              name: '水位',
            },
            {
              name: '流量',
            },
          ],
        },
      ],
      // 将上下两个tootip合成一个
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      xAxis: [
        {
          type: 'category',
          position: 'top',
          scale: true,
          axisLabel: {
            show: false,
          },
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: false,
          },
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed',
              color: '#fff',
            },
          },
        },
        {
          gridIndex: 1,
          type: 'category',
          scale: true,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#fff',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '雨量(mm)',
          inverse: true,
          nameTextStyle: {
            padding: [-200, 0, 0, 0], // 上右下左与原位置距离
            color: '#fff',
          },
          axisLabel: {
            color: '##fff',
          },
          scale: false,
          splitLine: {
            show: false,
            lineStyle: {
              type: 'dashed',
              color: '#999',
            },
          },
          splitNumber: 4, //设置坐标轴的分割段数
        },
        {
          type: 'value',
          name: '累计雨量(mm)',
          inverse: true,
          nameTextStyle: {
            padding: [-200, 0, 0, 0], // 上右下左与原位置距离
            color: '#C0DDFF',
          },
          axisLabel: {
            color: '##fff',
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.1)',
            },
          },
          splitNumber: 4, //设置坐标轴的分割段数
        },
        {
          type: 'value',
          name: data[2]?.name === '水位' ? '水位(m)' : '流量(m³/s)',
          nameTextStyle: {
            padding: [0, 0, 0, 6], // 上右下左与原位置距离
            color: '#C0DDFF',
          },
          gridIndex: 1,
          axisLabel: {
            color: '##fff',
          },
          scale: true,
          boundaryGap: [0, '10%'],
          splitLine: {
            show: true,
            lineStyle: {
              type: 'solid',
              color: 'rgba(255,255,255,0.1)',
            },
          },
          splitNumber: 4, //设置坐标轴的分割段数
        },
        {
          show: data.length === 4,
          type: 'value',
          name: '流量(m³/s)',
          nameTextStyle: {
            padding: [0, 0, 0, -5], // 上右下左与原位置距离
            color: '#C0DDFF',
          },
          gridIndex: 1,
          axisLabel: {
            color: '#fff',
          },
          scale: true,
          boundaryGap: [0, '10%'],
          splitLine: {
            show: false,
          },
          splitNumber: 4, //设置坐标轴的分割段数
        },
      ],
      dataZoom: [
        {
          show: false,
          type: 'inside',
          xAxisIndex: [0, 1], // 显示 0 1 的数据，这个要加，不加的话，悬浮提示就会出问题
        },
      ],
      series: [], // 遍历动态填充
    }

    data.forEach((item, index) => {
      if (item.name === '雨量') {
        option.series.push({
          name: '雨量',
          color: '#09FCC7',
          type: 'bar',
          xAxisIndex: 0,
          yAxisIndex: 0,
          barMaxWidth: 10,
          showBackground: false,
          hoverAnimation: true, // 悬浮的动画加上
          data: item.data,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: 'rgba(10,202,255,1)',
              },
              {
                offset: 1,
                color: 'rgba(10,202,255,0.4)',
              },
            ]),
          },
        })
      }
      if (item.name === '累计雨量') {
        option.series.push({
          name: '累计雨量',
          color: '#D2B461',
          type: 'line',
          xAxisIndex: 0,
          yAxisIndex: 1,
          smooth: true,
          symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          data: item.data,
        })
      }
      if (item.name === '水位') {
        option.series.push({
          name: '水位',
          color: '#09C8FC',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 2,
          smooth: true,
          symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          data: item.data,
        })
      }
      if (item.name === '流量') {
        option.series.push({
          name: '流量',
          color: '#74CF70',
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: data[2]?.name === '水位' ? 3 : 2,
          smooth: true,
          symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          data: item.data,
        })
      }
    })

    return option
  }
</script>

<style lang="scss" scoped></style>
