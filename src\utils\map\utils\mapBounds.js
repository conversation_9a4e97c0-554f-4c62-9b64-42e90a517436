import * as turf from '@turf/turf'

export function boundFlatLngLats(lngLatList, mapIns) {
  if (lngLatList?.length > 1) {
    let newData = lngLatList.filter(ele => ele?.[0] && ele?.[1])
    let lonList = Array.from(newData, ele => +ele[0])
    let latList = Array.from(newData, ele => +ele[1])
    let lonArr = lonList.sort()
    let latArr = latList.sort()
    let minLonLat = [lonArr[0], latArr[0]] // 西南角
    let maxLonLat = [lonArr[lonArr.length - 1], latArr[latArr.length - 1]] // 东北角

    mapIns.fitBounds([minLonLat, maxLonLat], {
      padding: { top: 50, bottom: 50, left: 50, right: 50 },
      animate: true,
    })
  } else {
    return false
  }
}

export function dealAllPoint(GeoJson) {
  let allPoint = []

  return () => {
    turf.geomEach(GeoJson, currentSegment => {
      // 获取点位
      const explode = turf.explode(currentSegment)
      allPoint.push.apply(allPoint, explode.features)
    })

    return allPoint
  }
}

export function mapBound(allPoint, mapIns) {
  // map偏移处理
  const enveloped = turf.envelope(turf.featureCollection(allPoint))
  mapIns.fitBounds(
    [
      [enveloped.bbox[0], enveloped.bbox[1]],
      [enveloped.bbox[2], enveloped.bbox[3]],
    ],
    {
      animate: true,
      padding: { top: 50, bottom: 50, left: 50, right: 50 },
    },
  )
}

// 根据一个geojson做偏移
export function mapBoundGeo(GeoJson, mapIns, padding) {
  let allPoint = []

  turf.geomEach(GeoJson, currentSegment => {
    // 获取点位
    const explode = turf.explode(currentSegment)
    allPoint.push.apply(allPoint, explode.features)
  })

  const enveloped = turf.envelope(turf.featureCollection(allPoint))
  mapIns.fitBounds(
    [
      [enveloped.bbox[0], enveloped.bbox[1]],
      [enveloped.bbox[2], enveloped.bbox[3]],
    ],
    {
      animate: true,
      duration: 1000,
      padding: padding || { top: 50, bottom: 50, left: 50, right: 50 },
    },
  )
}
