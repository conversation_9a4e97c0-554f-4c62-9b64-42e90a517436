<template>
  <MyCard name="用水异常预警">
    <!-- mb-0.2rem -->
    <div class="content relative pt-0.16rem h-4.22rem px-0.16rem">
      <div
        class="absolute w-0.96rem h-0.24rem top--0.27rem right-0.01rem cursor-pointer"
        :style="{ background: `url(${getImageUrl('reformation/more.png')}) no-repeat center / 100% 100%` }"
        @click="getMore"
      ></div>
      <vue3-seamless-scroll
        :list="dataSource"
        class="data-scroll"
        hover
        wheel
        :key="1"
        :step="1"
        v-if="dataSource?.length && currentNum == 0"
      >
        <div class="scroll-item relative text-0.14rem cursor-pointer" v-for="(el, index) in dataSource" :key="index">
          <div
            class="item-content w-100% h-1.36rem flex flex-col overflow-hidden"
            :key="index"
            :style="{ background: `url(${getImageUrl('right/content-type2.png')}) no-repeat center / 100% 100%` }"
          >
            <div
              class="text-overflow1 w-2.8rem px-0.12rem flex items-center mt-0.12rem text-0.16rem text-[#fff]"
              :title="el?.useWaterName"
            >
              {{ el?.useWaterName || '-' }}
            </div>
            <div class="info-item-content h-0.18rem flex mx-0.12rem justify-between w-[100%] mt-0.16rem">
              <div class="flex flex-row flex-5 h-[100%]">
                <div class="text-[#B2DAEA]">预警编码:&nbsp;</div>
                <div class="info-item-user text-[#FFFFFF]" :title="el.warnCode">
                  {{ el.warnCode || '-' }}
                </div>
              </div>
              <div class="flex flex-row flex-2 pr-0.26rem h-[100%]">
                <div class="text-[#B2DAEA]">行政区划:&nbsp;</div>
                <div class="info-item-dept text-[#FFFFFF]" :title="el.districtName">
                  {{ el.districtName || '-' }}
                </div>
              </div>
            </div>
            <div class="h-0.18rem px-0.12rem flex items-center mt-0.09rem text-[#fff]">
              <span class="text-[#B2DAEA]">预警时间:&nbsp;</span>
              {{ el?.warnDate || '-' }}
            </div>
            <div class="h-0.18rem w-[100%] px-0.12rem flex items-center mt-0.09rem text-[#fff]">
              <span class="text-[#B2DAEA]">预警内容:&nbsp;</span>
              <div class="text-overflow1 w-3rem" :title="el.warnContent">{{ el.warnContent }}</div>
              <!-- {{ el.warnContent + ' -- ' + (el.planEndTime ? dayjs(el.planEndTime).format('MM-DD HH:mm:ss') : '-') }} -->
            </div>
          </div>
          <!------------------- 右上角图标 ------------------>
          <div
            class="absolute h-0.25rem w-1.06rem top-0rem right-0rem items-center text-right pt-0.05rem pr-0.17rem text-0.14rem text-[#fff] z-9999"
            :style="{
              background:
                el.state == 1
                  ? `url(${getImageUrl('right/type1.png')}) no-repeat center / 100% 100%`
                  : `url(${getImageUrl('right/type0.png')}) no-repeat center / 100% 100%`,
              color: waterWarningTypes[el.state]?.color || '#fff',
            }"
          >
            {{ waterWarningTypes[el.state]?.name || '-' }}
          </div>
        </div>
      </vue3-seamless-scroll>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="DeviceInfo">
  import { getWarningList } from '../services'
  import { thjDrainageStatuses, serviceColor, projectColor } from '@/constants'
  import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
  import { dealNumber } from '@/utils'
  import { waterWarningTypes } from '@/constants'
  const state = reactive({
    info: {},
    multiPieChartData: [], // 饼图数据
    total: 0,
    multiList: [], // 项目列表数据
  })

  // const handleRainClick = (item, key) => {
  //   currentNum.value = key
  // }
  const getMore = () => {
    // getValueByKey('thj.drought.url').then(res => {
    //   // const url = `${res.data}?&token=${userStore.token}`
    //   const url = `${res.data}`
    //   window.open(url, '_blank')
    // })
  }
  const dataSource = ref([])
  const currentNum = ref(0)

  //  数据格式
  //     {
  //       "id": 2,
  //       "useWaterId": 1,
  //       "useWaterCode": "YS430922101001",
  //       "useWaterName": "取水户1",
  //       "districtCode": "430922101",
  //       "districtName": "修山镇",
  //       "warnCode": "ys00001",
  //       "warnDate": "2025-06-17 10:04:05",
  //       "warnContent": "用水量已达许可值90%",
  //       "status": 1
  //     }
  onMounted(() => {
    getList()
  })
  const getList = () => {
    getWarningList().then(res => {
      dataSource.value = res?.data || []
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }

  .data-scroll {
    height: 100%;
    // background: red;
    overflow: hidden;

    .scroll-item {
      // margin-right: 0.2rem;
      width: 100%;
      height: 1.36rem;
      // background: url('@/assets/images/intelligent/chart.png') no-repeat;
      background-size: 100% 100%;
      margin-bottom: 0.17rem;
    }
  }
</style>
