<template>
  <ThjModal ref="$modal" :showFooter="false">
    <!-- class="h-94% pt-2% flex relative" -->
    <div class="w-96% h-96% ml-2% mr-2% mt-1% mb-3% flex relative border-radius-0.16rem">
      <i class="light block absolute bottom--0.26rem"></i>
      <div class="list-card w-3.47rem p-0.15rem">
        <div
          class="row w-3.15rem h-0.39rem lh-0.35rem flex mb-0.08rem px-0.08rem cursor-pointer border-radius-0.1rem"
          v-for="el in state.list"
          :key="el.modelId"
          :class="{ active: state.selectedDispatch === el.resvrDispId }"
          @click="changeDispatch(el)"
        >
          <label class="text-0.16rem font-500">{{ el.caseName }}</label>
          <a class="w-0.64rem h-0.21rem lh-0.21rem text-center mt-0.07rem ml-auto c-#09FCC7 text-0.12rem check">查看结果</a>
        </div>

        <n-pagination
          class="mt-0.3rem"
          size="small"
          :default-page-size="12"
          v-model:page="state.pageNum"
          @update:page="changePageNum"
          :item-count="state.total"
        />
      </div>
      <div class="flex-1 ml-0.15rem">
        <DispatchResult :dispatchId="state.selectedDispatch" />
      </div>
    </div>
  </ThjModal>
</template>
<script setup lang="ts" name="DispatchModel">
  import { getDispatchList } from '../services'
  const [$modal, okLoading, loading, title] = useModal()

  import DispatchResult from './DispatchResult.vue'

  const attrs = useAttrs()

  const state = reactive({
    list: [],
    total: 0,
    selectedDispatch: undefined,
    pageNum: 1,
    pageSize: 16,
  })

  const openModal = async () => {
    getList()

    $modal.value?.open({
      loading: false,
      contentStyle: { width: '18rem', height: '9rem' },
      headerStyle: { display: 'none' },
      title: '水库调度模拟',
      onClose: () => {
        // 此处重置状态
        state.selectedDispatch = undefined
        attrs.onClose && attrs.onClose()
      },
    })
  }
  defineExpose({ openModal })

  const getList = () => {
    getDispatchList({ pageNum: state.pageNum, pageSize: state.pageSize }).then(res => {
      state.list = res?.data?.data
      state.total = Math.ceil(res?.data?.total / state.pageSize)
      state.selectedDispatch = state.list[0]?.resvrDispId
    })
  }

  const changeDispatch = row => {
    state.selectedDispatch = row.resvrDispId
  }

  const changePageNum = val => {
    state.pageNum = val
    getList()
  }
</script>
<style lang="scss" scoped>
  .light {
    width: 3.69rem;
    height: 0.58rem;
    background: radial-gradient(37% 36% at 50% 50%, #5defff 0%, rgba(42, 190, 206, 0) 100%);
  }
  .list-card {
    z-index: 9;
    background:
      linear-gradient(180deg, rgba(6, 55, 50, 0) 27%, rgba(12, 164, 149, 0.52) 98%),
      linear-gradient(180deg, rgba(2, 109, 143, 0.6) -5%, rgba(4, 79, 120, 0.6) 98%);
    border: 1px solid rgba(42, 190, 206, 0.62);
  }
  .row {
    border-radius: 0.04rem;
    background: rgba(9, 200, 252, 0.2);
    border: 1px solid rgba(105, 157, 178, 0.5);
    &.active {
      border-radius: 4px;
      background: #158ca0;
      box-sizing: border-box;

      border: 2px solid #09fcc7;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        top: 0.06rem;
        left: 0.01rem;
        width: 0.1rem;
        height: 0.24rem;
        background: url('@/assets/images/dispatch-row-dot.png') no-repeat center / 100% 100%;
      }
      &::after {
        content: '';
        position: absolute;
        transform: rotate(180deg);
        top: 0.06rem;
        right: 0.01rem;
        width: 0.1rem;
        height: 0.24rem;
        background: url('@/assets/images/dispatch-row-dot.png') no-repeat center / 100% 100%;
      }
      .check {
        color: #fff;
        border-radius: 2px;
        // background: #09fcc7;
        background: rgba(9, 252, 199, 0.8);
      }
    }
  }
  .n-pagination {
    justify-content: flex-end;
  }
</style>
