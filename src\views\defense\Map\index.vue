<template>
  <div class="h-full w-full z-1 absolute">
    <div class="w-full h-full relative">
      <n-spin v-if="state.loading" class="absolute-center z-9999999 size-full bg-[rgba(255,255,255,0)]" />
      <Mapbox
        :onMapMounted="onMapMounted"
        :onMapStyleLoad="onMapStyleLoad"
        :mapZoom="state.zoom"
        :onMapZoomEnd="onMapZoomEnd"
        :onMapDragEnd="onMapDragEnd"
      />

      <GeoJsonPolygon
        v-for="layer in state.layers.MultiPolygon"
        :key="layer.id"
        v-bind="{ ...layer, ...attrs }"
        v-model:activeItem="activeItem"
        :zoom="state.zoom"
        @mounted="onLayerMounted"
      />

      <GeoJsonLine
        v-for="layer in state.layers.MultiLineString"
        :key="layer.id"
        v-bind="{ ...layer, ...attrs }"
        v-model:activeItem="activeItem"
        :zoom="state.zoom"
        @mounted="onLayerMounted"
      />

      <GeoJsonPoint
        v-for="layer in state.layers.Point"
        :key="layer.id"
        v-bind="{ ...layer, ...attrs }"
        v-model:activeItem="activeItem"
        v-model:newActiveItem="newActiveItem"
        :zoom="state.zoom"
        @mounted="onLayerMounted"
      />
    </div>
  </div>
</template>

<script setup lang="tsx" name="Map">
  import { unmountLoading } from '@/core/loading'
  import dayjs from 'dayjs'
  import axios from 'axios'
  import { createVNode, render } from 'vue'
  import { debounce } from 'lodash-es'
  import mapboxgl from 'mapbox-gl'
  import gcoord from 'gcoord'
  import * as turf from '@turf/turf'
  import { getValueByKey } from '@/api'
  import { listByIds } from '../services.ts'

  // import MapTool from './components/tool.vue'
  // import MapStyle from './components/mapStyle.vue'
  import { colors, getNewColor, getNewIcon } from '@/utils/map/config.ts'
  import { getPulsingDot } from '@/utils/map/utils/pulsingDot.js'
  import { clearSourceAndLayer } from '@/utils/map/utils/mapUtils.js'
  import GeoJsonPolygon from '@/utils/map/layers/GeoJsonPolygon.vue'
  import GeoJsonLine from '@/utils/map/layers/GeoJsonLine.vue'
  import GeoJsonPoint from '@/utils/map/layers/GeoJsonPoint.vue'
  import useAreaDashLayer from '@/utils/map/layers/useAreaDashLayer.ts'
  import { dealAllPoint, mapBound, mapBoundGeo } from '@/utils/map/utils/mapBounds.js'
  import { useOverlayManager } from '@/utils/map/overlayManager.js'

  import PopupContent from './components/Popup/PopupContent.vue'
  import { mapboxPopup } from './components/Popup/popup.js'
  const attrs = useAttrs()

  const activeTabLevel2 = defineModel('activeTabLevel2')
  const activeItem = defineModel('activeItem')
  const newActiveItem = defineModel('newActiveItem')

  const state = reactive({
    mapIns: null,
    zoom: 10,
    drawIns: null,

    districtGeojson: null,

    loading: false,
    overlays: [],
    layers: {
      MultiPolygon: [],
      MultiLineString: [],
      Point: [],
    },

    activeMarkerIns: null,

    pointsInRect: [],
    mapSiteIndexExpired: 0,
    thjgq: '',
    expiredValue: null,
    showPopupZoom: 13.99,
    mapPopupIns: [],
    maxPopupIndex: 1,
    showPopupItem: [],
    newFeatures: [],
  })

  let activeMapStyle = $ref(null)

  useAreaDashLayer({
    mapIns: toRefs(state).mapIns,
    id: 'current-district',
    geojson: toRefs(state).districtGeojson,
    zoom: toRefs(state).zoom,
  })

  getValueByKey('map.site.index.expired').then(res => {
    state.mapSiteIndexExpired = +res.data
  })

  getValueByKey('gis.thj.gq').then(res => {
    state.thjgq = res.data
  })

  watch(
    () => newActiveItem.value,
    async newVal => {
      let temp2 = state.newFeatures?.find(feature => feature.properties.object_name == newVal.name)
      // let detail = {
      //   siteId: 232,
      //   siteCode: 'XZ-006',
      //   siteName: '塘埂桥',
      //   longitude: '120.6832006253235',
      //   latitude: '30.86635940032237',
      //   status: 2,
      //   siteClass: '2',
      //   wlStatus: 3,
      //   lngLat: new mapboxgl.LngLat(120.6832006253235, 30.86635940032237),
      //   img: 'http://localhost:8082/src/assets/images/legends/2-3.png',
      //   activeImg: 'http://localhost:8082/src/assets/images/legends/2-3-active.png',
      // }
      // ZQ: '水雨情监测' SL: '量测水监测'  ZZ: '水位监测'  SS: '墒情监测'  EL: '安全监测'  FL: '流量站' SLJC: '水量监测站'

      let detail = {
        ...temp2,
        ...temp2.properties,
        lngLat: temp2.geometry.coordinates,
        // id: 89,
        // lngLat: new mapboxgl.LngLat(111.96758761, 28.45031757), // [111.96758761, 28.45031757],//new mapboxgl.LngLat(lng, lat);
        // longitude: '111.96758761',
        // latitude: '28.45031757',
        // object_id: 91, // 121, //89,
        // object_name: '龙塘墒情站',
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        // map_class: 'EL',
        // object_type: 'MS',
        // color: '#6956D3',
        // icon: 'http://localhost:8080/src/assets/images/map/siteSS.png',
      }

      const el = document.createElement('div')

      // el.innerHTML = `
      //     <div style=" width:22px;height:25px;background:url(${detail?.icon}) no-repeat center / 100% 100%;">
      //     </div>`

      state.activeMarkerIns = new mapboxgl.Marker({ element: el, anchor: 'bottom' }).setLngLat(detail?.lngLat).addTo(state.mapIns)

      state.mapPopupIns?.remove?.()

      state.mapPopupIns = mapboxPopup(state.mapIns, {
        ...detail,
        ...newVal,
        color: '#fff', //detail.wlStatus && getColor(detail),
        name: newVal.name,
        detailInfo: detail,
        onPopupClose: () => {
          activeItem.value = null
          state.mapPopupIns.remove()
          // removePopup()
          // state.activeItem = null
        },
      })

      state.activeMarkerIns.getElement().style['z-index'] = '9999910'
      state.mapPopupIns.getElement().style['z-index'] = '9999911'
    },
  )

  const removePopup = () => {
    let arr = state.mapPopupIns //.slice()
    if (arr.length > 0) {
      for (let i = 0; i < arr.length; i++) {
        arr[i].remove()
      }
    }
    state.mapPopupIns = []
  }

  //  监听触发状态的值
  watch(
    () => activeItem.value,
    async (newVal: any) => {
      if (newVal === null) return
      newVal?.features?.forEach(elem => {
        elem.properties = {
          ...elem.properties,
          // ...newVal.items.find(element => element.code === elem.properties.map_class),
          color: getNewColor(newVal.code, newVal.section, ''),
          // icon: `url(${getImageUrl(`map/${newVal.section}${newVal.code}.png`)})`,
          icon: `${getImageUrl(`map/${newVal.section}${newVal.code}.png`)}`,
          // icon: getNewIcon('', newVal.section, '', newVal.code),
        }
      })
      if (!state.layers.Point?.length) {
        state.mapPopupIns.forEach(el => el.remove())
      }
      if (!newVal?.isActive) {
        state.mapIns.removeControl(state.overlays[newVal.id])
        state.overlays[newVal.id] = null
        Object.keys(state.layers).forEach(key => {
          state.layers[key].some(element => element.id === newVal.id) &&
            state.layers[key].splice(
              state.layers[key].findIndex(element => element.id === newVal.id),
              1,
            )
        })
      } else {
        // console.log('*** click item newVal 173', newVal)
        state.newFeatures = state.newFeatures.concat(newVal.features)
        // console.log('*** click item newVal 185', state.newFeatures)
        dealLayers(newVal, { objectCategoryCode: newVal.id }, { tabLevel1: 'water' }, false)
      }
    },
  )

  const dealLayers = (data, tabVal2, allData, isLeft) => {
    if (data?.features?.[0]?.geometry?.type === 'MultiPolygon') {
      const currentIdx = state.layers.MultiPolygon.findIndex(element => element.tabLevel2 === tabVal2.objectCategoryCode)
      if (currentIdx > -1) {
        state.layers.MultiPolygon[currentIdx] = {
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        }
      } else {
        state.layers.MultiPolygon.push({
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        })
      }
    }
    if (data?.features?.[0]?.geometry?.type === 'MultiLineString') {
      state.loading = true

      const currentIdx = state.layers.MultiLineString.findIndex(element => element.tabLevel2 === tabVal2.objectCategoryCode)
      if (currentIdx > -1) {
        state.layers.MultiLineString[currentIdx] = {
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        }
      } else {
        state.layers.MultiLineString.push({
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        })
      }
    }
    if (data?.features?.[0]?.geometry?.type === 'Point') {
      state.loading = true

      const currentIdx = state.layers.Point.findIndex(element => element.tabLevel2 === tabVal2.objectCategoryCode)
      if (currentIdx > -1) {
        state.layers.Point[currentIdx] = {
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        }
      } else {
        state.layers.Point.push({
          mapIns: state.mapIns,
          geojson: data,
          id: tabVal2.objectCategoryCode,
          tabLevel1: allData.tabLevel1,
          tabLevel2: tabVal2.objectCategoryCode,
          isLeft: isLeft || false,
          gisLayer: tabVal2.gisLayer,
        })
      }
    }

    setTimeout(() => {
      nextTick(() => {
        state.loading = false
        if (state.layers.Point?.length) {
          dealPointsInRect('pointDataChange')
        }
      })
    }, 500)
  }

  // 飘窗
  watch(
    () => state.pointsInRect,
    newVal => {
      state.mapPopupIns.forEach(el => el.remove())
      newVal?.forEach(el => {
        const ins = new mapboxgl.Popup({
          closeOnClick: false,
          closeButton: true,
          offset: [0, -25],
          maxWidth: 'none',
          anchor: 'bottom',
        })

        const elPopup = document.createElement('div')
        const vNodePopup = createVNode(PopupContent, {
          ...el.properties,
          onClick: () => {
            state.maxPopupIndex += 1
            ins.getElement().style.zIndex = state.maxPopupIndex
          },
        })
        render(vNodePopup, elPopup)

        ins.setLngLat(el.geometry.coordinates).setDOMContent(elPopup).addTo(state.mapIns)

        state.mapPopupIns.push(ins)
      })
    },
  )

  const dealActivePoint = () => {
    if (!state.mapIns.getLayer('layer-with-pulsing-dot')) return
    const options = JSON.parse(JSON.stringify(state.mapIns.getSource('dot-point')._options))
    const dotLayer = JSON.parse(JSON.stringify(state.mapIns.getLayer('layer-with-pulsing-dot')))

    clearSourceAndLayer(state.mapIns, ['dot-point'], ['layer-with-pulsing-dot'])

    state.mapIns.addSource('dot-point', options)
    state.mapIns.addLayer(dotLayer)
  }

  const onLayerMounted = (objectCategoryCode, layerId) => {
    // 存储图层ID而不是overlay实例
    state.overlays[objectCategoryCode] = layerId
  }

  const onStyleItemClick = (el, idx) => {
    activeMapStyle = el

    el.setMapLabel()
    el.setBaseMapStyle()
  }
  const onMapMounted = map => {
    state.mapIns = map
    const pulsingDot = getPulsingDot(state.mapIns, 150)
    state.mapIns.addImage('pulsing-dot', pulsingDot, { pixelRatio: 2 })
    nextTick(() => {
      setTimeout(() => {
        state.mapIns.addLayer(
          {
            id: 'mapbox-wmts-label-layer',
            type: 'raster',
            source: {
              type: 'raster',
              tiles: [
                `${import.meta.env.VITE_TIANDI_BASE}/cia_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=cia&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
          },
          // state.mapIns.getStyle().layers[0].id,
        )
        state.mapIns.addLayer(
          {
            id: 'mapbox-wmts-base-layer',
            type: 'raster',
            source: {
              type: 'raster',
              tiles: [
                `${import.meta.env.VITE_TIANDI_BASE}/img_w/wmts?tk=${import.meta.env.VITE_TIANDI_TK}&service=WMTS&request=GetTile&version=1.0.0&style=default&tilematrixSet=w&format=tiles&width=256&height=256&layer=img&tilematrix={z}&tilerow={y}&tilecol={x}`,
              ],
              tileSize: 256,
            },
          },
          state.mapIns.getLayer('mapbox-wmts-label-layer') ? 'mapbox-wmts-label-layer' : attrs.mapIns.getStyle().layers[0].id,
        )
        // `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=thjgq:HP004&filter=<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>1</Literal></PropertyIsEqualTo></Or>`,
        axios(`${state.thjgq}`).then(res => {
          // console.log('get geojson', res)
          const data = res.data || null
          // state.districtGeojson = res.data
          mapBoundGeo(data, state.mapIns, { top: 150, bottom: 150, left: 150, right: 150 })

          //蒙版边界
          state.mapIns.addLayer({
            id: 'mb-line',
            type: 'line',
            source: {
              type: 'geojson',
              data: data, //区划的面数据
            },
            paint: {
              'line-color': 'rgba(9, 236, 255, 1)',
              'line-width': 3,
            },
            layout: {
              visibility: 'visible',
            },
          })
          state.mapIns.addLayer({
            id: 'background-layer',
            type: 'background',
            paint: {
              'background-color': 'rgba(21, 140, 160, 0.22)',
            },
          })
          // 蒙版图层   //通过边界数据反选 达到挖洞效果
          state.mapIns.addLayer({
            id: 'mb-tag',
            type: 'fill',
            source: {
              type: 'geojson',
              data: {
                type: 'Feature',
                geometry: {
                  type: 'Polygon',
                  coordinates: [
                    [
                      [-180, 90],
                      [180, 90],
                      [180, -90],
                      [-180, -90],
                    ],
                    data.features[0].geometry.coordinates[0],
                  ],
                },
              },
            },
            paint: { 'fill-color': 'rgba(0,0,0,0.4)' },
            layout: { visibility: 'visible' },
          })
        })
        unmountLoading()
      }, 100)
    })
  }
  const onMapStyleLoad = map => {
    if (state.mapIns) {
      const pulsingDot = getPulsingDot(state.mapIns, 150)
      state.mapIns.addImage('pulsing-dot', pulsingDot, { pixelRatio: 2 })
      dealActivePoint()
    }
  }

  const onMapZoomEnd = (currentZoom, e) => {
    state.zoom = currentZoom

    if (e.originalEvent) {
      // 区别出fitBounds 导致的触发
      dealPointsInRect()
    } else {
      if (state.zoom < state.showPopupZoom) {
        state.mapPopupIns.forEach(el => el.remove())
      }
    }
  }

  const onMapDragEnd = () => {
    dealPointsInRect()
  }

  watch(
    () => attrs.isDataMode,
    newVal => {
      if (newVal) {
        dealPointsInRect()
      } else {
        state.mapPopupIns.forEach(el => el.remove())
      }
    },
  )

  const dealPointsInRect = (type?: string) => {
    if (!attrs.isDataMode) return
    if (state.zoom < state.showPopupZoom) {
      state.mapPopupIns.forEach(el => el.remove())

      return
    }

    const bound1 = state.mapIns.getBounds()
    // 把bound的padding设为0
    state.mapIns.fitBounds(
      [
        [bound1._ne.lng, bound1._ne.lat],
        [bound1._sw.lng, bound1._sw.lat],
      ],
      {
        animate: false,
        duration: 0,
        padding: { top: 0, bottom: 0, left: 0, right: 0 },
      },
    )

    setTimeout(() => {
      // 此时bound是整个地图窗口大小
      const bound2 = state.mapIns.getBounds()

      const boundRect = {
        type: 'Feature',
        properties: {},
        geometry: {
          type: 'Polygon',
          coordinates: [
            [
              [bound2._ne.lng, bound2._ne.lat],
              [bound2._ne.lng, bound2._sw.lat],
              [bound2._sw.lng, bound2._sw.lat],
              [bound2._sw.lng, bound2._ne.lat],
              [bound2._ne.lng, bound2._ne.lat],
            ],
          ],
        },
      }

      const points = []
      state.layers.Point.forEach(el => {
        points.push(...turf.pointsWithinPolygon(el.geojson, boundRect).features)
      })

      const pointsInRect = JSON.parse(JSON.stringify(state.pointsInRect))
      let params
      if (state.expiredValue && dayjs().valueOf() < state.expiredValue) {
        params = points.filter(
          el => !pointsInRect.some(ele => `${el.properties.object_type}_${el.properties.object_id}` == ele.properties.objectUid),
        )
        if (params.length === 0 && type !== 'pointDataChange') {
          state.pointsInRect = JSON.parse(JSON.stringify(state.pointsInRect))
          state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          return
        }
        listByIds(params.map(ele => ({ objectId: ele.properties.object_id, objectType: ele.properties.object_type }))).then(
          res => {
            state.pointsInRect = (res.data || [])
              .map((ele, i) => ({
                ...params?.[i],
                properties: { ...params?.[i].properties, ...ele },
              }))
              .concat(
                pointsInRect.filter(el =>
                  points.some(ele => `${ele.properties.object_type}_${ele.properties.object_id}` == el.properties.objectUid),
                ),
              )
            state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          },
        )
      } else {
        params = points
        if (params.length === 0) {
          state.pointsInRect = JSON.parse(JSON.stringify(state.pointsInRect))
          state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          return
        }
        listByIds(params.map(ele => ({ objectId: ele.properties.object_id, objectType: ele.properties.object_type }))).then(
          res => {
            state.pointsInRect = (res.data || []).map((ele, i) => ({
              ...params?.[i],
              properties: { ...params?.[i].properties, ...ele },
            }))

            state.expiredValue = dayjs().add(state.mapSiteIndexExpired, 'minute').valueOf()
          },
        )
      }
    }, 300)
  }

  // 在组件卸载时清理资源
  onUnmounted(() => {
    if (state.mapIns) {
      const { cleanup } = useOverlayManager()
      cleanup(state.mapIns)
    }
  })
</script>

<style lang="scss" scoped></style>
