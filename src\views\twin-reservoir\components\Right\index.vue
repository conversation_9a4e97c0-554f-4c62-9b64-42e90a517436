<template>
  <div
    class="h-1.94rem fixed right-0.26rem top-4.57rem p-0.16rem z-888 water-level-simulation"
    v-if="props.simulationType == 'gate-simulation'"
  >
    <h5 class="w-100% mt-0.08rem text-0.18rem font-[KwangLiangWine] c-#fff font-400 flex">
      <i class="w-0.04rem h-0.17rem bg-[#09ECFF] inline-block mr-0.13rem"></i>
      <span>闸门模拟</span>
    </h5>
    <div class="flex mt-0.08rem">
      <n-checkbox class="mt-0.05rem" v-model:checked="state.checkedSluiceGate1">闸门 #1</n-checkbox>
      <n-input-number class="w-1.2rem h-0.34rem s-input" max="90" min="-90" v-model:value="state.sluiceGate1" clearable />
      <span class="c-#9ad7ef ml-0.1rem mt-0.1rem">%</span>
    </div>
    <div class="flex mt-0.09rem">
      <n-checkbox class="mt-0.05rem" v-model:checked="state.checkedSluiceGate2">闸门 #2</n-checkbox>
      <n-input-number class="w-1.2rem h-0.34rem s-input" max="90" min="-90" v-model:value="state.sluiceGate2" clearable />
      <span class="c-#9ad7ef ml-0.1rem mt-0.1rem">%</span>
    </div>
    <div class="btn-group text-right mt-0.18rem">
      <n-button class="w-0.76rem h-0.32rem border-rd-0.04rem reset-btn text-0.14rem" @click="resetGate">重置</n-button>
      <n-button class="w-0.76rem h-0.32rem border-rd-0.04rem ml-0.08rem confirm-btn text-0.14rem" @click="confirmGate">
        确定
      </n-button>
    </div>
  </div>

  <div
    class="fixed right-0.26rem top-4.52rem z-888 water-level-simulation"
    :class="{ 'w-2.58rem,h-1.94rem': state.type === '1', 'w-3.47rem': state.type === '2' }"
    v-if="props.simulationType === 'water-level-simulation'"
  >
    <h5 class="w-100% mt-0.06rem text-0.18rem font-[KwangLiangWine] c-#fff font-400 flex">
      <i class="w-0.04rem h-0.17rem bg-[#09ECFF] inline-block mr-0.13rem"></i>
      <span>水位模拟</span>
    </h5>
    <n-radio-group class="mb-0.1rem mt-0.08rem" v-model:value="state.type" name="radiogroup" @update:value="handleTypeUpdate">
      <n-space>
        <n-radio v-for="song in waterType" :key="song.value" :value="song.value">
          {{ song.label }}
        </n-radio>
      </n-space>
    </n-radio-group>
    <div v-if="state.type === '1'">
      <div class="flex lh-0.32rem">
        <label class="c-#fff font-350 mr-0.09rem">水库水位</label>
        <n-input-number class="w-1.9rem h-0.34rem s-input" min="115" v-model:value="state.waterLevel" clearable />
        <span class="c-#699DB2 ml-0.1rem">m</span>
      </div>
      <div class="btn-group text-right mt-0.18rem">
        <n-button class="w-0.76rem h-0.32rem border-rd-0.04rem reset-btn text-0.14rem" @click="resetRiver">重置</n-button>
        <n-button class="w-0.76rem h-0.32rem border-rd-0.04rem ml-0.08rem confirm-btn text-0.14rem" @click="confirmRiver">
          确定
        </n-button>
      </div>
    </div>

    <div class="relative min-h-4.86rem" v-if="state.type === '2'">
      <h6 class="text-0.14rem c-#B2DAEA font-350 mb-0.1rem">水库模拟方案:</h6>
      <div
        class="card p-0.07rem mb-0.09rem"
        v-for="item in state.simulationPlan"
        :key="item.caseCode"
        :class="{ 'height-auto': item.showMore }"
      >
        <div class="flex lh-0.21rem cursor-pointer">
          <label class="w-2.2rem text-0.16rem font-500 c-#fff name">{{ item.caseName }}</label>
          <span class="text-0.12rem c-#09FCC7 mr-0.08rem ml-auto" @click="showSchedulingSimulation(item.resvrDispId)">模拟</span>
          <i class="w-0.16rem h-0.16rem inline-block uno-bg_twin-reservoir/arrow-down.png" @click="togglePlan(item)"></i>
        </div>
        <div class="small-card flex p-0.08rem mt-0.1rem">
          <hgroup class="flex-1">
            <h6 class="text-0.14rem font-350 c-#B2DAEA">起调水位</h6>
            <h3 class="text-0.2rem font-500 c-#fff">
              {{ item.startWaterLevel == null ? '--' : item.startWaterLevel }}
              <span v-if="item.startWaterLevel != null" class="unit c-#699DB2">m</span>
            </h3>
          </hgroup>
          <hgroup class="flex-1">
            <h6 class="text-0.14rem font-350 c-#B2DAEA">未期水位</h6>
            <h3 class="text-0.2rem font-500 c-#fff">
              {{ item.endWaterLevel == null ? '--' : item.endWaterLevel }}
              <span v-if="item.endWaterLevel != null" class="unit c-#699DB2">m</span>
            </h3>
          </hgroup>
          <hgroup class="flex-1">
            <h6 class="text-0.14rem font-350 c-#B2DAEA">预计出库水量</h6>
            <h3 class="text-0.2rem font-500 c-#fff">
              {{ item.sumOutWater == null ? '--' : item.sumOutWater }}
              <span v-if="item.sumOutWater != null" class="unit c-#699DB2">万m³</span>
            </h3>
          </hgroup>
        </div>
      </div>
      <div class="w-100% block mt-0.1rem absolute bottom-0.1rem flex">
        <n-pagination
          class="ml-auto"
          size="small"
          v-model:page="state.pageNum"
          :default-page-size="state.pageSize"
          :item-count="state.total"
          @update:page="updatePageNum"
        />
      </div>
    </div>
  </div>
  <SimulationResults
    v-show="state.showSimulation && props.simulationType == 'water-level-simulation'"
    v-bind="state.simulationDetails"
    @close-simulation="closeSimulation"
    @play-simulation="playSimulation"
  />
</template>
<script setup lang="ts" name="ReservoirRight">
  import { getSchedulingSimulation, getSimulationResults } from '../../services'
  import SimulationResults from './SimulationResults.vue'
  const waterType = [
    {
      value: '1',
      label: '手动模拟',
    },
    {
      value: '2',
      label: '调度模拟',
    },
  ]

  const props = defineProps({
    simulationType: undefined,
    waterLevel: undefined,
  })

  const state = reactive({
    isSluiceGate: true,
    type: '1',
    waterLevel: props?.waterLevel,
    checkedSluiceGate1: false,
    checkedSluiceGate2: false,
    sluiceGate1: 0,
    sluiceGate2: 0,

    pageNum: 1,
    pageSize: 7,
    simulationPlan: [],
    total: 0,
    simulationDetails: {},
    showSimulation: false,
  })

  onMounted(() => {
    getList()
  })

  const getList = () => {
    getSchedulingSimulation({ pageNum: state.pageNum, pageSize: state.pageSize }).then(res => {
      state.simulationPlan = res?.data?.data.map(el => ({
        ...el,
        showMore: false,
      }))
      state.total = res?.data?.total
    })
  }

  const updatePageNum = (val: number) => {
    state.pageNum = val
    getList()
  }

  const togglePlan = row => {
    state.simulationPlan.forEach(el => {
      if (el != row) {
        el.showMore = false
      }
    })
    row.showMore = !row.showMore
  }

  const showSchedulingSimulation = (id: number) => {
    getSimulationResults({ resvrDispId: id }).then(res => {
      state.simulationDetails = res?.data
      state.showSimulation = true
    })
  }

  const handleTypeUpdate = e => {
    if (e == '1') {
      state.showSimulation = false
    }
  }

  const emits = defineEmits(['change-river', 'change-gate', 'simulation-river'])
  const resetRiver = () => {
    state.waterLevel = props?.waterLevel
    emits('change-river', props?.waterLevel, 'init')
  }
  const confirmRiver = () => {
    emits('change-river', state.waterLevel)
  }

  const resetGate = () => {
    if (state.checkedSluiceGate1) state.sluiceGate1 = 0
    if (state.checkedSluiceGate2) state.sluiceGate2 = 0
    emits('change-gate', state.sluiceGate1, state.sluiceGate2, 'init', state.checkedSluiceGate1, state.checkedSluiceGate2)
  }
  const confirmGate = () => {
    emits('change-gate', state.sluiceGate1, state.sluiceGate2, '', state.checkedSluiceGate1, state.checkedSluiceGate2)
  }

  const closeSimulation = () => {
    state.showSimulation = false
    emits('simulation-river', 0, 'init')
  }
  const playSimulation = waterLevel => {
    emits('simulation-river', waterLevel)
  }
</script>
<style lang="scss" scoped>
  .water-level-simulation {
    padding: 0.1rem 0.14rem 0.14rem 0.14rem;
    border-radius: 0.08rem;
    background: linear-gradient(180deg, rgba(6, 55, 50, 0) 27%, rgba(12, 164, 149, 0.52) 98%),
      linear-gradient(180deg, rgba(2, 127, 166, 0.6) -5%, rgba(4, 41, 92, 0.6) 98%);
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: -1;
      border-radius: 0.08rem;
      -webkit-backdrop-filter: blur(10px);
      backdrop-filter: blur(10px);
    }
    :deep(.n-radio__label) {
      color: #fff;
    }
    :deep(.n-radio.n-radio--checked .n-radio__dot) {
      background-color: #09c8fc;
    }
  }

  .btn-group {
    font-size: 0.14rem;
    .reset-btn {
      background: linear-gradient(180deg, #0c2029 0%, #1f536a 100%);
      box-sizing: border-box;
      border: 1px solid;
      border-image: linear-gradient(180deg, rgba(255, 255, 255, 0.48) 0%, rgba(255, 255, 255, 0.24) 97%) 1;
    }
    .confirm-btn {
      background: radial-gradient(74% 74% at 44% 100%, #00c8ff 0%, rgba(9, 200, 252, 0) 99%),
        linear-gradient(180deg, #0c2029 0%, #1f536a 100%), rgba(146, 183, 202, 0.302);
      box-sizing: border-box;
      border: 2px solid;
      border-image: linear-gradient(180deg, #09c8fc 0%, #09c8fc 100%) 2;
    }
    :deep(.n-button:not(.n-button--disabled):active) {
      color: #fff;
    }
  }
  :deep(.n-checkbox .n-checkbox-box .n-checkbox-box__border) {
    border: 1.5px solid #699db2;
  }
  :deep(
    .n-checkbox.n-checkbox--checked:focus:not(:active) .n-checkbox-box .n-checkbox-box__border,
    .n-checkbox.n-checkbox--indeterminate:focus:not(:active) .n-checkbox-box .n-checkbox-box__border
  ) {
    border-color: #09c8fc;
  }
  :deep(.n-checkbox.n-checkbox--checked .n-checkbox-box, .n-checkbox.n-checkbox--indeterminate .n-checkbox-box) {
    background-color: #09c8fc;
  }
  :deep(.n-checkbox .n-checkbox-box .n-checkbox-icon .check-icon, .n-checkbox .n-checkbox-box .n-checkbox-icon .line-icon) {
    fill: #fff;
  }
  .s-input {
    background: rgba(105, 157, 178, 0.3);
    border: 1px solid rgba(105, 157, 178, 0.5);
    :deep(.n-input .n-input__input-el) {
      height: 0.34rem;
    }
  }

  .card {
    width: 3.15rem;
    height: 0.39rem;
    overflow: hidden;
    border-radius: 0.04rem;
    background: rgba(9, 200, 252, 0.3);
    border: 1px solid rgba(105, 157, 178, 0.5);
    .name {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .small-card {
      width: 2.99rem;
      height: 0.68rem;
      border-radius: 0.04rem;
      background: rgba(21, 140, 160, 0.6);
      border: 1px solid #158ca0;
      .unit {
        font-size: 0.14rem;
      }
    }
    &.height-auto {
      height: auto !important;
      .name {
        overflow: auto !important;
        text-overflow: initial !important;
        white-space: normal !important;
      }
    }
  }
  :deep(.n-pagination .n-pagination-item:not(.n-pagination-item--disabled).n-pagination-item--active) {
    width: 24px;
    height: 24px;
    border-radius: 2px;
    background: #699db2;
    color: #09fcc7;
    border: none;
  }
</style>
