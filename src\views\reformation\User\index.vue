<template>
    <MyCard name="灌区用水户">
        <!-- mb-0.2rem -->
        <div class="content pt-0.1rem h-2.2rem">
            <div class="item flex h-2.1rem mt-0rem">
                <!------------------- 左侧图标 ------------------>
                <div class="chart relative">
                    <div class="absolute w-2.2rem h-2.2rem top--0.05rem left-0.05rem"
                        :style="{ background: `url(${getImageUrl('intelligent/info-icon.png')}) no-repeat center / 100% 100%` }">
                        <MultiPieChart :dataSource="state.multiPieChartData"
                            style="margin-top: 0.18rem; margin-left: 0.14rem" />
                    </div>

                    <div
                        class="absolute w-1rem h-1rem top-0.7rem left-0.66rem text-0.1rem text-[#fff] flex flex-col items-center">
                        <div class="text-0.32rem mb-0.16rem font-400">
                            {{ state.total }}
                            <span class="text-0.14rem font-350">户</span>
                        </div>
                        <div class="w-0.8rem text-0.14rem text-[#B2DAEA] font-500 text-center">用水户分布</div>
                    </div>
                </div>
                <!------------------- 右侧图标 ------------------>
                <div class="w-1.6rem h-1.8rem ml-2.3rem mt-0.15rem flex flex-col relative">
                    <vue3-seamless-scroll :list="state.multiList" class="data-scroll" hover wheel :key="1" :step="1"
                        v-if="state.multiList?.length" :limitScrollNum="5" >
                        <div class="chart-item flex h-0.3rem items-center mt-0.1rem mb-0.05rem mr-0.1rem"
                            v-for="(item, index) in state.multiList" :key="index">
                            <div class="w-0.1rem h-0.1rem mr-0.1rem ml-0.1rem border border-solid border-[#FFF] rounded-[1px] "
                                :style="{ background: projectColor[index] }">
                            </div>
                            <div class="text-0.14rem text-[#B2DAEA] mr-0.2rem">{{ item.name }}</div>
                            <div class="text-[#fff] absolute font-400 flex right-0.14rem">
                                <span class="text-0.15rem  lh-normal">{{ item.value }}</span>
                                <span class="text-0.14rem text-[#699DB2] mt-0.04rem ml-0.03rem">户</span>
                            </div>
                        </div>
                    </vue3-seamless-scroll>
                </div>
            </div>
        </div>
    </MyCard>
</template>
<script setup lang="tsx" name="DeviceInfo">
import { getDistrictUserWater } from '../services'
import { thjDrainageStatuses, serviceColor, projectColor } from '@/constants'
import MultiPieChart from './MultiPieChart.vue'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
import { dealNumber } from '@/utils'

const state = reactive({
    info: {},
    multiPieChartData: [], // 饼图数据
    total: 0,
    multiList: [], // 项目列表数据
})

onMounted(() => {
    getList()
})
const getList = () => {
    // 用水户
    getDistrictUserWater().then(res => {
        state.multiList = res?.data ? res?.data.map((item, index) => ({
            name: item.districtName,
            value: Number(item.count),
            itemStyle: { color: projectColor[index] }
        })).filter(item => item.value > 0) : []

        state.total = state.multiList.reduce((sum, item) => sum + item.value, 0)
        state.multiPieChartData = state.multiList.map((item, index) => ({
            name: item.name,
            value: item.value,
            itemStyle: { color: projectColor[index] },
        }))
    })
}



</script>
<style lang="scss" scoped>
.content {
    opacity: 1;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
}

:deep(.slick-slide) {
    height: 1.6rem;
    overflow: hidden;
    color: #ffffff;
}

.chart-item {
    background: rgba(31, 115, 121, 0.3);
    border: 1px solid;
    border-image: radial-gradient(4% 33% at 3% 100%, #00d4ff 0%, rgba(90, 228, 255, 0) 100%) 1;
}

.data-scroll {
    height: 100%;
    // background: red;
    overflow: hidden;

    .scroll-item {
        // margin-right: 0.2rem;
        width: 100%;
        height: 0.7rem;
        // background: url('@/assets/images/intelligent/chart.png') no-repeat;
        background-size: 100% 100%;
    }
}
</style>