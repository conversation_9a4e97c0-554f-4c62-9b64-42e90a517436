// 水库放水统计
export function getDrawCount(params) {
  return request({
    url: '/view/resource/getDrawCount',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 降雨预报
export function futureRainConvert(params) {
  return request({
    url: '/model/futureRain/convert',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//降雨监测
export function futureRainList(params) {
  return request({
    url: '/model/futureRain/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//旱情预警列表
export function getModelDroughtPage(data) {
  return request({
    url: '/model/drought/page',
    method: 'post',
    data,
  })
}
//旱情预警详情
export function getModelDrought(data) {
  return request({
    url: '/model/drought/get',
    method: 'post',
    data,
  })
}

//来水预报模拟自动预报查询
export function queryAutoForecast(data) {
  return request({
    url: '/model/in-water/auto-forecast/query',
    method: 'post',
    data,
  })
}

//渠系仿真列表
export function getChSimPage(data) {
  return request({
    url: '/model/ch-sim/page',
    method: 'post',
    data,
  })
}
// 推演结果
export function getInferRes(params) {
  return request({
    url: '/model/ch-sim/getInferRes',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水库调度概化图
export function getResourceConsumptionList(params) {
  return request({
    url: '/view/resource/getConsumptionList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水利对象分类-根据分类统计对象
export function objectCategoryCountByFirstLevel(data) {
  return request({
    url: '/base/objectCategory/countByFirstLevel',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}
// 水利对象分类-一级分类
export function objectCategoryFirstLevelList() {
  return request({
    url: '/base/objectCategory/firstLevel/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

//工程运行状态
export function getProjectDeviceList(params) {
  return request({
    url: '/view/intelligent/getProjectDeviceList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

//视频列表
export function getAllCameraList() {
  return request({
    url: '/base/camera/getAllCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 行政区划树
export function districtGetTree() {
  return request({
    url: '/base/district/getTree',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  })
}

// 水利对象分类-列表分页查询
export function objectCategoryObjectPage(data) {
  return request({
    url: '/base/objectCategory/object/page',
    method: 'post',
    data,
  })
}

//视频监控-根据水利对象查询视频点信息
export function getVideoById(data: object) {
  return request({
    url: '/base/camera/object/getCameraList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}

// 水利对象分类-根据对象统计监控指标
export function listByIds(data) {
  return request({
    url: '/base/objectCategory/object/listByIds',
    method: 'post',
    data,
  })
}

// 获取视频播放地址
export function getVideoAddress(params) {
  return request({
    url: '/external/easyCvr/getPlayOnUrl',
    method: 'get',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 水库调度模拟-列表
export function getDispatchList(data) {
  return request({
    url: '/model/resvr-disp/page',
    method: 'post',
    data,
  })
}

// 水库调度模拟-详情
export function getDispatchDetails(params) {
  return request({
    url: '/model/resvr-disp/get',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}
//
export function getInWaterPage(data) {
  return request({
    url: '/model/in-water/page',
    method: 'post',
    data,
  })
}

// 工程曲线图
export function getChSimResList(data) {
  return request({
    url: '/model/ch-sim/getChSimResList',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}

// 配置-列表
export function getChSimRange(data) {
  return request({
    url: '/model/ch-sim/range/list',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data,
  })
}

// 推演概化图-部分水利设施仿真结果列表
export function getScaleResDetails(params) {
  return request({
    url: '/model/ch-sim/getScaleResDetails',
    method: 'post',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    params,
  })
}

// 查询降雨等值面
export function getContourSurface(data) {
  return request({
    url: '/war/rainfall/contourSurface/list',
    method: 'post',
    data,
  })
}