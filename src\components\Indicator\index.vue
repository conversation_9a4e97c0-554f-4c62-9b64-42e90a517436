<template>
  <div class="flex items-center">
    <div
      class="mr-0.08rem"
      :style="{
        background: `url(${attrs.img}) no-repeat center / 100% 100%`,
        width: attrs.size || '0.42rem',
        height: attrs.size || '0.42rem',
      }"
    ></div>
    <div>
      <div>
        <span class="text-0.28rem font-bold font-[DINAlternate]">{{ attrs.value }}</span>
        <span class="ml-0.04rem c-text_gray">{{ attrs.unit }}</span>
      </div>
      <div class="c-text_md mt-0.04rem">{{ attrs.label }}</div>
    </div>
  </div>
</template>
<script setup lang="tsx" name="Indicator">
  const attrs = useAttrs()
</script>
<style lang="scss" scoped></style>
