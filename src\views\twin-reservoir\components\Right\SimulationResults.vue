<template>
  <div class="w-4.3rem h-2.94rem fixed right-4.66rem top-4.36rem p-0.16rem z-888">
    <h3 class="w-4.3rem h-0.42rem lh-0.48rem flex uno-bg_/card-title-bg.png">
      <label class="pl-0.36rem text-0.2rem font-500 c-#fff text-overflow1">调度模拟结果</label>
      <span class="text-0.18rem c-#fff font-500 ml-auto mr-0.1rem cursor-pointer" @click="close">×</span>
    </h3>
    <div class="chart-box">
      <DispatchLine v-show="attrs?.resvrDispResList?.length" :dataSource="attrs?.resvrDispResList" />
      <div class="w-4.3rem h-0.3rem flex mt-1.98rem pl-0.12rem pt-0.03rem" v-if="attrs?.resvrDispResList?.length">
        <div class="w-0.22rem h-0.22rem border-rd-5.87rem bg-[#09C8FC] mr-0.08rem cursor-pointer" @click="onClickPlay">
          <i class="suspend" v-if="state.isPlay"></i>
          <i class="play-icon" v-else></i>
        </div>
        <div
          class="w-3.66rem h-0.13rem p-0.02rem mt-0.05rem border-rd-5.87rem bg-[rgba(105, 157, 178, 0.5)] b b-[#699DB2] b-solid"
        >
          <n-progress
            type="line"
            :percentage="state.percentage"
            :show-indicator="false"
            :height="5"
            color="#F6CC57"
            rail-color="rgba(105, 157, 178, 0.5)"
            processing
          />
        </div>
      </div>
      <n-empty
        v-if="attrs?.resvrDispResList == null || attrs?.resvrDispResList?.length == 0"
        class="pt-0.44rem"
        description="暂无数据"
      />
    </div>
  </div>
</template>
<script setup lang="ts" name="SimulationResults">
  import DispatchLine from './DispatchLine.vue'

  const attrs = useAttrs()

  const state = reactive({
    percentage: 0,
    totalIndex: 0,
    timer: null,
    isPlay: false, //根据后面返回的时间播放，播放到哪个时间，显示哪个时间对应的值，水位也跟着上涨下降
  })

  watch(
    () => attrs?.resvrDispResList,
    newVal => {
      init()
    },
  )

  onMounted(() => {})

  const init = () => {
    state.timer = null
    clearInterval(state.timer)
    state.isPlay = false
    state.percentage = 0
    state.totalIndex = 0
  }

  const emits = defineEmits(['close-simulation', 'play-simulation'])
  const close = () => {
    init()
    emits('close-simulation')
  }

  const onClickPlay = () => {
    state.isPlay = !state.isPlay
    const count = attrs?.resvrDispResList?.length
    state.timer = setInterval(() => {
      if (state.isPlay && count - 1 > state.totalIndex) {
        state.percentage += 100 / (count - 1)
        state.totalIndex++
        emits('play-simulation', attrs?.resvrDispResList[state.totalIndex]?.wlv)
      } else {
        state.isPlay = false
        clearInterval(state.timer)
      }
    }, 1500)
  }
</script>
<style lang="scss" scoped>
  .chart-box {
    width: 4.3rem;
    height: 2.3rem;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    border: 1px solid rgba(54, 96, 103, 0.5);
  }
  .play-icon {
    display: block;
    margin: 0.05rem 0 0 0.08rem;
    border-width: 0.07rem 0 0.07rem 0.09rem;
    border-style: solid;
    border-color: transparent transparent transparent #fff;
  }
  .suspend {
    width: 0.03rem;
    height: 0.1rem;
    display: block;
    margin: 0.06rem 0 0 0.06rem;
    background: #fff;
    position: relative;
    &::after {
      content: '';
      width: 0.04rem;
      height: 0.1rem;
      position: absolute;
      left: 0.06rem;
      background: #fff;
    }
  }
</style>
