import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import ReactivityTransform from '@vue-macros/reactivity-transform/vite'
// import path from 'path'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { NaiveUiResolver } from 'unplugin-vue-components/resolvers'
import ViteCompression from 'vite-plugin-compression'
import vueSetupExtend from 'vite-plugin-vue-setup-extend'
// import { lazyImport, VxeResolver } from 'vite-plugin-lazy-import'
import Unocss from 'unocss/vite'

import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'
import { pluginIcons } from './build/plugins/icons'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'

import cesium from 'vite-plugin-cesium'

// https://vitejs.dev/config/
export default ({ mode }) => {
  const isProd = mode === 'prod'

  return defineConfig({
    plugins: [
      vue({ script: { propsDestructure: true } }),
      vueJsx({ enableObjectSlots: true }),
      cesium(),
      // ref vs. 响应式变量  去掉.value写法
      ReactivityTransform(),
      Unocss(),
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          'pinia',
          {
            '@vueuse/core': ['useDebounceFn', 'useEventListener', 'useFullscreen', 'useMediaQuery', 'useTitle', 'useToggle'],
          },
          {
            from: 'naive-ui',
            imports: [
              'DataTableBaseColumn',
              'DataTableColumn',
              'DataTableColumns',
              'DataTableCreateSummary',
              'DropdownOption',
              'FormInst',
              'FormItemInst',
              'FormItemRule',
              'FormRules',
              'FormValidationError',
              'MenuInst',
              'MenuOption',
              'UploadCustomRequestOptions',
              'UploadFileInfo',
              'UploadInst',
            ],
            type: true,
          },
        ],
        include: [
          /\.[tj]sx?$/, // .ts, .tsx, .js, .jsx
          /\.vue$/,
          /\.vue\?vue/, // .vue
        ],
        dirs: [
          'src/api/index**',
          'src/components/**',
          'src/components/custom-naive/**/**',
          'src/hooks/**',
          'src/store/**',
          'src/store/**/**',
          'src/utils/**',
        ],
        dts: '@types/auto-imports.d.ts',
        vueTemplate: true, // 支持在 Vue 模版中使用
      }),

      Components({
        dts: '@types/components.d.ts',
        resolvers: [NaiveUiResolver(), IconsResolver()],
        types: [
          {
            from: 'vue-router',
            names: ['RouterLink', 'RouterView'],
          },
        ],
        directives: true, // 自动导入指令
        globs: [
          'src/components/common/*',
          'src/components/*/index.*',
          'src/components/custom-naive/*/index.*',
          'src/layouts/*/index.vue',
        ],
      }),

      Icons(),
      // 自定义插件，用于生成自定义icon，并添加到虚拟模块
      pluginIcons(),
      // svg icon
      createSvgIconsPlugin({
        // 指定图标文件夹
        iconDirs: [
          fileURLToPath(new URL('./src/assets/svgs/rightLevel2', import.meta.url)),
          fileURLToPath(new URL('./src/assets/svgs', import.meta.url)),
        ],
        // 指定 symbolId 格式
        symbolId: 'icon-[dir]-[name]',
      }),

      // 允许 setup 语法糖上添加组件名属性
      vueSetupExtend(),
      // gzip压缩
      ViteCompression({
        filter: /\.(js|mjs|json|css|html|ttf|otf|svg)$/i,
        algorithm: 'gzip',
        threshold: 1024 * 5, // 5k以上压缩,
      }),
    ],

    // 环境变量
    define: {
      'import.meta.env.VITE_BASE_API': `"${process.env.npm_config_base_api || loadEnv(mode, process.cwd()).VITE_BASE_API}"`,
      'import.meta.env.VITE_CAS_URL': `"${process.env.npm_config_cas_url || loadEnv(mode, process.cwd()).VITE_CAS_URL}"`,
      'import.meta.env.VITE_TITLE': `"${process.env.npm_config_title || loadEnv(mode, process.cwd()).VITE_TITLE}"`,
      'import.meta.env.VITE_FAVICON': `"${process.env.npm_config_favicon || loadEnv(mode, process.cwd()).VITE_FAVICON}"`,

      'import.meta.env.VITE_MODEL_URL': `"${process.env.npm_config_model_url || loadEnv(mode, process.cwd()).VITE_MODEL_URL}"`,

      'import.meta.env.VITE_TIANDI_BASE': `"${process.env.npm_config_tiandi_base || loadEnv(mode, process.cwd()).VITE_TIANDI_BASE}"`,
      'import.meta.env.VITE_TIANDI_TK': `"${process.env.npm_config_tiandi_tk || loadEnv(mode, process.cwd()).VITE_TIANDI_TK}"`,

      'import.meta.env.VITE_GEOSERVER_BASE': `"${process.env.npm_config_geoserver_base || loadEnv(mode, process.cwd()).VITE_GEOSERVER_BASE}"`,
      'import.meta.env.VITE_GEOSERVER_URL': `"${process.env.npm_config_geoserver_url || loadEnv(mode, process.cwd()).VITE_GEOSERVER_URL}"`,
      'import.meta.env.VITE_GEOSERVER_DISTRICT_URL': `"${process.env.npm_config_geoserver_district_url || loadEnv(mode, process.cwd()).VITE_GEOSERVER_DISTRICT_URL}"`,
    },

    //这里进行配置别名
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)), // @代替src
        '~': fileURLToPath(new URL(import.meta.url)),
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue'],
    },
    optimizeDeps: {
      include: ['@iconify/json', 'echarts'],
    },
    css: {
      preprocessorOptions: {
        scss: {
          api: 'modern-compiler', // or 'modern'
          additionalData: `@use "@/styles/variables.scss" as *;`,
          javascriptEnabled: true,
        },
      },
    },
    build: {
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: isProd, // 是否过滤掉所有consol.log
          drop_debugger: isProd,
        },
      },
      reportCompressedSize: false,
      chunkSizeWarningLimit: 1500,
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            axios: ['axios'],
            'lodash-es': ['lodash-es'],
            'naive-ui': ['naive-ui'],
            vue: ['vue', 'vue-router', 'pinia', '@vueuse/core'],
          },
        },
      },
    },
    base: '/',
    server: {
      host: '0.0.0.0',
      port: 8080,
      open: true,
      // proxy: {
      //   '/api': {
      //     target: loadEnv(mode, process.cwd()).VITE_BASE_API,
      //     changeOrigin: true,
      //     rewrite: (path: string) => path.replace(/^\/api/, ''),
      //     configure: (proxy, options: any) => {
      //       // 配置此项可在响应头中看到请求的真实地址
      //       proxy.on('proxyRes', (proxyRes, req) => {
      //         proxyRes.headers['x-real-url'] = new URL(req.url || '', options.target)?.href || ''
      //       })
      //     },
      //   },
      // },
    },
  })
}
