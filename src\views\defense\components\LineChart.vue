<template>
  <ScaleBox :style="{ width: '560px', height: '250px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="<PERSON><PERSON><PERSON>">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()

  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )
  const hexToRgba = (hex, opacity) => {
    let rgbaColor = ''
    let reg = /^#[\da-f]{6}$/i
    if (reg.test(hex)) {
      rgbaColor = `rgba(${parseInt('0x' + hex.slice(1, 3))},${parseInt(
        '0x' + hex.slice(3, 5),
      )},${parseInt('0x' + hex.slice(5, 7))},${opacity})`
    }
    return rgbaColor
  }
  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  // let colors = ['rgba(108,230,103,1)', 'rgba(30,89,115,1)', 'rgba(9, 200, 252, 0.8)']
  function getConfig(data) {
    // console.log('*** 11 info chart data', data)
    let type = data[0].type
    let xData = data[0].xData // [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    let seriesData = data[0].data //[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    let bgColor = '#fff'
    let colors = ['#0090FF', '#36CE9E', '#FFC005', '#FF515A', '#8B5CFF', '#00CA69']
    return {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '-') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = colors[0] //图例颜色
              console.log('*** 11 info chart color', param)
              function getUnit(seriesName) {
                switch (seriesName) {
                  case '降雨量':
                    return 'mm'
                  case '累计雨量':
                    return 'mm'
                  case '水位':
                    return 'm'
                  case '上游水位':
                    return 'm'
                  case '下游水位':
                    return 'm'
                  case '预报来水流量':
                    return 'm³/s'
                  case '流量':
                    return 'm³/s'
                  default:
                    return
                }
              }
              htmlStr += `
                    <div style="border: 1px solid rgba(152,224,255,0.3) border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
        // formatter: params => {
        //   let dataStr = `
        //     <div style="line-height:1.2">
        //       <span style="font-size:0.12rem;color:var(--text-md);">${params[0].axisValue}</span>
        //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[0].seriesName}:<span style="color:var(--primary-color)">${params[0].value[1]}台时</span></div>
        //     </div>
        //   `
        //   return dataStr
        // },
      },
      grid: {
        // left: 20,
        left: 40,
        right: 15,
        top: 30,
        bottom: 5,
        containLabel: true,
      },
      legend: {
        show: true,
        textStyle: {
          color: 'rgba(222, 242, 252, 1)',
        },
        itemWidth: 12,
        itemHeight: 10,
      },
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        axisLabel: {
          color: 'rgba(222, 242, 252, 1)',
        },
        data: xData,
      },
      yAxis: {
        type: 'value',
        name: '单位:mm',
        // nameGap: 12,
        minInterval: 1, //自动计算坐标轴最小间隔，例：设置成1，刻度没有小数
        // maxInterval: '', //自动计算坐标轴最大间隔
        nameTextStyle: {
          color: 'rgba(222, 242, 252, 1)',
          padding: [0, 15, 0, 0],
        },
        axisLine: {
          show: false,
        },
        axisLabel: {
          color: 'rgba(105, 157, 178, 1)',
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        // max: 3,
        min: 0,
        max: function (value) {
          let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

          return val
        },
      },
      series: {
        type: 'bar',
        barMaxWidth: 8,
        name: '降雨量',
        smooth: true,
        // showSymbol: false,/
        symbolSize: 8,
        zlevel: 3,
        color: colors[0],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            {
              offset: 0,
              color: hexToRgba(colors[0], 0.3),
            },
            {
              offset: 0.2,
              color: hexToRgba(colors[0], 0.5),
            },
            {
              offset: 1,
              color: hexToRgba(colors[0], 0.9),
            },
          ]),
        },
        areaStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: hexToRgba(colors[0], 0.3),
                },
                {
                  offset: 1,
                  color: hexToRgba(colors[0], 0.1),
                },
              ],
              false,
            ),
            shadowColor: hexToRgba(colors[0], 0.1),
            shadowBlur: 10,
          },
        },
        data: seriesData,
        // itemStyle: {
        //   color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        //     {
        //       offset: 0,
        //       color: 'rgba(108, 230, 103, 1)',
        //     },
        //     {
        //       offset: 1,
        //       color: 'rgba(108, 230, 103, 0)',
        //     },
        //   ]),
        // },
      },
    }
  }
</script>

<style lang="scss" scoped></style>
