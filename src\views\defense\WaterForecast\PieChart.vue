<template>
  <ScaleBox :style="{ width: '150px', height: '150px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="<PERSON><PERSON><PERSON>">
  import * as echarts from 'echarts/core'

  const attrs = useAttrs()

  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    const colors = ['#1E345A']
    return {
      backgroundColor: 'rgba(202, 227, 254, 0)',
      tooltip: {
        show: false,
        trigger: 'item',
        // backgroundColor: 'rgba(0,0,0,0.6)', //通过设置rgba调节背景颜色与透明度
        // textStyle: { fontSize: 12, color: '#fff' },
        // borderWidth: 0,
        // axisPointer: {
        //   type: 'cross',
        //   label: {
        //     backgroundColor: '#6a7985',
        //   },
        // },
        // formatter: '{b}: {c}个',
        // },
        // legend: {
        //   top: '5%',
        //   left: 'center',
      },
      series: [
        {
          name: '设备占比',
          type: 'pie',
          radius: ['70%', '82%'],
          showEmptyCircle: true,
          avoidLabelOverlap: false,
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
            },
          },
          labelLine: {
            show: false,
          },
          itemStyle: {
            emphasis: {
              scaleSize: 1, // 鼠标移入时不放大
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
            // borderColor: '#C6E7FF', // 设置环形边框颜色
            // borderWidth: 2, // 设置边框宽度
            color: 'rgba(255, 255, 255, 0.1)', //'rgba(255, 255, 255, 0.3)' // 设置环状背景色，这里设置为半透明的白色
          },
          data: data,
          // data: [{ value: 50 }, { value: 50, name: 'Direct' }],
        },
      ],
    }
  }
</script>

<style lang="scss" scoped></style>
