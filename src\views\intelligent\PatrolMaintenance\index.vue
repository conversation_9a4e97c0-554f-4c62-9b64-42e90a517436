<template>
  <MyCard name="工程巡检及养护">
    <template #headerRight>
      <!-- style="position: absolute; width: 0.98rem; right: 0.1rem; top: 0.08rem; display: flex" -->
      <NaSelect
        class="absolute w-0.98rem flex right-0.1rem top-0.1rem"
        v-model:value="year"
        type="side"
        :show-checkmark="false"
        :options="state.yearList"
      >
        <template #arrow>
          <MyIcon
            class="i-iconamoon:arrow-down-2-fill group-hover:rotate-180 transition-(all duration-0.5s) text-0.24rem c-[#fff] mt--0.08rem ml--0.12rem"
          />
        </template>
      </NaSelect>
    </template>
    <div class="content pt-0.2rem h-9.2rem">
      <!-- 工程巡检及养护 -->
      <div class="item flex flex-col items-center h-0.6rem mb-0.20rem relative">
        <div class="text-0.14rem justify-between w-[98%] h-0.6rem p-0.06rem ml-0.2rem flex">
          <div class="flex flex-1 mr-0.1rem my-0.1rem">
            <div
              class="h-0.4rem w-0.4rem"
              :style="{ background: `url(${getImageUrl('intelligent/patrol.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.6rem">
              <div class="text-[#B2DAEA] mb-0.1rem">日常巡检</div>
              <div class="ml-0.1rem" :style="{ color: legendColors[0] }">
                {{ state.info?.patrolCount }}
                <span class="text-[#B2DAEA] ml-0.05rem">次</span>
              </div>
            </div>
          </div>
          <div class="flex flex-1 mr-0.1rem my-0.1rem">
            <div
              class="h-0.4rem w-0.4rem"
              :style="{ background: `url(${getImageUrl('intelligent/maintenance.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.66rem">
              <div class="text-[#B2DAEA] mb-0.1rem">维修养护</div>
              <div class="ml-0.1rem" :style="{ color: legendColors[1] }">
                {{ state.info?.conserveCount }}
                <span class="text-[#B2DAEA] ml-0.05rem">次</span>
              </div>
            </div>
          </div>
          <div class="flex flex-1 mr-0.1rem my-0.1rem">
            <div
              class="h-0.4rem w-0.4rem"
              :style="{ background: `url(${getImageUrl('intelligent/emergency.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.6rem">
              <div class="text-[#B2DAEA] mb-0.1rem">应急响应</div>
              <!-- :style="{
                    color: tankWaterLevel?.thjDrainageStatus ? thjDrainageStatuses[tankWaterLevel?.thjDrainageStatus]?.color : '',
                  }" -->
              <div class="ml-0.1rem" :style="{ color: legendColors[2] }">
                {{ state.info?.emergencyCount }}
                <span class="text-[#B2DAEA] ml-0.05rem">次</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="h-2.1rem relative">
        <div
          class="left-0.16rem w-1.9rem h-0.27rem text-0.15rem px-0.2rem font-bold py-0.06rem text-[#B2DAEA] z-2 ml-0.16rem"
          :style="{ background: `url(${getImageUrl('intelligent/title-icon.png')}) no-repeat center / 100% 100%` }"
        >
          历月巡检及养护记录
        </div>
        <div class="intelligent-chart w-4.2rem h-1.6rem mt-0.2rem ml-0.05rem mr-0.16rem">
          <!-- :style="{ background: `url(${getImageUrl('intelligent/chart.png')}) no-repeat center / 100% 100%` }" -->
          <MultiBarLineChart :dataSource="state.barChartData" :year="year" />
        </div>
      </div>
      <div class="flex flex-col h-6.5rem relative">
        <div
          class="left-0.16rem w-1.9rem h-0.27rem text-0.15rem px-0.2rem font-bold py-0.06rem text-[#B2DAEA] z-2 ml-0.16rem"
          :style="{ background: `url(${getImageUrl('intelligent/title-icon.png')}) no-repeat center / 100% 100%` }"
        >
          进行中的工单
        </div>
        <!-- :style="{ background: `url(${getImageUrl('intelligent/chart.png')}) no-repeat center / 100% 100%` }" -->

        <div class="relative flex items-center h-0.5rem mt-0.05rem ml-0.16rem mr-0.16rem">
          <div
            v-for="(item, key) in rainData"
            :key="key"
            class="h-0.32rem w-0.93rem px-0.06rem text-0.15rem flex items-center justify-center mt-0rem mr-0.1rem cursor-pointer"
            :style="{
              background:
                currentNum == key
                  ? `url(${getImageUrl('right/content-btn2.png')}) no-repeat center / 100% 100%`
                  : `url(${getImageUrl('right/content-btn1.png')}) no-repeat center / 100% 100%`,
              color: currentNum == key ? '#09C8FC' : '#fff',
            }"
            @click="handleRainClick(item, key)"
          >
            {{ item.label }}
          </div>
          <div class="absolute right-0rem top-0.2rem text-0.12rem text-white flex cursor-pointer" @click="jumpTo()">
            更多
            <div
              class="w-0.28rem h0.28rem mt--0.08rem"
              :style="{
                background: `url(${getImageUrl('intelligent/more.png')}) no-repeat center / 100% 100%`,
              }"
            ></div>
          </div>
        </div>
        <div class="h-5.2rem mt-0.1rem ml-0.16rem mr-0.16rem">
          <!-- 巡检记录 -->
          <vue3-seamless-scroll
            :list="dataSource"
            class="data-scroll"
            hover
            :key="1"
            :step="1"
            v-if="dataSource?.length && currentNum == 0"
          >
            <div
              class="scroll-item relative text-0.14rem cursor-pointer"
              v-for="(el, index) in dataSource"
              :key="index"
              @click="selectSlide(index)"
            >
              <div
                class="item-content w-100% h-1.6rem flex flex-col mb-0.1rem overflow-hidden"
                :key="index"
                :class="{ active: activeSlide == index }"
                :style="{
                  background:
                    activeSlide == index
                      ? `url(${getImageUrl('right/content-type3.png')}) no-repeat center / 100% 100%`
                      : `url(${getImageUrl('right/content-type1.png')}) no-repeat center / 100% 100%`,
                }"
              >
                <div
                  class="text-overflow1 w-2.8rem px-0.12rem flex items-center mt-0.1rem mb-0.06rem text-0.16rem text-[#fff]"
                  :title="el?.taskName"
                >
                  {{ el?.taskName || '-' }}
                </div>
                <div class="px-0.12rem py-0.05rem flex items-center mt-0.06rem text-[#fff]">
                  <span class="text-[#B2DAEA]">巡检任务编码:&nbsp;</span>
                  {{ el?.taskCode || '-' }}
                </div>
                <div class="info-item-content h-0.58rem flex mt-0.1rem mx-0.14rem justify-between w-[93%] p-0.06rem">
                  <div class="flex flex-col flex-1">
                    <span class="text-[#B2DAEA]">巡检范围</span>
                    <div class="info-item-dept text-0.16rem text-[#09fcc7] mt-0.07rem" :title="el.lineName">
                      {{ el.lineName || '-' }}
                    </div>
                  </div>
                  <div class="flex flex-col flex-1">
                    <span class="text-[#B2DAEA]">巡检人</span>
                    <div class="info-item-user text-0.16rem text-[#09fcc7] mt-0.07rem" :title="el.patrolUserName">
                      {{ el.patrolUserName || '-' }}
                    </div>
                  </div>
                </div>
                <div class="h-0.32rem w-[100%] px-0.12rem flex items-center mb-0.06rem text-[#fff]">
                  <span class="text-[#B2DAEA]">计划巡检时间:&nbsp;</span>
                  <!-- {{ el.planStartTime + ' -- ' + (el.planEndTime ? dayjs(el.planEndTime).format('MM-DD HH:mm:ss') : '-') }} -->
                  {{ el.planStartTime + ' -- ' + el.planEndTime }}
                </div>
              </div>
              <div
                class="item-content-type absolute h-0.25rem w-1.06rem top-0rem right-0rem items-center text-center pt-0.05rem text-0.14rem text-[#fff] z-9999"
                :class="{ active: activeSlide == index }"
                :style="{
                  background:
                    el.taskStatus == 1
                      ? `url(${getImageUrl('right/type1.png')}) no-repeat center / 100% 100%`
                      : `url(${getImageUrl('right/type2.png')}) no-repeat center / 100% 100%`,
                  color: el.taskStatus == 1 ? '#F6CC57' : '#09C8FC',
                }"
              >
                {{ patrolTypes[el.taskStatus]?.name || '-' }}
              </div>
            </div>
          </vue3-seamless-scroll>

          <!-- 维养记录 -->
          <vue3-seamless-scroll
            :list="dataSource"
            class="data-scroll"
            hover
            :key="2"
            :step="1"
            v-else-if="dataSource?.length && currentNum == 1"
          >
            <div
              class="scroll-item relative text-0.14rem cursor-pointer"
              v-for="(el, index) in dataSource"
              :key="index"
              @click="selectSlide(index)"
            >
              <div
                class="item-content w-100% h-1.6rem flex flex-col mb-0.1rem overflow-hidden"
                :key="index"
                :class="{ active: activeSlide == index }"
                :style="{
                  background:
                    activeSlide == index
                      ? `url(${getImageUrl('right/content-type3.png')}) no-repeat center / 100% 100%`
                      : `url(${getImageUrl('right/content-type1.png')}) no-repeat center / 100% 100%`,
                }"
              >
                <div
                  class="text-overflow1 w-2.8rem px-0.12rem flex items-center mt-0.1rem mb-0.06rem text-0.16rem text-[#fff]"
                  :title="el.objectNames"
                >
                  {{ el.objectNames || '-' }}
                </div>
                <div class="px-0.12rem py-0.05rem flex items-center mt-0.06rem text-[#fff]">
                  <span class="text-[#B2DAEA]">工单编号:&nbsp;</span>
                  {{ el.serialNumber || '-' }}
                </div>
                <div class="info-item-content h-0.58rem flex mt-0.04rem mx-0.14rem justify-between w-[93%] p-0.06rem">
                  <div class="flex flex-col flex-1">
                    <span class="text-[#B2DAEA]">维养单位</span>
                    <div class="info-item-dept text-0.16rem text-[#09fcc7] mt-0.07rem" :title="el.deptName">
                      {{ el.deptName || '-' }}
                    </div>
                  </div>
                  <div class="flex flex-col flex-1">
                    <span class="text-[#B2DAEA]">维养人员</span>
                    <div class="info-item-user text-0.16rem text-[#09fcc7] mt-0.07rem" :title="el.userNames">
                      {{ el.userNames || '-' }}
                    </div>
                  </div>
                  <div class="flex flex-col flex-1 text-[##09FCC7]">
                    <span class="text-[#B2DAEA]">维养时间</span>
                    <span class="text-0.16rem text-[#09fcc7] mt-0.07rem">{{ el.maintenanceDate || '-' }}</span>
                  </div>
                </div>
                <div class="h-0.32rem px-0.12rem py-0.05rem flex items-center mb-0.06rem text-[#fff]">
                  <span class="text-[#B2DAEA]">计划内容:&nbsp;</span>
                  {{ el.content || '-' }}
                </div>
              </div>
              <div
                class="item-content-type absolute h-0.25rem w-1.06rem top-0rem right-0rem items-center text-center pt-0.05rem text-0.14rem text-[#fff] z-9999"
                :class="{ active: activeSlide == index }"
                :style="{
                  background:
                    el.status == 1
                      ? `url(${getImageUrl('right/type1.png')}) no-repeat center / 100% 100%`
                      : `url(${getImageUrl('right/type2.png')}) no-repeat center / 100% 100%`,
                  color: el.status == 1 ? '#F6CC57' : '#09C8FC',
                }"
              >
                {{ maintenanceTypes[el.status]?.name || '-' }}
              </div>
            </div>
          </vue3-seamless-scroll>
          <!-- 应急响应 -->
          <vue3-seamless-scroll
            :list="dataSource"
            class="data-scroll"
            hover
            :key="3"
            :step="1"
            v-else-if="dataSource?.length && currentNum == 2"
          >
            <div
              class="scroll-item relative text-0.14rem cursor-pointer"
              v-for="(el, index) in dataSource"
              :key="index"
              @click="selectSlide(index)"
            >
              <div
                class="item-content w-100% h-1.6rem flex flex-col mb-0.1rem overflow-hidden"
                :key="index"
                :class="{ active: activeSlide == index }"
                :style="{
                  background:
                    activeSlide == index
                      ? `url(${getImageUrl('right/content-type3.png')}) no-repeat center / 100% 100%`
                      : `url(${getImageUrl('right/content-type1.png')}) no-repeat center / 100% 100%`,
                }"
              >
                <div
                  class="text-overflow1 w-2.8rem px-0.12rem flex items-center mt-0.1rem mb-0.06rem text-0.16rem text-[#fff]"
                  :title="el.disposeFeedback"
                >
                  {{ el.disposeFeedback || '-' }}
                </div>
                <div class="px-0.12rem py-0.05rem flex items-center mt-0.06rem text-[#fff]">
                  <span class="text-[#B2DAEA]">应急工单编号:&nbsp;</span>
                  {{ el.serialNumber || '-' }}
                </div>
                <div class="info-item-content h-0.58rem flex mt-0.04rem mx-0.14rem justify-between w-[93%] p-0.06rem">
                  <div class="flex flex-col flex-1">
                    <span class="text-[#B2DAEA]">提报单位</span>
                    <div class="info-item-dept text-0.16rem text-[#09fcc7] mt-0.07rem" :title="el.deptName">
                      {{ el.deptName || '-' }}
                    </div>
                  </div>
                  <div class="flex flex-col w-0.7rem">
                    <span class="text-[#B2DAEA]">应急类型</span>
                    <div class="info-item-user text-0.16rem text-[#09fcc7] mt-0.07rem">
                      {{ emergencyStatuses[el.emergencyType]?.name || '-' }}
                    </div>
                  </div>
                  <div class="flex flex-col w-1.6rem text-[##09FCC7]">
                    <span class="text-[#B2DAEA]">提报时间</span>
                    <span class="text-0.16rem w-1.6rem text-[#09fcc7] mt-0.07rem">{{ el.createdTime || '-' }}</span>
                  </div>
                </div>
                <div class="h-0.32rem px-0.12rem py-0.05rem flex items-center mb-0.06rem text-[#fff]">
                  <span class="text-[#B2DAEA]">问题描述:&nbsp;</span>
                  {{ el.content || '-' }}
                </div>
              </div>
              <div
                class="item-content-type absolute h-0.25rem w-1.06rem top-0rem right-0rem items-center text-center pt-0.05rem text-0.14rem text-[#fff] z-9999"
                :class="{ active: activeSlide == index }"
                :style="{
                  background:
                    el.status == 1
                      ? `url(${getImageUrl('right/type2.png')}) no-repeat center / 100% 100%`
                      : `url(${getImageUrl('right/type1.png')}) no-repeat center / 100% 100%`,
                  color: el.status == 1 ? '#09C8FC' : '#F6CC57',
                }"
              >
                {{ emergencyTypes[el.status]?.name || '-' }}
              </div>
            </div>
          </vue3-seamless-scroll>
          <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
        </div>
      </div>
      <!-- <Indicator v-for="item in dataSource" :key="item.field" v-bind="item" class="w-50% mt-0.2rem" /> -->
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="PatrolMaintenance">
  import {
    objectCategoryCountByFirstLevel,
    getConsumptionList,
    getPatrolList,
    getMaintenanceRecordList,
    getEmergencyList,
    getPatrolCount,
  } from '../services'
  import { overview, chartsBgColor, gradientColors } from '@/constants'

  import { over } from 'lodash-es'

  import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'

  import { waterSourceTypes, maintenanceTypes, patrolTypes, emergencyTypes, emergencyStatuses } from '@/constants'
  import MultiBarLineChart from './MultiBarLineChart.vue'
  import dayjs from 'dayjs'
  import { getOptions } from '@/api'
  import { useUserStore } from '@/store'
  const userStore = useUserStore()

  const legendColors = ['#09C8FC', '#09FCC7', '#F6CC57']
  const year = ref(null)
  const swiperInstance = ref(null)
  //初始化swiper后，为swiperInstance赋值
  const activeSlide = ref(null)
  const selectSlide = index => {
    activeSlide.value = index
    if (swiperInstance.value) {
      swiperInstance.value.slideTo(index)
    }
  }

  const state = reactive({
    linkList: [],
    yearList: [],
    barChartData: [],
    info: {},
    loading: false,
    overview: {
      project: [],
      site: [],
      reservoir: [],
      canal: [],
    },
  })

  const rainData = ref([])
  const currentNum = ref(0)
  rainData.value = [
    { label: '巡检记录', value: 1 },
    { label: '维养记录', value: 2 },
    { label: '应急响应', value: 3 },
  ]
  onMounted(() => {
    year.value = new Date().getFullYear()
    init()
    generateYearList()
  })

  watch(
    year,
    newVal => {
      if (newVal) {
        getPatrolCount({ year: year.value }).then(res => {
          state.info = res.data
          state.barChartData = [
            {
              name: '运行中',
              xData: res.data?.patrolList?.map(el => el.month),
              data1: res.data?.patrolList?.map(el => [el.month, el.count]),
              data2: res.data?.conserveList?.map(el => [el.month, el.count]),
              data3: res.data?.emergencyList?.map(el => [el.month, el.count]),
            },
          ]
        })
      }
    },
    { immediate: true },
  )
  const handleRainClick = (item, key) => {
    currentNum.value = key
    getList()
    //1巡检记录 2维养记录 3应急响应
  }
  // const overview = ref({})
  const generateYearList = () => {
    const currentYear = new Date().getFullYear()
    const startYear = 2023 // 生成从当前年份到前6年的年份数组
    for (let year = currentYear; year >= startYear; year--) {
      state.yearList.push({ label: String(year), value: String(year) })
    }
  }

  // const appStore = useAppStore()
  const dataSource = ref([])
  // const barChartData = ref()

  const getList = async () => {
    let paramMaintenance = {
      deptId: null,
      endTime: '',
      objectCategoryCode: '',
      objectId: null,
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
      serialNumber: '',
      sort: [],
      startTime: '',
      status: '',
      type: 1,
    }

    let paramPatrol = {
      groupId: null,
      isForceEnded: null,
      isIncomplete: null,
      isTemp: null,
      lineId: null,
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
      patrolType: 2,
      patrolUserName: '',
      planName: '',
      planStartTimeHigh: '',
      planStartTimeLow: '',
      shiftId: null,
      sort: [],
      taskCode: '',
      taskName: '',
      taskStatus: null,
      type: 1,
    }
    let paramEmergency = {
      deptId: null,
      emergencyType: null,
      objectCategoryCode: '',
      objectId: null,
      pageNum: 1,
      pageSize: Number.MAX_SAFE_INTEGER,
      serialNumber: '',
      sort: [],
      status: '',
      type: 1,
    }

    let mData = await getMaintenanceRecordList(paramMaintenance)
    let pData = await getPatrolList(paramPatrol)
    let eData = await getEmergencyList(paramEmergency)
    // pData.data.data = [
    //   {
    //     lineName: '线路名称1',
    //     patrolUserName: '刘乐乐',
    //     planName: '巡检范围测试1',
    //     taskCode: 'XC123455',
    //     taskEndTime: '2024-11-20 15:00:05',
    //     taskId: 1,
    //     taskName: '测试任务名称1',
    //     taskStartTime: '2024-11-20 05:00:05',
    //     taskStatus: '1',
    //   },
    //   {
    //     lineName: '线路名称21',
    //     patrolUserName: '刘强',
    //     planName: '巡检测试21',
    //     taskCode: 'XC123421',
    //     taskEndTime: '2024-11-20 15:00:05',
    //     taskId: 1,
    //     taskName: '测试任务名称1',
    //     taskStartTime: '2024-11-20 05:00:05',
    //     taskStatus: '2',
    //   },
    // ]
    dataSource.value = mData?.data?.data || []
    dataSource.value =
      currentNum.value == 0
        ? pData?.data?.data
        : currentNum.value == 1
          ? mData?.data?.data
          : currentNum.value == 2
            ? eData?.data?.data
            : []
  }
  //

  const init = () => {
    getOptions('screenJumpLink').then(res => {
      state.linkList = res.data
    })
    getList()
  }

  const jumpTo = () => {
    let val = currentNum.value == 0 ? 6 : currentNum.value == 1 ? 5 : currentNum.value == 2 ? 4 : 6

    state.linkList?.forEach(item => {
      if (item.key == val) {
        const url = val == 6 ? `${item.value}&token=${userStore.token}` : `${item.value}?token=${userStore.token}`

        window.open(url, '_blank')
      }
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }

  .swiper-container {
    width: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
  }
  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .row-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  .row-item {
    display: flex;
    cursor: pointer;
  }
  .row-item:active {
    background: #1b5e63;
  }
  .active {
    background: #1b5e63;
  }
  .icon {
    font-size: 24px;
    margin-right: 10px;
  }
  .content {
    font-size: 16px;
  }
  .data-scroll {
    height: 100%;
    // background: red;
    overflow: hidden;

    .scroll-item {
      // margin-right: 0.2rem;
      width: 100%;
      height: 1.66rem;
      // background: url('@/assets/images/intelligent/chart.png') no-repeat;
      background-size: 100% 100%;
      margin-bottom: 10px;
    }
  }

  :deep(.swiper-button-prev, .swiper-button-next) {
    border: 1px solid #374147;
    color: #374147;
  }

  :deep(.swiper-button-prev) {
    width: 0.24rem;
    height: 0.24rem;
    left: 3.3rem !important;
    top: 0.25rem !important;
  }
  :deep(.swiper-button-next) {
    border: 1px solid #374147;
    width: 0.24rem;
    height: 0.24rem;
    top: 0.25rem !important;
    right: -0.05rem !important;
  }
  :deep(.swiper-button-prev:hover) {
    border: 1px solid #00d4ff;
    color: #00d4ff;
  }
  :deep(.swiper-button-next:hover) {
    border: 1px solid #00d4ff;
    color: #00d4ff;
  }
  :deep(.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after) {
    color: #00d4ff !important;
    position: absolute !important;
    // top: -0.76rem !important;
    // right: -0.1rem !important;
    font-size: 0.14rem !important;
    // z-index: 4 !important;
  }
  :deep(.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after) {
    color: #00d4ff !important;
    position: absolute !important;
    // top: -0.76rem !important;
    // right: -3.2rem !important;
    font-size: 0.14rem !important;
    // z-index: 4 !important;
  }
  .left-item {
    width: 0.6rem;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    // border: 1px solid #ccc; /* 可选：添加边框以便更好地看到效果 */
  }
  .info-item-content {
    // width: 374px;
    // height: 54px;
    border-radius: 2px;
    opacity: 1;

    background: linear-gradient(180deg, rgba(85, 130, 148, 0.24) 0%, rgba(85, 130, 148, 0) 100%);

    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(91deg, rgba(255, 255, 255, 0) 12%, rgba(255, 255, 255, 0.38) 44%, rgba(255, 255, 255, 0) 91%) 1;
  }
  .info-item-dept,
  .info-item-user {
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
  }
  .info-item-dept {
    width: 1.2rem;
  }
  .info-item-user {
    width: 1rem;
  }
</style>
