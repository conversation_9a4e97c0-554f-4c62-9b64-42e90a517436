<template>
  <MyCard name="灌区水利设施">
    <div class="content pt-0.2rem h-9.3rem">
      <!-- 水库建设 -->
      <div class="item flex flex-col items-center h-1.26rem w-3.98rem mb-0.20rem relative ml-0.16rem">
        <div
          class="absolute h-0.32rem w-1.35rem top--0.15rem pt-0.1rem px-0.14rem left-0.02rem text-0.15rem font-bold text-[#ffffff] z-2"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">水库建设</span>
        </div>
        <div class="reservoir-content text-0.14rem mt-0.06rem pt-0.16rem flex">
          <!-- ml-0.06rem mr-0.1rem -->
          <div
            class="row-item flex flex-1 mb-0.08rem ml-0.08rem mr-0.08rem pb-0.2rem z-3"
            :class="item.isActive ? 'active' : ''"
            v-for="(item, index) in state.overview.reservoir"
            :key="index"
            @click="handleClick('reservoir', item)"
          >
            <div
              class="h-0.4rem w-0.4rem ml-0.2rem mt-0.06rem"
              :style="{ background: `url(${getImageUrl(`icon/reservoir${item.code}.png`)}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.6rem">
              <div class="text-[#B2DAEA] mb-0.1rem">{{ item.title }}</div>
              <div class="construction row-item-value">
                <span class="text-[#ffffff] text-0.24rem">{{ item.count }}</span>

                <span class="text-[#699DB2] text-0.14rem">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 水利工程概况 -->
      <div class="item flex flex-col items-center h-2.6rem w-3.98rem mb-0.40rem relative ml-0.16rem">
        <div
          class="absolute h-0.32rem w-1.35rem top--0.15rem pt-0.1rem px-0.14rem left-0.02rem text-0.15rem font-bold text-[#ffffff] z-2"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">水利工程概况</span>
        </div>
        <div class="project-content text-0.14rem mt-0.06rem">
          <swiper
            class="mt-0.3rem pt-0.1rem ml-0.08rem w-3.8rem"
            v-if="state.rows.length > 0"
            ref="swiperRef"
            :modules="modules"
            :slides-per-view="3"
            :space-between="10"
            :navigation="true"
            :autoplay="{ delay: 2000, disableOnInteraction: false }"
            :loop="true"
            :speed="1000"
            @swiper="onSwiper"
            @slideChange="onSlideChange"
          >
            <!-- bg-green -->
            <swiper-slide class="flex-1" v-for="(row, rowIndex) in state.rows" :key="rowIndex">
              <!-- w-1.3rem bg-red -->
              <div class="row-container">
                <!-- w-1.1rem mt-0.1rem pt-0.1rem pb-0.05rem-->
                <div
                  class="row-item2 mt-0.1rem"
                  :class="item.isActive ? 'active' : ''"
                  v-for="(item, itemIndex) in row"
                  :key="itemIndex"
                  @click="handleClick('project', item)"
                >
                  <div
                    class="h-0.45rem w-0.45rem ml-0.02rem mt-0rem"
                    :style="{ background: `url(${getImageUrl(`icon/project${item.code}.png`)}) no-repeat center / 100% 100%` }"
                  ></div>
                  <div class="flex flex-1 flex-col ml-0.02rem w-0.86rem">
                    <div class="text-[#B2DAEA] mb-0.1rem">{{ item.title }}</div>
                    <div class="row-item-value">
                      <span class="text-[#ffffff] text-0.2rem">{{ item.count }}</span>
                      <span class="text-[#699DB2] text-0.14rem">{{ item.unit }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <!-- <div class="custom-navigation">
            <button @click="prev"></button>
            <button @click="next"></button>
          </div> -->
          <!-- <div class="flex flex-1 mr-0.1rem mt-0.2rem" v-for="(item, index) in overview.project" :key="index">
            <div
              class="h-0.4rem w-0.4rem pl-0.18rem"
              :style="{ background: `url(${getImageUrl('right/current-water.png')}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0.1rem w-0.6rem">
              <div class="text-[#B2DAEA] mb-0.1rem">{{ item.title }}</div>
              <div class="text-[#ffffff] ml-0.1rem">
                {{ item.count }}
                <span class="text-[#B2DAEA] ml-0.05rem">{{ item.unit }}</span>
              </div>
            </div>
          </div> -->
        </div>
      </div>
      <!-- 物联感控体系站点 -->
      <div class="item flex flex-col items-center h-2.2rem w-3.98rem mb-0.15rem relative ml-0.16rem">
        <div
          class="absolute h-0.32rem w-1.8rem top--0.15rem pt-0.1rem px-0.14rem left-0.02rem text-0.15rem font-bold text-[#ffffff] z-2"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">物联感控体系站点</span>
        </div>
        <!-- pt-0.1rem pb-0.1rem pr-0.08rem mr-0.1rem ml-0.1rem flex flex-wrap -->
        <div class="system-content text-0.14rem mt-0.06rem">
          <swiper
            class="mt-0.3rem pt-0.1rem ml-0.08rem w-3.8rem"
            v-if="state.siteRows.length > 0"
            ref="swiperRef"
            :modules="modules"
            :slides-per-view="3"
            :space-between="10"
            :navigation="true"
            :autoplay="{ delay: 2000, disableOnInteraction: false }"
            :loop="true"
            :speed="1000"
            @swiper="siteOnSwiper"
            @slideChange="siteOnSlideChange"
          >
            <!-- bg-green -->
            <swiper-slide class="flex-1" v-for="(row, rowIndex) in state.siteRows" :key="rowIndex">
              <!-- w-1.3rem bg-red -->
              <div class="row-container w-1.3rem">
                <!-- w-1.1rem mt-0.1rem pt-0.1rem pb-0.05rem-->
                <div
                  class="row-item2 mt-0.1rem"
                  :class="item.isActive ? 'active' : ''"
                  v-for="(item, itemIndex) in row"
                  :key="itemIndex"
                  @click="handleClick('site', item)"
                >
                  <div
                    class="h-0.45rem w-0.45rem ml-0.02rem mt-0rem"
                    :style="{ background: `url(${getImageUrl(`icon/site${item.code}.png`)}) no-repeat center / 100% 100%` }"
                  ></div>
                  <div class="flex flex-1 flex-col ml-0.02rem w-0.86rem">
                    <div class="text-[#B2DAEA] mb-0.1rem w-0.9rem">{{ item.title }}</div>
                    <div class="row-item-value">
                      <span class="text-[#ffffff] text-0.2rem">{{ item.count }}</span>
                      <span class="text-[#699DB2] text-0.14rem">{{ item.unit }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
          <!-- <div
            class="row-item flex flex-1 ml-0.08rem z-3 mt-0.1rem"
            :class="item.isActive ? 'active' : ''"
            v-for="(item, index) in state.overview.site"
            :key="index"
            @click="handleClick('site', item)"
          >
            <div
              class="h-0.45rem w-0.45rem ml-0.02rem mt-0.05rem"
              :style="{ background: `url(${getImageUrl(`icon/site${item.code}.png`)}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0rem w-0.7rem">
              <div class="text-[#B2DAEA] mb-0.1rem w-0.9rem">{{ item.title }}</div>
              <div class="row-item-value">
                <span class="text-[#ffffff] text-0.2rem">{{ item.count }}</span>
                <span class="text-[#699DB2] text-0.14rem">{{ item.unit }}</span>
              </div>
            </div>
          </div> -->
        </div>
      </div>
      <!-- 渠系概况 -->
      <div class="item flex flex-col items-center h-1.9rem w-3.98rem relative mb-0.2rem ml-0.12rem">
        <div
          class="absolute h-0.32rem w-1.35rem top--0.15rem pt-0.1rem px-0.14rem left-0.06rem text-0.15rem font-bold text-[#ffffff] z-2"
          :style="{ background: `url(${getImageUrl('right/content-header-icon.png')}) no-repeat center / 100% 100%` }"
        >
          <span class="header-title">渠系概况</span>
        </div>
        <div class="canal-content text-0.14rem mt-0.06rem pt-0.1rem pb-0.1rem pr-0.08rem mr-0.1rem ml-0.1rem flex flex-wrap">
          <div
            class="row-item flex flex-1 ml-0.08rem z-3 mt-0.1rem"
            :class="item.isActive ? 'active' : ''"
            v-for="(item, index) in state.overview.canal"
            :key="index"
            @click="handleClick('canal', item)"
          >
            <div
              class="h-0.45rem w-0.45rem ml-0.02rem mt-0.05rem"
              :style="{ background: `url(${getImageUrl(`icon/canal${item.code}.png`)}) no-repeat center / 100% 100%` }"
            ></div>
            <div class="flex flex-col ml-0rem w-0.7rem">
              <div class="left-item text-[#B2DAEA] mb-0.1rem" :title="item.title">{{ item.title }}</div>
              <div class="row-item-value w-0.7rem">
                <span class="text-[#ffffff] text-0.2rem">{{ item.count }}</span>
                <span class="text-[#699DB2] text-0.14rem">{{ item.unit }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- <Indicator v-for="item in dataSource" :key="item.field" v-bind="item" class="w-50% mt-0.2rem" /> -->
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="DeviceInfo">
  import { objectCategoryCountByFirstLevel } from '../services'

  import axios from 'axios'
  import { Swiper, SwiperSlide } from 'swiper/vue'
  import { Navigation, Pagination, A11y, Autoplay } from 'swiper/modules'
  import 'swiper/css'
  import 'swiper/css/navigation'
  import 'swiper/css/pagination'
  import 'swiper/css/autoplay'
  import { over } from 'lodash-es'
  import { getNewColor, getNewIcon } from '../config.ts'
  import { getOptions } from '@/api'
  import { formatDecimal } from '@/utils'
  import { json } from 'node:stream/consumers'

  const indicators = [
    {
      img: getImageUrl('indicators/sumSite.png'),
      field: 'sumSite',
      unit: '个',
      label: '站点数量',
    },
    {
      img: getImageUrl('indicators/sumTerminal.png'),
      field: 'sumTerminal',
      unit: '个',
      label: '终端数量',
    },
  ]

  const state = $ref({
    canalList: [],
    cameraArr: [],
    rows: [],
    siteRows: [],
    loading: false,
    overview: {
      project: [],
      site: [],
      reservoir: [],
      canal: [],
    },
    dataSource: [],
    allSource: {},
    tabLevel1: null,
  })
  // const overview = ref({})
  // overview.value = {
  //   project: [],
  //   site: [],
  //   reservoir: [],
  //   canal: [],
  // }
  const modules = [Navigation, Pagination, A11y, Autoplay]
  const swiperInstance = ref<typeof Swiper | null>(null)
  // const swiperInstance = ref(null)
  const swiperRef = ref(null)
  //初始化swiper后，为swiperInstance赋值
  const activeSlide = ref(null)
  const onSwiper = (swiper: typeof Swiper) => {
    swiperInstance.value = swiper.activeIndex
    swiperRef.value = swiper
    //swiperInstance.value = swiper
  }
  //监听slide滑块更改事件回调
  const onSlideChange = swiper => {
    swiperRef.value = swiper
    swiperInstance.value = swiper.activeIndex
    // console.log('slide change')
  }
  //site列表
  const siteOnSwiper = (swiper: typeof Swiper) => {
    swiperInstance.value = swiper.activeIndex
    swiperRef.value = swiper
    //swiperInstance.value = swiper
  }
  //监听slide滑块更改事件回调
  const siteOnSlideChange = swiper => {
    swiperRef.value = swiper
    swiperInstance.value = swiper.activeIndex
    // console.log('slide change')
  }
  const selectSlide = index => {
    // console.log('*** 2', index)
    activeSlide.value = index
    if (swiperInstance.value) {
      swiperInstance.value.slideTo(index)
    }
  }

  const initMouseEvents = () => {
    // const swiper = this.$refs.mySwiper.$swiper;
    const swiper = swiperRef.value
    // console.log('swiper', swiper)
    // 当鼠标进入时停止自动滚动
    swiper.el.addEventListener('mouseenter', () => {
      if (swiper.autoplay) {
        swiper.autoplay.stop()
      }
    })

    // 当鼠标离开时恢复自动滚动
    swiper.el.addEventListener('mouseleave', () => {
      if (swiper.autoplay) {
        swiper.autoplay.start()
      }
    })
  }

  // overview.project
  // const rows = overview?.project?.reduce((acc, item, index) => {
  //   if (index % 3 === 0) acc.push([])
  //   acc[acc.length - 1].push(item)
  //   return acc
  // }, [])

  const prev = () => {
    if (swiperInstance.value) {
      // console.log('*** prev 1', swiperInstance.value)
      swiperInstance.value.slidePrev()
    }
  }

  const next = () => {
    if (swiperInstance.value) {
      // console.log('*** next 1', swiperInstance.value)
      swiperInstance.value.slideNext()
    }
  }
  // const appStore = useAppStore()
  const dataSource = ref([])
  dataSource.value = indicators
  // 触发模块
  const activeItem = defineModel('activeItem')

  watch(
    () => state.overview?.project,
    newVal => {
      state.rows = state.overview?.project?.reduce((acc, item, index) => {
        if (index % 3 === 0) acc.push([])
        acc[acc.length - 1].push(item)
        return acc
      }, [])
    },
  )
  watch(
    () => state.overview?.site,
    newVal => {
      state.siteRows = state.overview?.site?.reduce((acc, item, index) => {
        if (index % 2 === 0) acc.push([])
        acc[acc.length - 1].push(item)
        return acc
      }, [])
    },
    { immediate: true },
  )
  const allData = defineModel('allData')
  onMounted(() => {
    getOptions('chanType').then((res: any) => {
      state.canalList = res.data
    })
    init()
    setTimeout(() => {
      nextTick(() => {
        initMouseEvents()
      })
    }, 1000)
    //
  })
  const handleClick = (section, item) => {
    let flag = null
    state.overview[section] = state.overview[section].map(el => {
      if (el.code === item.code) {
        el.isActive = !el.isActive
        flag = el.isActive
      }
      return el
    })
    // item.code = item.code == 'HP003' ? 6 : item.code
    // let filterParam = `<Or><PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${item.code}</Literal></PropertyIsEqualTo></Or>`
    if (item) {
      let propertyIsEqualTos = null
      if (section == 'project') {
        propertyIsEqualTos = item.codes
          ?.map(
            value => `<PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${value}</Literal></PropertyIsEqualTo>`,
          )
          .join('')
      } else {
        propertyIsEqualTos = `<PropertyIsEqualTo><PropertyName>map_class</PropertyName><Literal>${item.code}</Literal></PropertyIsEqualTo>`
      }

      axios(
        `${import.meta.env.VITE_GEOSERVER_BASE}${import.meta.env.VITE_GEOSERVER_URL}&typeName=${item.gisLayer}&filter=<Or>${propertyIsEqualTos}</Or>`,
      ).then(res => {
        const data = res.data || null
        activeItem.value = {
          ...data,
          isActive: flag,
          id: section + '-' + item.code,
          code: item.code,
          section,
        }
      })
    }
  }
  const init = () => {
    state.loading = true
    const val = 3

    objectCategoryCountByFirstLevel({ objectCategoryId: 3 }).then((res: any) => {
      state.loading = false
      localStorage.setItem('objectCategory3', JSON.stringify(res.data))
      // 存储目录信息
      state.dataSource = (res.data || []).map((el, idx) => ({
        ...el,
        checked: state.allSource?.[val]?.[idx]?.checked || false,
        indeterminate: state.allSource?.[val]?.[idx]?.indeterminate || false,
        // color: getNewColor(tabLevel1Code.value, el.objectCategoryCode, index),
        // icon: getNewIcon(tabLevel1Code.value, el.objectCategoryCode, index, ele.code),

        items: el.items.map((ele, index) => ({
          ...ele,
          // color: getColor(tabLevel1Code.value, el.objectCategoryCode, index),
          // icon: getIcon(tabLevel1Code.value, el.objectCategoryCode, index, ele.code),
          checked: state.allSource?.[val]?.[idx]?.items?.[index]?.checked || false,
        })),
      }))
      state.allSource[val] = state.dataSource

      let res1 = res.data
      const HP001Info = res1.find(item => item.objectCategoryCode == 'HP001')
      const HP005Info = res1.find(item => item.objectCategoryCode == 'HP005')
      const HP001List = HP001Info.items

      const result = HP001List?.reduce((acc, item) => {
        if (item.code === '3') {
          acc['3'] = {
            ...item,
            title: '中型',
            url: '/totalCanal',
            gisLayer: HP001Info.gisLayer,
            isActive: false,
            unit: '座',
            color: '#fff',
          }
        } else {
          if (!acc['4']) {
            acc['4'] = {
              code: '5',
              title: '小型',
              url: '/totalCanal',
              gisLayer: HP001Info.gisLayer,
              isActive: false,
              count: 0,
              unit: '座',
              color: '#fff',
            }
          }
          acc['4'].count += item.count
        }
        return acc
      }, {})

      const codesToFilter = [
        'HP006',
        'HP012',
        'HP013',
        'HP014',
        'HP016',
        'HP017',
        'HP018',
        'HP019',
        'HP020',
        'HP021',
        'HP034',
        'HP001',
        'HP005',
        'HP004',
      ]
      //   const codesToFilter = ['MS001']
      // let siteArr = res.data?.filter(item => codesToFilter.includes(item.objectCategoryCode)) || []

      let list = res1.filter(item => !codesToFilter.includes(item.objectCategoryCode))
      // const list = res1.filter(item => item.objectCategoryCode !== 'HP001')
      list = [...list].reverse()
      state.overview.project = list.map(item => {
        const codes = item.items?.filter(subItem => subItem.count != 0).map(subItem => parseInt(subItem.code, 10))
        return {
          codes: codes,
          code: item.objectCategoryCode,
          title: item.objectCategoryName,
          url: '/totalCanal',
          count: item.total,
          gisLayer: item.gisLayer,
          isActive: false,
          unit: '座',
          color: '#fff',
        }
      })
      state.overview.reservoir = Object.values(result)
      state.overview.canal = HP005Info?.items.map(item => ({
        code: item.code,
        title: item.name,
        url: '/totalCanal',
        count: state.canalList?.find(el => el.key == item.code).option1,
        gisLayer: HP005Info.gisLayer,
        isActive: false,
        unit: '公里',
        color: '#fff',
      }))
      setTimeout(() => {
        nextTick(() => {
          handleClick('reservoir', state.overview.reservoir[0])
          handleClick('canal', state.overview.canal[0])
          handleClick('canal', state.overview.canal[1])
        })
      }, 1000)
      // indicators.map(el => ({ ...el, value: res.data?.[el.field] }))
      // console.log('**** res1-21', state.overview.project)
    })
    // objectCategoryCountByFirstLevel({ objectCategoryId: 3, isDataMode: false }).then(res => {
    //   state.loading = false
    //   let res1 = res.data
    //   const codesToFilter = [
    //     'HP006',
    //     'HP012',
    //     'HP013',
    //     'HP014',
    //     'HP016',
    //     'HP017',
    //     'HP018',
    //     'HP019',
    //     'HP020',
    //     'HP021',
    //     'HP034',
    //     'HP001',
    //     'HP005',
    //     'HP004',
    //   ]
    //   //   const codesToFilter = ['MS001']
    //   // let siteArr = res.data?.filter(item => codesToFilter.includes(item.objectCategoryCode)) || []

    //   let list = res1.filter(item => !codesToFilter.includes(item.objectCategoryCode))
    //   // const list = res1.filter(item => item.objectCategoryCode !== 'HP001')
    //   list = [...list].reverse()
    //   state.overview.project = list.map(item => {
    //     const codes = item.items?.filter(subItem => subItem.count != 0).map(subItem => parseInt(subItem.code, 10))
    //     return {
    //       codes: codes,
    //       code: item.objectCategoryCode,
    //       title: item.objectCategoryName,
    //       url: '/totalCanal',
    //       count: item.total,
    //       gisLayer: item.gisLayer,
    //       isActive: false,
    //       unit: '座',
    //       color: '#fff',
    //     }
    //   })
    //   // indicators.map(el => ({ ...el, value: res.data?.[el.field] }))
    // })

    objectCategoryCountByFirstLevel({ objectCategoryId: 4 }).then(res => {
      localStorage.setItem('objectCategory4', JSON.stringify(res.data))
      const codesToFilter = ['MS001']
      let siteArr = res.data?.filter(item => codesToFilter.includes(item.objectCategoryCode)) || []

      const siteCodes = ['ZQ', 'SL', 'EL', 'ZZ', 'SS', 'FL', 'SLJC']
      const titleMap = {
        ZQ: '水雨情监测',
        SL: '量测水监测',
        ZZ: '闸站水位监测',
        SS: '墒情监测',
        EL: '安全监测',
        FL: '流量站',
        SLJC: '水量监测站',
      }
      state.overview.site = siteArr?.[0]?.items
        .filter(item => siteCodes.includes(item.code))
        .map(item => ({
          code: item.code,
          title: titleMap[item.code],
          url: '/totalCanal',
          count: item.count,
          gisLayer: siteArr?.[0]?.gisLayer,
          isActive: false,
          unit: '处',
          color: '#fff',
        }))
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 0.01rem solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  .header-title {
    font-family: SourceHanSansCN-Medium;
    font-size: 0.15rem;
    font-weight: 500;
    line-height: 121.86%;
    letter-spacing: 0em;

    font-variation-settings: 'opsz' auto;
    font-feature-settings: 'kern' on;
    background: linear-gradient(179deg, #ffffff 49%, rgba(9, 252, 199, 0.36) 90%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }
  .reservoir-content {
    // height: 0.97rem;
    width: 3.98rem;
    height: 0.98rem;
    border-radius: 0.02rem;
    border: 0.01rem solid;
    background: linear-gradient(181deg, rgba(25, 83, 106, 0.4) 1%, rgba(0, 205, 161, 0) 57%);
    border-image: radial-gradient(4% 33% at 3% 100%, rgba(0, 215, 255, 0.6) 0%, rgba(90, 228, 255, 0) 100%) 1;
  }
  .project-content {
    height: 2.66rem;
    width: 3.98rem;
    border-radius: 0.02rem;
    overflow: hidden;
    // background: linear-gradient(182deg, #1b5e63 7%, rgba(0, 205, 161, 0) 56%);

    border: 0.01rem solid;

    background: linear-gradient(181deg, rgba(25, 83, 106, 0.4) 1%, rgba(0, 205, 161, 0) 57%);
    border-image: radial-gradient(4% 33% at 3% 100%, rgba(0, 215, 255, 0.6) 0%, rgba(90, 228, 255, 0) 100%) 1;
  }
  .system-content {
    height: 1.82rem;
    width: 3.98rem;
    border-radius: 0.02rem;
    // opacity: 0.7;
    border: 0.01rem solid;
    background: linear-gradient(181deg, rgba(25, 83, 106, 0.4) 1%, rgba(0, 205, 161, 0) 57%);
    border-image: radial-gradient(4% 33% at 3% 100%, rgba(0, 215, 255, 0.6) 0%, rgba(90, 228, 255, 0) 100%) 1;
  }
  .canal-content {
    height: 1.82rem;
    width: 3.98rem;
    border-radius: 0.02rem;
    // opacity: 0.7;
    border: 0.01rem solid;
    background: linear-gradient(181deg, rgba(25, 83, 106, 0.4) 1%, rgba(0, 205, 161, 0) 57%);
    border-image: radial-gradient(4% 33% at 3% 100%, rgba(0, 215, 255, 0.6) 0%, rgba(90, 228, 255, 0) 100%) 1;
  }
  .swiper-container {
    width: 100%;
    position: relative;
    display: flex;
    flex-wrap: nowrap;
  }
  .swiper-slide {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .construction {
    width: 0.9rem;
    height: 0.5rem;
    padding: 0.02rem 0;
    border-radius: 4px;
    opacity: 1;
    // background: red;
    background: linear-gradient(273deg, rgba(114, 204, 240, 0.1) 3%, rgba(98, 188, 219, 0) 99%);
  }
  .row-container {
    display: flex;
    flex-wrap: wrap;
    // background: red;
    width: 1.2rem;
    margin-top: -0.1rem;
    // gap: 10px;
  }
  .row-item2 {
    display: flex;
    // padding-right: 4px;
    cursor: pointer;
    padding-top: 0.08rem;
    padding-bottom: 0.06rem;
    width: 1.2rem;
  }
  .row-item {
    display: flex;
    padding-top: 10px;
    cursor: pointer;
    width: 1.2rem;
    // margin-right: 0.08rem;
    // align-items: center;
    // width: calc(100% / 3 - 20px); /* 每行3个，减去gap */
    // border: 1px solid #ccc;
    // padding: 10px;
    // box-sizing: border-box;
  }
  .row-item:nth-child(1) {
    // margin-left: 0.08rem;
  }
  .row-item:active {
    // background: #1b5e63;

    background: rgba(9, 200, 252, 0.1);
    border-radius: 0.04rem;
    box-shadow: inset 0px 0px 0.1rem 0px #09c8fc;
  }
  .active {
    // background: #1b5e63;
    background: rgba(9, 200, 252, 0.1);
    border-radius: 0.04rem;
    box-shadow: inset 0px 0px 0.1rem 0px #09c8fc;
  }
  .row-item-value {
    font-size: 16px;
    // font-size: 20px;
  }
  .icon {
    font-size: 24px;
    margin-right: 10px;
  }

  // .custom-navigation {
  //   z-index: 5;
  //   position: absolute;
  //   top: 0.02rem;
  //   right: 0.05rem;
  // }
  // .custom-navigation button {
  //   background-color: transparent;
  //   border: 1px solid transparent;
  //   padding: 5px;
  //   margin-left: 0.06rem;
  //   // margin-top: -0.4rem;
  //   pointer-events: none;
  //   cursor: pointer;
  // }
  // .custom-navigation button:hover {
  //   background-color: #f1f1f1;
  // }
  :deep(.swiper) {
    position: relative;
    overflow: visible;
  }

  :deep(.swiper-button-prev) {
    // background: red;
    // border: 1px solid #374147 !important;
    // color: #374147 !important;
    // background: rgba(55, 66, 72, 0.4) !important;
    z-index: 999;
    position: absolute !important;
    width: 0.26rem;
    height: 0.26rem;
    left: 3.1rem !important;
    top: 0rem !important;
    // padding-top: 0.04rem;
    // padding: 0.06rem;
  }
  :deep(.swiper-button-next) {
    // border: 1px solid #374147;
    // background: rgba(55, 66, 72, 0.4);
    z-index: 999;
    position: absolute !important;
    width: 0.26rem;
    height: 0.26rem;
    top: 0rem !important;
    right: 0.1rem !important;
  }
  :deep(.swiper-button-prev:hover) {
    border: 1px solid #00d4ff;
    color: #00d4ff;
  }
  :deep(.swiper-button-next:hover) {
    border: 1px solid #00d4ff;
    color: #00d4ff;
  }
  :deep(.swiper-button-next:after, .swiper-rtl .swiper-button-prev:after) {
    color: #00d4ff !important;
    position: absolute !important;
    // top: -0.76rem !important;
    // right: -0.1rem !important;
    font-size: 0.14rem !important;
    z-index: 6 !important;
  }
  :deep(.swiper-button-prev:after, .swiper-rtl .swiper-button-next:after) {
    color: #00d4ff !important;
    position: absolute !important;
    // top: -0.76rem !important;
    // right: -3.2rem !important;
    font-size: 0.14rem !important;
    // z-index: 4 !important;
  }
  .left-item {
    width: 0.6rem;
    white-space: nowrap; /* 防止文字换行 */
    overflow: hidden; /* 隐藏超出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    // border: 1px solid #ccc; /* 可选：添加边框以便更好地看到效果 */
  }
</style>
