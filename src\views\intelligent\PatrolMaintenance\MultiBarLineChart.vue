<template>
  <ScaleBox :style="{ width: '410px', height: '164px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="MultiBarLineChart">
  import * as echarts from 'echarts/core'
  // import { legendColors } from '@/constants'
  const attrs = useAttrs()

  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))
  const legendColors = ['#09C8FC', '#09FCC7', '#F6CC57']

  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  /**
   *  数据格式 如
   * [
   *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
   *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
   * ]
   */
  function getConfig(data) {
    let xData = []
    let data1 = []
    let data2 = []
    let data3 = []
    xData = data[0].xData
    data1 = data[0].data1
    data2 = data[0].data2
    data3 = data[0].data3
    return {
      tooltip: {
        show: true,
        trigger: 'axis',
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '-') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = param.color //图例颜色

              htmlStr += `
                    <div style="border: 1px solid rgba(152,224,255,0.3) border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} 次
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
        // appendToBody: true,
        // confine: true,
        // // alwaysShowContent: true,
        // className: 'echart-tooltip',
        // backgroundColor: 'rgba(22,45,72,0.84)',
        // axisPointer: {
        //   type: 'shadow', // shadow cross
        //   label: {
        //     backgroundColor: 'rgba(152,224,255,0.15)',
        //   },
        // },
        // formatter: params => {
        //   let dataStr =
        //     `
        //     <div style="line-height:1.2">
        //       <span style="font-size:0.12rem;color:var(--text-chart);">${attrs.year}年${params[0].axisValue}</span>` +
        //     `
        //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[0]?.seriesName}:<span style="color:var(--primary-color)">${params[0]?.value[1]}次</span></div>` +
        //     `
        //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[1]?.seriesName}:<span style="color:var(--primary-color)">${params[1]?.value[1]}次</span></div>` +
        //     `
        //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[2]?.seriesName}:<span style="color:var(--primary-color)">${params[2]?.value[1]}次</span></div>` +
        //     `
        //     </div>
        //   `
        //   return dataStr
        // },
      },
      grid: {
        left: 10,
        right: '5',
        top: 30,
        bottom: 5,
        containLabel: true,
      },
      legend: [
        {
          data: ['日常巡检', '维修养护'], //legendData,

          selectedMode: true, // 当selectedMode为false时，tooltip不生效！！！【巨坑】
          // icon: 'path://M42,8.5 C42.8284271,8.5 43.5,9.17157288 43.5,10 C43.5,10.8284271 42.8284271,11.5 42,11.5 C35.8883895,11.5 32.0936192,12.8449819 29.7619812,15.3885869 C28.0373078,17.2700488 27.2535726,19.2375848 26.0644683,23.9296711 L25.7304159,25.2507287 C24.4779324,30.1177632 23.5588794,32.3374253 21.4494812,34.6385869 C18.4873692,37.8699819 13.8883895,39.5 7,39.5 C6.17157288,39.5 5.5,38.8284271 5.5,38 C5.5,37.1715729 6.17157288,36.5 7,36.5 C13.1116105,36.5 16.9063808,35.1550181 19.2380188,32.6114131 C20.9626922,30.7299512 21.7464274,28.7624152 22.9355317,24.0703289 L23.2695841,22.7492713 C24.5220676,17.8822368 25.4411206,15.6625747 27.5505188,13.3614131 C30.5126308,10.1300181 35.1116105,8.5 42,8.5 Z',
          textStyle: {
            color: '#fff',
            fontSize: 12,
          },
          type: 'scroll',
          orient: 'horizontal', // vertical horizontal
          // right: 280,
          left: 60,
          top: 0,
          itemWidth: 15,
          itemGap: 10,
        },
        {
          data: ['应急响应'], //legendData,

          selectedMode: true, // 当selectedMode为false时，tooltip不生效！！！【巨坑】
          // icon: 'path://M42,8.5 C42.8284271,8.5 43.5,9.17157288 43.5,10 C43.5,10.8284271 42.8284271,11.5 42,11.5 C35.8883895,11.5 32.0936192,12.8449819 29.7619812,15.3885869 C28.0373078,17.2700488 27.2535726,19.2375848 26.0644683,23.9296711 L25.7304159,25.2507287 C24.4779324,30.1177632 23.5588794,32.3374253 21.4494812,34.6385869 C18.4873692,37.8699819 13.8883895,39.5 7,39.5 C6.17157288,39.5 5.5,38.8284271 5.5,38 C5.5,37.1715729 6.17157288,36.5 7,36.5 C13.1116105,36.5 16.9063808,35.1550181 19.2380188,32.6114131 C20.9626922,30.7299512 21.7464274,28.7624152 22.9355317,24.0703289 L23.2695841,22.7492713 C24.5220676,17.8822368 25.4411206,15.6625747 27.5505188,13.3614131 C30.5126308,10.1300181 35.1116105,8.5 42,8.5 Z',
          textStyle: {
            color: '#fff',
            fontSize: 12,
          },
          type: 'scroll',
          orient: 'horizontal', // vertical horizontal
          // right: 280,
          left: 220,
          top: 0,
          itemWidth: 40,
          itemGap: 10,
        },
      ],
      xAxis: {
        type: 'category',
        axisTick: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            // color: '#54608A',
            color: 'rgba(54, 96, 103, 0.5)',
          },
        },
        axisLabel: {
          // color: '#8092AC',
          color: 'rgba(222, 242, 252, 1)',
        },
      },
      yAxis: {
        name: '单位(次)',
        minInterval: 1,
        nameTextStyle: {
          // color: '#A7D2F4',
          color: 'rgba(222, 242, 252, 1)',
          fontSize: 12,
          left: 24,
          // nameLocation: 'start'
          padding: [0, 15, 0, 20],
        },
        nameGap: '10',
        axisTick: { show: false },
        axisLine: { show: false },
        splitLine: { show: true, lineStyle: { color: 'rgba(54, 96, 103, 0.5)' } },
        // min: function (value) {
        //   let sumArr = data1.concat(data2, data3)
        //   if (Math.min(...sumArr) >= 0) {
        //     return 0
        //   }
        //   if (Math.abs(value.max) > Math.abs(value.min)) {
        //     return (-Math.abs(value.max) * 1.2).toFixed(0)
        //   } else {
        //     return (-Math.abs(value.min) * 1.2).toFixed(0)
        //   }
        // },
        max: function (value) {
          if (Math.abs(value.max) > Math.abs(value.min)) {
            return (Math.abs(value.max) * 1.2).toFixed(0)
          } else {
            return (Math.abs(value.min) * 1.2).toFixed(0)
          }
        },
        axisLabel: {
          // color: '#A7D2F4',
          color: 'rgba(105, 157, 178, 1)',
          fontSize: 12,
        },
      },
      series: [
        {
          name: '日常巡检', //legend[0],
          type: 'bar',
          barMaxWidth: 10,
          gridIndex: 0,
          color: legendColors[0],
          // backgroundStyle: {
          //   // color: '#10274B'
          //   color: 'rgba(16, 39, 75, 0.5)',
          // },
          data: data1,
        },
        {
          name: '维修养护', //legend[1],
          type: 'bar',
          barMaxWidth: 10,
          gridIndex: 0,
          color: legendColors[1],
          // backgroundStyle: {
          //   // color: '#10274B'
          //   color: 'rgba(16, 39, 75, 0.5)',
          // },
          data: data2,
        },
        {
          name: '应急响应', //legend[4], //
          type: 'line',
          // smooth: 0.4,
          gridIndex: 0,
          // yAxisIndex: 0,
          // symbol: 'circle',
          // symbolSize: 2,
          color: legendColors[2],
          // backgroundStyle: {
          //   // color: '#10274B'
          //   color: 'rgba(16, 39, 75, 0.5)',
          // },
          data: data3,
        },
      ],
    }
  }
</script>

<style lang="scss" scoped></style>
