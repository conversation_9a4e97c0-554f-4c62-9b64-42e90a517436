<template>
  <MyCard name="信息化工程设备信息总览">
    <!-- mb-0.2rem -->
    <div class="content pt-0.1rem h-4.2rem">
      <div class="item flex h-2.1rem mt-0rem">
        <div class="chart relative">
          <div
            class="absolute w-2.2rem h-2.2rem top--0.05rem left-0.05rem"
            :style="{ background: `url(${getImageUrl('intelligent/info-icon.png')}) no-repeat center / 100% 100%` }"
          >
            <MultiPieChart :dataSource="state.multiPieChartData" style="margin-top: 0.188rem; margin-left: 0.146rem" />
          </div>

          <div class="absolute w-1rem h-1rem top-0.7rem left-0.66rem text-0.1rem text-[#fff] flex flex-col items-center">
            <div class="text-0.32rem mb-0.16rem font-600">
              {{ state.total }}
              <span class="text-0.12rem">座</span>
            </div>
            <div class="w-0.8rem text-0.14rem text-center">信息化水利工程数量</div>
          </div>
        </div>
        <div class="w-1.8rem h-1.8rem ml-2.4rem mt-0.15rem flex flex-col relative">
          <div
            class="chart-item flex h-0.4rem items-center mt-0.05rem mb-0.05rem mr-0.1rem"
            v-for="(item, index) in state.multiList"
            :key="index"
          >
            <div class="w-0.1rem h-0.1rem mr-0.1rem ml-0.1rem" :style="{ background: projectColor[index] }"></div>
            <div class="text-0.14rem text-[#B2DAEA] mr-0.2rem">{{ item.name }}</div>
            <div class="text-[#fff] absolute flex right-0.14rem">
              <span class="text-0.15rem font-normal lh-normal">{{ item.value }}</span>
              <span class="text-0.14rem text-[#B2DAEA] mt-0.04rem ml-0.03rem">座</span>
            </div>
          </div>
        </div>
      </div>

      <div class="flex flex-wrap h-2rem mt-0.2rem w-[100%] ml-0.2rem">
        <div
          class="flex flex-col h-1.58rem w-1.2rem mt-0.01rem"
          :style="{ background: `url(${getImageUrl('intelligent/device-info-bg.png')}) no-repeat center / 100% 100%` }"
        >
          <div
            class="w-0.6rem h-0.6rem mt-0.13rem ml-0.36rem"
            :style="{ background: `url(${getImageUrl('intelligent/device-info-icon.png')}) no-repeat center / 100% 100%` }"
          ></div>
          <div class="mt-0.1rem w-[100%] text-center text-0.32rem text-white">
            {{ state.info?.deviceCount }}
            <span class="text-0.14rem text-white">台</span>
          </div>
          <div class="mt-0.1rem w-[100%] text-center text-0.15rem text-white">设备数量</div>
        </div>
        <div class="chart-item flex w-2.7rem h-1.58rem">
          <div class="flex flex-1 flex-col mx-0.01rem w-0.9rem h-1.3rem mt-0.14rem">
            <div class="w-0.56rem h-0.5rem ml-0.08rem px-0.06rem relative">
              <div
                class="pie-chart1 w-0.6rem h-0.6rem"
                :style="{ background: `url(${getImageUrl('intelligent/chart-info-icon.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <PieChart :dataSource="state.pieChartDataOnLine" key="1" class="absolute top--0.01rem left-0.046rem" />
            </div>
            <div class="mt-0.26rem w-[100%] text-center text-0.24rem text-[#09FCC7]">
              {{ state.info?.onLineCount }}
              <span class="text-0.14rem text-white">台</span>
            </div>
            <div class="mt-0.12rem w-[100%] text-center text-0.14rem text-white">正常总数</div>
          </div>

          <div class="flex flex-1 flex-col mx-0.01rem w-0.9rem mt-0.14rem">
            <div class="w-0.56rem h-0.5rem ml-0.08rem px-0.06rem relative">
              <div
                class="pie-chart2 w-0.6rem h-0.6rem"
                :style="{ background: `url(${getImageUrl('intelligent/chart-info-icon.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <PieChart :dataSource="state.pieChartDataOffLine" key="2" class="absolute top--0.01rem left-0.046rem" />
            </div>
            <div class="mt-0.26rem w-[100%] text-center text-0.24rem text-[#F6CC57]">
              {{ state.info?.offLineCount }}
              <span class="text-0.14rem text-white">台</span>
            </div>
            <div class="mt-0.12rem w-[100%] text-center text-0.14rem text-white">离线总数</div>
          </div>
          <div class="flex flex-1 flex-col mx-0.01rem w-0.9rem mt-0.14rem">
            <div class="w-0.56rem h-0.5rem ml-0.08rem px-0.06rem relative">
              <div
                class="pie-chart3 w-0.6rem h-0.6rem"
                :style="{ background: `url(${getImageUrl('intelligent/chart-info-icon.png')}) no-repeat center / 100% 100%` }"
              ></div>
              <PieChart :dataSource="state.pieChartDataFault" key="3" class="absolute top--0.01rem left-0.046rem" />
            </div>
            <div class="mt-0.26rem w-[100%] text-center text-0.24rem text-[#DF2F2F]">
              {{ state.info?.faultCount }}
              <span class="text-0.14rem text-white">台</span>
            </div>
            <div class="mt-0.12rem w-[100%] text-center text-0.14rem text-white">故障总数</div>
          </div>
        </div>
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="DeviceInfo">
  import { getDeviceCount, getProjectChart } from '../services'
  import { thjDrainageStatuses, serviceColor, projectColor } from '@/constants'
  import MultiPieChart from './MultiPieChart.vue'
  import PieChart from './PieChart.vue'
  import { dealNumber } from '@/utils'

  const state = reactive({
    info: {},
    pieChartData: [],
    multiList: [],
    multiPieChartData: [],
    total: 0,
    pieChartDataOnLine: [],
    pieChartDataOffLine: [],
    pieChartDataFault: [],
    faultPercent: 0,
    onLinePercent: 0,
    offLinePercent: 0,
  })
  const parseData = val => {
    let temp = val * 100
    temp = dealNumber(temp, 1)
    return temp
  }

  // const handleRainClick = (item, key) => {
  //   currentNum.value = key
  // }

  onMounted(() => {
    getList()
  })
  const getList = () => {
    getDeviceCount().then(res => {
      state.info = res?.data || {}
      state.faultPercent = parseData(state.info?.faultCount / state.info?.deviceCount)
      state.onLinePercent = parseData(state.info?.onLineCount / state.info?.deviceCount)
      state.offLinePercent = parseData(state.info?.offLineCount / state.info?.deviceCount)

      state.pieChartDataOnLine = [
        { value: 100 - state.onLinePercent },
        { value: state.onLinePercent, name: '正常', itemStyle: { color: serviceColor[0] } },
      ]
      state.pieChartDataOffLine = [
        { value: 100 - state.offLinePercent },
        { value: state.offLinePercent, name: '离线', itemStyle: { color: serviceColor[1] } },
      ]
      state.pieChartDataFault = [
        { value: 100 - state.faultPercent },
        { value: state.faultPercent, name: '故障', itemStyle: { color: serviceColor[2] } },
      ]
    })
    getProjectChart().then(project => {
      state.multiList = Object.entries(project.data).map(([keyValue, value]) => {
        const [key, name] = keyValue.split(',')
        return { key: parseInt(key), name, value }
      })

      state.total = state.multiList.reduce((sum, item) => sum + item.value, 0)
      state.multiPieChartData = state.multiList.map((item, index) => ({
        name: item.name,
        value: item.value,
        itemStyle: { color: projectColor[index] },
      }))
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  :deep(.slick-slide) {
    height: 1.6rem;
    overflow: hidden;
    color: #ffffff;
  }
  .info-item {
    border-radius: 0.04rem;
    background: linear-gradient(94deg, rgba(63, 94, 144, 0.32) 12%, rgba(64, 88, 124, 0.25) 86%);
    box-shadow: inset 0px -1px 0.02rem 0px rgba(255, 255, 255, 0.2);
  }
  .chart-item {
    background: rgba(31, 115, 121, 0.3);
    border: 1px solid;
    border-image: radial-gradient(4% 33% at 3% 100%, #00d4ff 0%, rgba(90, 228, 255, 0) 100%) 1;
  }
  .pie-chart1 {
    animation: rotate 14s linear infinite;
  }
  .pie-chart2 {
    animation: rotate 20s linear infinite;
  }
  .pie-chart3 {
    animation: rotate 16s linear infinite;
  }
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
</style>
