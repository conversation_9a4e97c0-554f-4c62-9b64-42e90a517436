<template>
  <MyCard name="水库来水预报">
    <div class="content pt-0.1rem h-4.7rem flex flex-col relative">
      <div class="w-4.1rem h-1.05rem mt-0.05rem ml-0.1rem flex">
        <hgroup class="w-1.17rem h-1.05rem text-center">
          <h3 class="w-0.78rem h-0.78rem m-auto uno-bg_defense/forecast-rain-sum.png"></h3>
          <div class="text-0.14rem font-350 c-#B2DAEA my-0.05rem">预报累计降雨</div>
          <h2 class="text-0.2rem c-#fff mt-0.03rem mb-0.05rem">
            {{ state.forecastRes?.rainSum }}
            <span class="text-0.14rem font-400 c-#699DB2">mm</span>
          </h2>
        </hgroup>
        <hgroup class="w-1.17rem h-1.05rem text-center ml-0.1rem mr-0.1rem">
          <h3 class="w-0.78rem h-0.78rem m-auto uno-bg_defense/forecast-water-sum.png"></h3>
          <h6 class="text-0.14rem font-350 c-#B2DAEA my-0.05rem">预计总来水量</h6>
          <h2 class="text-0.2rem c-#fff mt-0.03rem mb-0.05rem">
            {{ state.forecastRes?.inWaterSum }}
            <span class="text-0.14rem font-400 c-#699DB2">万m³</span>
          </h2>
        </hgroup>
        <hgroup class="w-1.17rem h-1.05rem text-center">
          <h3 class="w-0.78rem h-0.78rem m-auto uno-bg_defense/forecast-water-max.png"></h3>
          <h6 class="text-0.14rem font-350 c-#B2DAEA my-0.05rem">预报最高水位</h6>
          <h2 class="text-0.2rem c-#fff mt-0.03rem mb-0.05rem">
            {{ state.forecastRes?.wlvMax === null ? '--' : state.forecastRes?.wlvMax }}
            <span class="text-0.14rem font-400 c-#699DB2">m</span>
          </h2>
        </hgroup>
      </div>
      <div class="w-4.2rem h-3.3rem mt-0.35rem mb-0.06rem ml-0.02rem">
        <ForecastChart
          v-show="!!state.forecastResult"
          :dataSource="state.forecastResult"
          :markLineXAxis="state.forecastRes?.reals?.length - 1"
          :markPointsData="markPointsData"
        />
      </div>
    </div>
  </MyCard>
</template>
<script setup lang="tsx" name="WaterForecast">
  import ForecastChart from './ForecastChart.vue'
  // import { apiRes } from './api'
  import { maxBy } from 'lodash-es'
  import { queryAutoForecast } from '../services'
  import dayjs from 'dayjs'
  const state = reactive({
    areaTab: 1,
    autoForecast: {
      fcstRange: 1,
      startTime: dayjs().format('YYYY-MM-DD HH:00'),
      endTime: dayjs().add(72, 'hour').format('YYYY-MM-DD HH:00'),
    },
    // forecastRes: apiRes,
    forecastRes: null,
    forecastResult: [],
  })
  const markPointsData = computed(() => {
    const predFlow = maxBy(state.forecastRes?.fcsts, 'wlv')
    const predFlowMax = {
      x: state.forecastRes?.fcsts.findIndex(el => (predFlow ? predFlow.tm === el.tm : false)) + state.forecastRes?.reals.length,
      y: predFlow?.inflow,
    }

    const predWater = maxBy(state.forecastRes?.fcsts, 'wlv')

    const predWaterMax = {
      x: state.forecastRes?.fcsts.findIndex(el => (predWater ? predWater.tm === el.tm : false)) + state.forecastRes?.reals.length,
      y: predWater?.wlv,
    }

    return {
      water: [{ name: '预测', value: predWaterMax.y, xAxis: predWaterMax.x, yAxis: predWaterMax.y }],
      flow: [{ value: predFlowMax.y, xAxis: predFlowMax.x, yAxis: predFlowMax.y }],
    }
  })

  onMounted(() => {
    setTimeout(() => {
      getForecast()
    }, 600)
  })

  const getForecast = () => {
    queryAutoForecast({ ...state.autoForecast }).then(res => {
      state.forecastRes = res.data
      const apiRes = res.data
      const rainData = {
        name: '降雨量',
        data: apiRes?.reals.map(el => [el.tm, el.rain]).concat(apiRes?.fcsts.map(el => [el.tm, el.rain])),
      }
      const sumRainData = {
        name: '累计雨量',
        data: rainData.data.map((el, idx) => {
          const sum = rainData.data.slice(0, idx + 1).reduce((a, b) => a + b[1], 0)
          return [el[0], +sum.toFixed(1)]
        }),
      }

      state.forecastResult = [
        rainData,
        sumRainData,
        {
          name: '水位',
          data: apiRes?.reals.map(el => [el.tm, el.wlv]).concat(apiRes?.fcsts.map(el => [el.tm, el.wlv])),
        },
        {
          name: '预报来水流量',
          data: apiRes?.reals.map(el => [el.tm, el.inflow]).concat(apiRes?.fcsts.map(el => [el.tm, el.inflow])),
        },
      ]
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  .tabs {
    background: rgba(9, 36, 63, 0.7);
    box-shadow: inset 0px 0px 23px 0px rgba(0, 131, 230, 0.5);
    border-radius: 0.04rem;
    border: 1px solid rgba(152, 224, 255, 0.2);
  }
</style>
