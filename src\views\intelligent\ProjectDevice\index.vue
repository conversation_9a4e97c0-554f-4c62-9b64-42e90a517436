<template>
  <MyCard name="工程运行状态">
    <!-- bg-[rgba(0,0,0,.2)] 'horizontal'（水平）或 'vertical'（垂直）-->
    <div class="content pt-0.05rem h-4.5rem flex flex-col">
      <div class="mt-0.05rem h-0.2rem text-[#fff] text-0.14rem flex">
        <div class="w-0.9rem ml-0.1rem mt-0.1rem">关键词搜索：</div>
        <div class="flex-1 mr-0.1rem">
          <n-input
            class="w-2rem mt-0.02rem text-[#fff] border-rd-0.06rem"
            v-model:value="state.keywords"
            @change="getList"
            @clear="getList"
            placeholder="请输入工程名称/工程类型"
            clearable
          >
            <template #suffix>
              <MyIcon name="search" class="text-0.18rem" />
            </template>
          </n-input>
        </div>
      </div>
      <!-- h-4rem -->
      <div class="mt-0.05rem h-4rem px-0.12rem">
        <SwiperList v-if="dataSource.length" :key="tableKey" :dataSource="dataSource" :columns="columns" class="mt-0.25rem" />
        <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
      </div>
    </div>
  </MyCard>
</template>

<script setup lang="tsx" name="ProjectDevice">
  import { waterSourceTypes } from '@/constants'
  import { getProjectDeviceList } from '../services'

  import { Swiper, SwiperSlide } from 'swiper/vue'

  import { Navigation, Pagination, A11y, Autoplay } from 'swiper/modules'
  import 'swiper/css'
  import 'swiper/css/navigation'
  import 'swiper/css/pagination'
  import 'swiper/css/autoplay'
  import { getOptions } from '@/api'

  const dataSource = ref([])
  // <img :src="iconSrc" :alt="" :class="iconClass" />

  // const renderIcon = (isOpen: number) => {
  //   return isOpen == 1 ? (
  //     <div class="['item-icon','pump-open']"></div>
  //   ) : (
  //     <div class='item-icon'>
  //       <img src='@/assets/images/intelligent/pump-close.png' alt='' />
  //     </div>
  //   )
  // }

  const renderIcon = (isOpen: number) => {
    // return isOpen == 1
    //   ? `url(${getImageUrl('intelligent/pump-close.png')}) no-repeat center / 100% 100%`
    //   : '@/assets/images/intelligent/pump-open.png'
    // return `url(${getImageUrl('intelligent/pump-close.png')}) no-repeat center / 100% 100%`
    return '@/assets/images/intelligent/pump-open.png'
  }

  const renderDeviceList = (devices: Device[] | null) => {
    if (!devices || devices.length === 0) {
      return '无设备'
    }
    return devices.map(device => (
      <div class='flex flex-wrap' key={device.deviceId}>
        {device.deviceCategoryCode == 'PUMP' && device.faultStatus != 0 ? (
          <img src={getImageUrl('intelligent/pump-fault.png')} alt={device.deviceName} class='w-0.15rem h-0.15rem mt-0.05rem' />
        ) : device.deviceCategoryCode == 'PUMP' && device.faultStatus == 0 && device.isOnline == 1 ? (
          <img src={getImageUrl('intelligent/pump-open.png')} alt={device.deviceName} class='w-0.15rem h-0.15rem mt-0.05rem' />
        ) : device.deviceCategoryCode == 'PUMP' && device.faultStatus == 0 && device.isOnline == 0 ? (
          <img src={getImageUrl('intelligent/pump-close.png')} alt={device.deviceName} class='w-0.15rem h-0.15rem mt-0.05rem' />
        ) : device.deviceCategoryCode == 'GATE' && device.faultStatus != 0 ? (
          <img src={getImageUrl('intelligent/gate-fault.png')} alt={device.deviceName} class='w-0.15rem h-0.15rem mt-0.05rem' />
        ) : device.deviceCategoryCode == 'GATE' && device.faultStatus == 0 && device.isOnline == 1 ? (
          <img src={getImageUrl('intelligent/gate-open.png')} alt={device.deviceName} class='w-0.15rem h-0.15rem mt-0.05rem' />
        ) : device.deviceCategoryCode == 'GATE' && device.faultStatus == 0 && device.isOnline == 0 ? (
          <img src={getImageUrl('intelligent/gate-close.png')} alt={device.deviceName} class='w-0.15rem h-0.15rem mt-0.05rem' />
        ) : (
          ''
        )}
        <span
          class={devices.length > 4 ? 'text-overflow1 w-0.3rem pt-0.05rem mr-0.04rem' : 'pt-0.05rem  mr-0.04rem'}
          title={device.deviceName}
        >
          {device.deviceName}
        </span>
      </div>
    ))
  }
  // @/assets/images/intelligent/pump-open.png

  const columns = [
    {
      title: '工程名称',
      width: '0.8rem',
      align: 'left',
      render: (row, idx) => <n-ellipsis>{row.projectName}</n-ellipsis>,
    },
    {
      title: '工程类型',
      width: '0.8rem',
      render: (row, idx) => (
        // <NSpace>{row.projectType == 1 ? <NTag type='success'>分水闸</NTag> : <NTag type='info'>排水闸</NTag>}</NSpace>
        // <NSpace>{row.projectType == 1 ? '分水闸' : '排水闸'}</NSpace>
        <NSpace>{state.projectTypeOptions?.find(item => item.key == row.projectType)?.value}</NSpace>
        // this.projectTypeOptions?.find(item => item.key == row.projectType)?.value
      ),
      // return row.isOpen === 1 ? <NTag type="success">开启</NTag> : <NTag type="error">关闭</NTag>
    },
    {
      title: '工程设备',
      width: '2.5rem',
      render: (row, idx) => <div class='flex '>{renderDeviceList(row.devices)}</div>,
    },
  ]
  const tableKey = ref(0)
  const state = $ref({
    keywords: '',
    projectTypeOptions: [],
  })
  const directionValue = ref('vertical')
  onMounted(() => {
    getOptions('automateType').then(res => {
      state.projectTypeOptions = res.data
      console.log('******** 130', res)
      //.map(el => ({ label: el.value, value: el.key }))
    })
    getList()
  })
  // dataSource.value = [
  //   { districtCodeName: '测试111301', serialNumber: '111', dateTimes: '2021-02-03', type: 1 },
  //   { name: 'ly', mobile: '222', dateTimes: '2021-02-03', type: 2 },
  //   { name: 'dzt', mobile: '333', dateTimes: '2021-02-03', type: 1 },
  //   { name: 'tzm', mobile: '444', dateTimes: '2021-02-03', type: 1 },
  // ]
  const handleChange = e => {
    // console.log(e)
  }
  const getList = () => {
    let param = {
      keywords: state.keywords,
    }
    getProjectDeviceList(param).then(res => {
      dataSource.value = res?.data || []
    })
  }
</script>
<style lang="scss" scoped>
  .content {
    opacity: 1;

    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);
    // background: red;
    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
    z-index: 999;
  }
  :deep(.n-input) {
    font-size: 0.14rem;
    background: rgba(52, 72, 82, 0.4);

    .n-input-wrapper {
      background: transparent;
      //background: rgba(52, 72, 82, 0.8);
      // color: #f2f3f5;
      padding: 0 0.12rem;
      .n-input__input-el {
        height: 0.32rem;
        line-height: 1.1;
        color: #f2f3f5;
      }
      :focus {
        // color: rgba(52, 72, 82, 0.8);
        color: #ffffff;
        // background: rgba(52, 72, 82, 0.8) !important;
        // background: rgba(52, 72, 82, 0.4) !important;
      }
    }
    .n-input__content {
      background: rgba(52, 72, 82, 0.8) !important;
    }
    .n-input__border {
      // border-color: transparent;
      border-color: rgba(52, 72, 82, 0.6);
    }
    // .n-input .n-input--resizable .n-input-focus .n-input--stateful {
    //   color: black;
    // }
  }
  :deep(.slick-slide) {
    height: 1.6rem;
    overflow: hidden;
    color: #ffffff;
  }
  .info-item {
    border-radius: 0.04rem;
    background: linear-gradient(94deg, rgba(63, 94, 144, 0.32) 12%, rgba(64, 88, 124, 0.25) 86%);

    box-shadow: inset 0px -1px 0.02rem 0px rgba(255, 255, 255, 0.2);
  }
  .swiper-container {
    cursor: pointer;
    // display: flex;
    // flex-direction: column;
  }
  .item-icon {
    width: 0.12rem;
    height: 0.12rem;
    margin-right: 0.04rem;
  }

  // .info-item-title {
  //   font-size: 0.14rem;
  //   font-family: PingFangSC-Medium, PingFang SC;
  //   font-weight: 500;
  //   color: #ffffff;
  //   line-height: 0.2rem;
  // }
</style>
