<template>
  <ScaleBox :style="{ width: '420px', height: '300px' }">
    <div ref="domRef" class="w-full h-full"></div>
  </ScaleBox>
</template>

<script setup lang="ts" name="ForecastChart">
  import * as echarts from 'echarts/core'
  import dayjs from 'dayjs'
  const attrs = useAttrs()
  const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))
  const colors = ['#507EF7', '#74C3F8', '#EF8432', '#0FC6C2']
  watch(
    () => attrs.dataSource,
    newVal => {
      updateOptions(opt => {
        return getConfig(newVal || [])
      })
    },
  )

  function getConfig(list) {
    const serArr = []
    list = list.map(item => {
      return {
        ...item,
        data: item.data?.map(([time, value]) => {
          const formattedTime = dayjs(time).format('MM-DD HH')
          return [formattedTime, value]
        }),
      }
    })
    console.log('chart *** 25', list)
    list.forEach((item, index) => {
      if (item.name === '降雨量') {
        serArr.push({
          name: '降雨量',
          color: colors[0],
          type: 'bar',
          xAxisIndex: 0,
          yAxisIndex: 0,
          barMaxWidth: 10,
          showBackground: false,
          hoverAnimation: true, // 悬浮的动画加上
          markLine: {
            lineStyle: {
              width: 2,
              color: '#DF2F2F',
            },
            label: {
              show: true,
              position: 'start',
              formatter: '预报依据时间',
              color: '#DF2F2F',
              height: 10,
              padding: [12, 12, -4, 12],
            },
            silent: true, // 鼠标悬停事件, true悬停不会出现实线
            symbol: 'none', // 去掉箭头
            data: [{ xAxis: attrs.markLineXAxis }],
            // data: [{ xAxis: 6 }],
          },
          data: item.data,
        })
      }

      if (item.name === '累计雨量') {
        serArr.push({
          name: '累计雨量',
          color: colors[1],
          type: 'line',
          xAxisIndex: 0,
          yAxisIndex: 1,
          smooth: false,
          // symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          data: item.data,
        })
      }

      if (item.name === '水位') {
        serArr.push({
          name: '水位',
          color: colors[2],
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 2,
          smooth: false,
          // symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          markPoint: {
            symbolRotate: 180,
            symbolSize: 60,
            label: {
              offset: [0, 13],
            },
            data: attrs.markPointsData?.water,
          },
          markLine: {
            lineStyle: {
              width: 2,
              color: 'red',
            },
            label: {
              show: false,
            },
            silent: true, // 鼠标悬停事件, true悬停不会出现实线
            symbol: 'none', // 去掉箭头
            data: [{ xAxis: attrs.markLineXAxis }],
            // data: [{ xAxis: 6 }],
          },
          data: item.data,
        })
      }

      if (item.name === '预报来水流量') {
        serArr.push({
          name: '预报来水流量',
          color: colors[3],
          type: 'line',
          xAxisIndex: 1,
          yAxisIndex: 3,
          smooth: false, //true平滑
          // symbol: 'none',
          showSymbol: true,
          symbolSize: 2,
          symbol: 'circle',
          hoverAnimation: true, // 悬浮的动画加上
          lineStyle: { width: 3 },
          markPoint: {
            symbolSize: 60,
            data: attrs.markPointsData?.flow,
          },
          data: item.data,
        })
      }
    })

    return {
      grid: [
        // 配置第一个柱状图的位置
        {
          left: '13%',
          right: '13%',
          top: '11%',
          height: '38%',
        },
        // 配置第二个折线图位置
        {
          left: '13%',
          right: '13%',
          top: '55%',
          height: '38%',
        },
      ],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
            height: 10,
          },
          crossStyle: { color: '#1664FF' },
          lineStyle: { color: '#1664FF' },
        },

        // formatter函数动态修改tooltip样式
        formatter: function (params) {
          if (params) {
            let htmlStr = ''
            htmlStr += params[0].name.replace(/\-/g, '-') + '<br/>' //x轴的名称
            for (var i = 0; i < params.length; i++) {
              let param = params[i] // 存一份item项
              let seriesName = param.seriesName //图例名称
              let value = param.value[1] === null ? '-' : param.value[1] //y轴值
              let color = param.color //图例颜色

              function getUnit(seriesName) {
                switch (seriesName) {
                  case '降雨量':
                    return 'mm'
                  case '累计雨量':
                    return 'mm'
                  case '水位':
                    return 'm'
                  case '预报来水流量':
                    return 'm³/s'
                  default:
                    return
                }
              }
              //
              htmlStr += `
                    <div style="border: 1px solid rgba(152,224,255,0.3) border-radius:4px 4px 4px 4px; padding:5px 8px; margin-top:4px; display:flex; align-items:center; justify-content:space-between">
                      <span style="margin-right:30px">
                        <span style="margin-right:5px;display:inline-block;width:10px;height:10px;border-radius:5px;background-color:${color}"></span>
                        ${seriesName}
                      </span>
                      <span>
                        ${value} ${getUnit(seriesName)}
                      </span>
                    </div>
                    `
            }
            return htmlStr
          } else {
            return
          }
        },
        backgroundColor: 'rgba(86, 103, 128, 0.58)',
        borderWidth: 0,
        borderColor: 'rgba(152,224,255,0.3)',
        padding: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
          fontWeight: 500,
          align: 'left',
        },
      },
      legend: {
        show: true,
        x: 'center',
        y: '-2',
        data: ['降雨量', '累计雨量', '水位', '预报来水流量'],

        textStyle: {
          // color: '#8092AC',
          color: 'rgba(222, 242, 252, 1)',
          fontSize: 12,
        },
        itemWidth: 12,
        itemHeight: 10,
      },
      // 将上下两个tootip合成一个
      axisPointer: {
        link: { xAxisIndex: 'all' },
      },
      xAxis: [
        {
          type: 'category',
          position: 'top',
          scale: true,
          axisLabel: {
            show: false,
          },
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: false,
            color: 'rgba(222, 242, 252, 1)',
          },
          splitLine: {
            show: false,
            lineStyle: {
              // color: '#BBB',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
        },
        {
          gridIndex: 1,
          type: 'category',
          scale: true,
          axisTick: {
            alignWithLabel: true,
            show: false,
          },
          axisLine: {
            show: false,
            color: 'rgba(222, 242, 252, 1)',
          },
          axisLabel: {
            // color: '#DEF2FC',
            color: 'rgba(222, 242, 252, 1)',
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '降雨量(mm)',
          nameLocation: 'middle',
          nameGap: 35,
          // nameGap: 45,
          nameRotate: 270,
          inverse: true,
          nameTextStyle: {
            // color: '#DEF2FC',
            color: 'rgba(222, 242, 252, 1)',
            fontSize: 12,
            // padding: [0, 0, 0, -100], // 上右下左与原位置距离
          },
          axisLabel: {
            fontSize: 12,
            // color: '#699DB2',
            color: 'rgba(105, 157, 178, 1)',
          },
          // splitLine: {
          //   show: false,
          //   lineStyle: {
          //     color: '#000',
          //   },
          // },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              // type: 'dashed',
              // color: 'rgba(255, 255, 255, 0.10)',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          alignTicks: true, // 配置多坐标轴标签对齐
        },
        {
          type: 'value',
          name: '累计雨量(mm)',
          inverse: true,
          nameLocation: 'middle',
          nameGap: 35,
          nameRotate: 270,
          nameTextStyle: {
            // color: '#DEF2FC',
            color: 'rgba(222, 242, 252, 1)',
            // color: 'rgba(105, 157, 178, 1)',
            // padding: [-140, 0, 0, 0], // 上右下左与原位置距离
            fontSize: 12,
          },
          axisLabel: {
            fontSize: 12,
            // color: '#699DB2',
            color: 'rgba(105, 157, 178, 1)',
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              // color: 'rgba(255, 255, 255, 0.10)',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          alignTicks: true, // 配置多坐标轴标签对齐
        },
        {
          type: 'value',
          name: '水位(m)',
          nameLocation: 'middle',
          nameGap: 35,
          nameRotate: 270,
          nameTextStyle: {
            // color: '#DEF2FC',
            color: 'rgba(222, 242, 252, 1)',
            // color: 'rgba(105, 157, 178, 1)',
            // padding: [0, 0, 0, -115], // 上右下左与原位置距离
            fontSize: 12,
          },
          gridIndex: 1,
          axisLabel: {
            fontSize: 12,
            // color: '#699DB2',
            color: 'rgba(105, 157, 178, 1)',
          },
          scale: true,
          splitLine: {
            // show: false,
            show: true,
            lineStyle: {
              // color: 'rgba(255, 255, 255, 0.10)',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          alignTicks: true, // 配置多坐标轴标签对齐
        },
        {
          type: 'value',
          name: '预报来水流量(m³/s)',
          nameLocation: 'middle',
          nameGap: 35,
          nameRotate: 270,
          nameTextStyle: {
            // color: '#DEF2FC',
            color: 'rgba(222, 242, 252, 1)',
            // color: 'rgba(105, 157, 178, 1)',
            // padding: [0, 0, 0, -100], // 上右下左与原位置距离
            fontSize: 12,
          },
          gridIndex: 1,
          axisLabel: {
            fontSize: 12,
            // color: '#699DB2',
            color: 'rgba(105, 157, 178, 1)',
          },
          scale: true,
          splitLine: {
            show: true,
            lineStyle: {
              // type: 'dashed',
              // color: 'rgba(255, 255, 255, 0.10)',
              color: 'rgba(54, 96, 103, 0.5)',
            },
          },
          min: 0,
          max: function (value) {
            let val = !value.max || (value.max * 1.1).toFixed(0) == 0 ? 3 : (value.max * 1.1).toFixed(0) // 假设留出10%的空间

            return val
          },
          alignTicks: true, // 配置多坐标轴标签对齐
        },
      ],
      dataZoom: [
        {
          show: false,
          type: 'inside',
          xAxisIndex: [0, 1], // 显示 0 1 的数据，这个要加，不加的话，悬浮提示就会出问题
        },
      ],

      series: serArr,
    }
  }
</script>

<style lang="scss" scoped></style>
