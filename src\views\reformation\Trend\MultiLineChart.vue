<template>
  <ScaleBox :style="{ width: '398px', height: '194px' ,  }">
    <div ref="domRef" class="w-full h-full "></div>
  </ScaleBox>
</template>

<script setup lang="tsx" name="MultiLineChart">
import * as echarts from 'echarts/core'
import { projectColor } from '@/constants'
const attrs = useAttrs()

const { domRef, updateOptions, chartIns } = useEcharts(() => ({}))

watch(
  () => attrs.dataSource,
  newVal => {
    console.log('newVal', newVal)
    updateOptions(opt => {
      return getConfig(newVal || [])
    })
  },
)


/**
 *  数据格式 如
 * [
 *   {name: 'xxxx', data: [['20220202', 7], ['20220203', 8]]},
 *   {name: 'yyyy', data: [['20220302', 5], ['20220303', 6]]}
 * ]
 */
function getConfig(data) {
  let seriesData = [
    {
      name: data.name,
      type: 'line',
      gridIndex: 0,
      color: projectColor[0],
      backgroundStyle: {
        // color: '#10274B'
        color: 'rgba(16, 39, 75, 0.5)',
      },
      lineStyle: {
        width: 1,
      },
      data: data.listData,
      symbol: 'none', // 去除线上的点
      smooth: 1
    }
  ]

  let options = {
    tooltip: {
      show: true,
      trigger: 'axis',
      // appendToBody: true,
      // confine: true,
      // // alwaysShowContent: true,
      // className: 'echart-tooltip',
      backgroundColor: 'rgba(22,45,72,0.84)',
      // axisPointer: {
      //   type: 'shadow', // shadow cross
      //   label: {
      //     backgroundColor: 'rgba(152,224,255,0.15)',
      //   },
      // },
      // formatter: params => {
      //   let dataStr =
      //     `
      //     <div style="line-height:1.2">
      //       <span style="font-size:0.12rem;color:var(--text-chart);">${attrs.year}年${params[0].axisValue}</span>` +
      //     `
      //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[0]?.seriesName}:<span style="color:var(--primary-color)">${params[0]?.value[1]}次</span></div>` +
      //     `
      //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[1]?.seriesName}:<span style="color:var(--primary-color)">${params[1]?.value[1]}次</span></div>` +
      //     `
      //       <div style="font-size:0.14rem;margin-top:0.05rem">${params[2]?.seriesName}:<span style="color:var(--primary-color)">${params[2]?.value[1]}次</span></div>` +
      //     `
      //     </div>
      //   `
      //   return dataStr
      // },
    },
    grid: { //
      left: 0,
      right: 0,
      top: 50,
      bottom: 0,
      containLabel: true,
    },
    legend:
    {
      data: seriesData.map(item => item.name), //legendData,
      selectedMode: true, // 当selectedMode为false时，tooltip不生效！！！【巨坑】
      // icon: 'path://M42,8.5 C42.8284271,8.5 43.5,9.17157288 43.5,10 C43.5,10.8284271 42.8284271,11.5 42,11.5 C35.8883895,11.5 32.0936192,12.8449819 29.7619812,15.3885869 C28.0373078,17.2700488 27.2535726,19.2375848 26.0644683,23.9296711 L25.7304159,25.2507287 C24.4779324,30.1177632 23.5588794,32.3374253 21.4494812,34.6385869 C18.4873692,37.8699819 13.8883895,39.5 7,39.5 C6.17157288,39.5 5.5,38.8284271 5.5,38 C5.5,37.1715729 6.17157288,36.5 7,36.5 C13.1116105,36.5 16.9063808,35.1550181 19.2380188,32.6114131 C20.9626922,30.7299512 21.7464274,28.7624152 22.9355317,24.0703289 L23.2695841,22.7492713 C24.5220676,17.8822368 25.4411206,15.6625747 27.5505188,13.3614131 C30.5126308,10.1300181 35.1116105,8.5 42,8.5 Z',
      textStyle: {
        color: '#fff',
        fontSize: '12px',
        fontWeight: 350,
      },
      type: 'scroll',
      // icon:"line",
      orient: 'horizontal', // vertical horizontal
      // right: 280,
      left: 0,
      top: -3,
      itemHeight: 1,
      itemWidth: 14,
      itemGap: 12,
    },

    xAxis: {
      type: 'category',
      data: data.labelData, //xAxisData,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#FFF',
        },
      },
      axisTick: { 
        show: true,
        inside: false 
       },
      axisLabel: {
        color: '#8092AC',
        interval: 0
      },
    },
    yAxis: {
      name: '水量:万m³', //yAxisName,
      minInterval: 1,
      nameTextStyle: {
        color: '#DEF2FC',
        fontSize: 12,
        fontWeight: 350,
        left: 24,
        // nameLocation: 'start'
      },
      nameGap: '10',
      axisTick: { show: false },
      axisLine: { show: false },
      splitLine: { show: true },
      // min: function (value) {
      //   let sumArr = data1.concat(data2, data3)
      //   if (Math.min(...sumArr) >= 0) {
      //     return 0
      //   }
      //   if (Math.abs(value.max) > Math.abs(value.min)) {
      //     return (-Math.abs(value.max) * 1.2).toFixed(0)
      //   } else {
      //     return (-Math.abs(value.min) * 1.2).toFixed(0)
      //   }
      // },
      max: function (value) {
        if (Math.abs(value.max) > Math.abs(value.min)) {
          return (Math.abs(value.max) * 1).toFixed(0)
        } else {
          return (Math.abs(value.min) * 1).toFixed(0)
        }
      },
      axisLabel: {
        color: '#699DB2',
        fontSize: 12,
      },
    },
    series: seriesData
  }
  return options
}
</script>

<style lang="scss" scoped></style>
