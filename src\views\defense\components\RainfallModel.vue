<template>
  <ThjModal ref="$modal" :showFooter="false">
    <div class="w-96% h-96% ml-2% mr-2% mt-1% mb-3% flex relative border-radius-0.16rem">
      <div class="legend">
        <div style="margin-bottom: 0.05rem; font-size: 0.14rem; font-weight: bold">
          图例
          <span style="font-weight: normal; font-size: 0.12rem; margin-left: 0.05rem">单位：mm</span>
        </div>
        <span class="legend-item" v-for="(el, i) in state.rainLevelOptions" :key="i">
          <div
            style="width: 0.18rem; height: 0.1rem; border-radius: 0.02rem; margin-right: 0.1rem; margin-top: 0.05rem"
            :style="{ background: el.color }"
          ></div>
          {{ el.label }}
        </span>
      </div>

      <div class="absolute w-5.8rem h-[42%] ml-0.2rem bottom-0.9rem flex">
        <MyCard class="w-[100%]" name="降雨趋势">
          <!--  -->
          <div class="w-2.8rem h-0.2rem absolute right-0.16rem top-0.06rem flex">
            <ScaleBox :style="{ width: '280px', height: '20px', zIndex: 999 }">
              <NaDatePicker
                v-model:formatted-value="range"
                type="datetimerange"
                :format="'yyyy-MM-dd HH'"
                input-readonly
                :to="false"
                size="small"
                @update:formatted-value="getList()"
              />
            </ScaleBox>
          </div>
          <div class="absolute today-rainfall w-1.9rem h-0.25rem right-0.16rem top-0.46rem flex text-[#def2fc]">
            <div
              class="h-0.2rem w-0.2rem mx-0.06rem"
              :style="{ background: `url(${getImageUrl(`defense/today-rainfall.png`)}) no-repeat center / 100% 100%` }"
            ></div>
            时段累计降雨量
            <span class="text-[#ffffff] text-0.15rem mx-0.06rem">{{ twoDecimalFilter(state.rainSum, 2) }}</span>
            mm
          </div>
          <div class="charts w-[100%] h-[100%]">
            <LineChart
              v-if="state.lineChartData"
              :dataSource="state.lineChartData"
              :year="year"
              class="mt-0.3rem ml-0.14rem w-[90%] h-[60%]"
            />
            <MyEmpty v-else :src="getImageUrl('empty.png')" description="暂无数据" />
          </div>
        </MyCard>
      </div>
      <!-- <ScaleBox :style="{ width: '368', zIndex: 999, background: 'red', bottom: '20px' }"></ScaleBox> -->
      <div class="w-6rem h-0.34rem absolute bottom-0.34rem left-0.2rem flex z-3">
        <TimeSlider
          class="w-5.8rem h-[100%]"
          v-if="state.times?.length"
          :times="state.times"
          :resetTime="state.resetTime"
          @onTimeChange="onTimeChange"
        />
      </div>

      <!-- <Mapbox :onMapMounted="onMapMounted" class="w-[100%] h-[100%] border-radius-0.06rem" /> -->
      <Mapbox class="w-[100%] h-[100%]" :onMapMounted="onMapMounted" :mapZoom="state.mapZoom" />
          <MultiPolygon v-if="state.mapIns" :geojson="state.currentGeo" :mapIns="state.mapIns" />
    </div>
  </ThjModal>
</template>
<script setup lang="ts" name="RainfallModel">
  import { getDispatchList, futureRainList, futureRainConvert,getContourSurface } from '../services'
  const [$modal, okLoading, loading, title] = useModal()
  import { getOptions, getValueByKey } from '@/api'
  // import initMap from './initMap.js'
  import TimeSlider from '@/components/TimeSlider/index.vue'
  import LineChart from './LineChart.vue'
  import dayjs from 'dayjs'
  import initMap from './initMap.js'
  import MultiPolygon from './MultiPolygon.vue'
  import axios from 'axios'

  const attrs = useAttrs()
  const state = reactive({
    geojsonList: null,
    currentGeo: null,
    zoom: 9,
    mapZoom: 9,
    mapIns: null,

    list: [],
    total: 0,
    pageNum: 1,
    resetTime: 0,
    times: [],
    layers: {
      MultiPolygon: [],
      MultiLineString: [],
      Point: [],
    },
    rainLevelOptions: [],
    lineChartData: [],
    rainList: [],
    newRange: [],
    rainSum: 0,
  })
  const openModal = async () => {
    getList()

    $modal.value?.open({
      loading: false,
      contentStyle: { width: '18rem', height: '9rem' },
      headerStyle: { display: 'none' },
      title: '降雨分布',
      onClose: () => {
        // 此处重置状态
        initTime()
        attrs.onClose && attrs.onClose()
      },
    })
  }
  defineExpose({ openModal })

  onMounted(() => {
    initTime()
    getList()
  })

  const onMapMounted = mapIns => {
    state.mapIns = mapIns
    // initMap(state.mapIns)
    nextTick(() => {
      initMap(state.mapIns)
      state.mapIns.resize()
    })
  }
  const onMapZoomEnd = currentZoom => {
    state.zoom = currentZoom
  }

  const range = ref([])
  const year = ref(null)

  watch(
    () => state.geojsonList,
    () => {
      nextTick(() => {
        dealLayers()
      })
    },
  )
  const initTime = () => {
    const startTime = dayjs().subtract(48, 'hour').format() // 当前时间前48小时
    const endTime = dayjs().add(24, 'hour').format() // 当前时间后24小时
    range.value = [dayjs(startTime).format('YYYY-MM-DD HH'), dayjs(endTime).format('YYYY-MM-DD HH')]
  }
  const getData = async type => {
    let rainList = null
    let rainConvert = null
    let geoList=null
    let geoConvert=null
    if (type !== 2) {
      rainList = await futureRainList({
        type: 1,
        startTime: state.newRange[0][0],
        endTime: state.newRange[0][1],
      })
      geoList=await getContourSurface({
        startTime: dayjs(state.newRange[0][0]).format('YYYY-MM-DD HH'),
        endTime: dayjs(state.newRange[0][1]).format('YYYY-MM-DD HH') ,
        isFuture:false
      }) 
    }
    if (type !== 1) {
      rainConvert = await futureRainConvert({
        startTime: state.newRange[1][0],
        endTime: state.newRange[1][1],
      })
      geoConvert=await getContourSurface({
        startTime:dayjs(state.newRange[1][0]).format('YYYY-MM-DD HH') ,
        endTime:dayjs(state.newRange[1][1]).format('YYYY-MM-DD HH'), 
        isFuture:true
      }) 
    }
    let newList =
      type == 1 ? rainList?.data : type == 2 ? rainConvert?.data : type == 3 ? rainList?.data?.concat(rainConvert.data) : []
    let newGeoList =
      type == 1 ? geoList?.data : type == 2 ? geoConvert?.data : type == 3 ? geoList?.data?.concat(geoConvert.data) : []
    state.geojsonList = newGeoList
    state.lineChartData = [
      {
        xData: newList?.map(el => dayjs(el.dateTime).format('MM-DD HH')),
        data: newList?.map(el => {
          const site = el.sites[0]
          return [dayjs(el.dateTime).format('MM-DD HH'), site.rain]
        }),
      },
    ]

    state.times = newGeoList?.map(el => dayjs(el.dateTime).format('YYYY-MM-DD HH'))
    state.rainSum = newList?.reduce((accumulator, item) => {
      const site = item.sites[0] // 假设每个 dateTime 只有一个站点
      if (site && site.rain !== null) {
        accumulator += parseFloat(site.rain)
      }
      return accumulator
    }, 0)
  }
  //事件
  const getList = async () => {
    getOptions('rainfall_class_range').then(res => {
      state.rainLevelOptions = res.data.map(el => ({
        ...el,
        label: el.option2 ? `${el.option1}~${el.option2}` : `>${el.option1}`,
        color: el.value,
        value: el.key,
      }))
    })

    let newRange = [range.value[0], range.value[1]]
    let nowTime = dayjs()
    const convertTime = dayjs(nowTime).add(1, 'hour').format('YYYY-MM-DD HH:mm:ss')
    state.resetTime++
    const startTime = dayjs(newRange[0])
    const endTime = dayjs(newRange[1])
    // 判断nowTime是否在times范围内
    if (!startTime.isBefore(nowTime) || !endTime.isAfter(nowTime)) {
      if (nowTime.isAfter(endTime)) {
        state.newRange = [newRange, []]
        getData(1)
      } else if (nowTime.isBefore(startTime)) {
        state.newRange = [[], newRange]
        getData(2)
      }
    } else {
      state.newRange = [
        [newRange[0], nowTime.format('YYYY-MM-DD HH:mm:ss')],
        [convertTime, newRange[1]],
      ]
      getData(3)
    }
  }
  const onTimeChange = (time, tIdx) => {
    const current = state.geojsonList.find(el => el.dateTime === time)

    if (!!current.url) {
      axios.get(current.url).then(res => {
        state.currentGeo = res.data
      })
    } else {
      state.currentGeo = {
        type: 'FeatureCollection',
        features: [
          {
            type: 'Feature',
            geometry: {
              type: 'MultiPolygon',
              coordinates: [[[[102.0, 2.0]]]],
            },
            properties: {
              lower: 0,
              upper: 0,
            },
          },
        ],
      }
    }
  }

  const dealLayers = () => {
    state.times = state.geojsonList?.map(el => el.dateTime)
  }
</script>
<style lang="scss" scoped>
  :deep(.n-time-picker .n-input .n-input__input-el) {
    height: 20px !important;
  }

  /* 如果需要调整其他相关元素的高度，可以继续添加相应的样式规则 */
  :deep(.n-time-picker .n-input .n-input__suffix) {
    height: 20px !important;
    line-height: 20px !important;
  }
  .charts {
    opacity: 1;
    border-radius: 0.04rem;
    background: linear-gradient(180deg, rgba(9, 43, 49, 0) 57%, rgba(29, 117, 132, 0.22) 99%), rgba(18, 33, 41, 0.71);

    box-sizing: border-box;
    border: 1px solid rgba(54, 96, 103, 0.5);
  }
  .today-rainfall {
    border-radius: 2px;
    opacity: 1;

    background: linear-gradient(0deg, #15799b -41%, rgba(21, 111, 141, 0) 90%);

    box-sizing: border-box;
    border: 1px solid;
    border-image: linear-gradient(180deg, #ffffff 0%, rgba(255, 255, 255, 0.66) 0%, rgba(255, 255, 255, 0) 100%) 1;

    font-family: Source Han Sans;
    font-size: 0.12rem;
    font-weight: 350;
    line-height: 0.25rem;
    // line-height: normal;
    // letter-spacing: 0em;
    // color: #def2fc;
  }
  .legend {
    // width: 120px;
    right: 0.56rem;
    bottom: 0.46rem;
    position: absolute;
    z-index: 999;
    width: 1.18rem;
    height: 2rem;
    border-radius: 0.04rem;
    opacity: 1;
    padding: 0.1rem 0 0 0.08rem;
    background: rgba(18, 33, 41, 0.6);
    .legend-item:nth-child(n):not(:last-child) {
      margin-bottom: 0.06rem;
    }
    .legend-item {
      font-size: 0.13rem;
      padding: 0;
      // display: inline-block;
      display: flex;
    }
  }
</style>
