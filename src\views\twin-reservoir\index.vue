<template>
  <div class="w-100vw h-100vh relative user-select">
    <div class="w-100vw h-100vh absolute" v-resize="onResize">
      <div class="w-5.07rem h-5.2rem absolute right-0rem top-0rem uno-bg_/bamboo.png z-8 pointer-events-none"></div>

      <div class="absolute left-2.8rem top-0.07rem decorate-top"></div>
      <div class="absolute left-2.8rem bottom-0.07rem decorate-bottom"></div>

      <div
        class="w-0.07rem h-6.64rem absolute left-0.11rem top-1.32rem uno-bg_twin-reservoir/decorate-left.png z-9 pointer-events-none"
      ></div>
      <div
        class="w-0.07rem h-6.64rem absolute top-2.6rem right-0.11rem rotate-(y-180) uno-bg_twin-reservoir/decorate-left.png z-9 pointer-events-none"
      ></div>

      <div class="decorate-right-angle left-top absolute left-0.05rem top-0.05rem z-9 pointer-events-none"></div>
      <div class="decorate-right-angle absolute right-0.05rem top-0.05rem rotate-180 z-9 pointer-events-none"></div>
      <div class="decorate-right-angle absolute left-0.05rem bottom-0.05rem pointer-events-none"></div>
      <div class="decorate-right-angle absolute right-0.05rem bottom-0.05rem rotate-(y-180) pointer-events-none"></div>

      <header class="header w-[calc(100%-0.46rem)] h-0.61rem bg-[rgba(9,200,252,0.1)] absolute left-0.23rem top-0.3rem z-22 flex">
        <div class="w-2.6rem c-#fff pl-0.1rem pt-0.16rem">
          <n-button class="back-btn text-0.16rem c-#fff pl-0.24rem" @click="back">返回</n-button>
          <i :class="[`qi-${state.weather?.[state.weather.iconType]}`, 'ml-0.16rem mr-0.12rem text-0.18rem']"></i>
          <span class="text-0.15rem c-#B2DAEA">{{ state.weather?.textDay }}</span>
          <span class="ml-0.1rem text-0.18rem">{{ state.weather?.tempMin }}℃-{{ state.weather?.tempMax }}℃</span>
          <span></span>
        </div>
        <div class="flex-1">
          <h1
            class="w-11.38rem h-0.62rem m-auto c-#fff text-center title uno-bg_twin-reservoir/decorate-header.png pointer-events-none"
          >
            桃花江数字孪生水库
          </h1>
        </div>
        <div class="w-2.6rem ml-auto lh-0.61rem pr-0.1rem">
          <span class="text-0.16rem c-#B2DAEA">{{ dayjs().format('YYYY-MM-DD') }}</span>
          <span class="ml-0.1rem mr-0.1rem text-0.14rem c-#B2DAEA">周{{ getWeek(dayjs().day()) }}</span>
          <span class="font-[KwangLiangWine] c-#fff text-0.18rem">{{ state.time }}</span>
        </div>
      </header>

      <div class="w-100vw h-100vh relative">
        <ReservoirLeft v-bind="state.reservoirSummary" />
        <CesiumMap :waterLevel="state.reservoirSummary?.waterLevel" :isBack="state.isBack" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts" name="TwinReservoir">
  import { useSizeStore } from '@/store'
  import dayjs from 'dayjs'
  import { getTankWaterLevel } from '@/views/home/<USER>'

  import ReservoirLeft from './components/Left/index.vue'
  import CesiumMap from './components/Map/index.vue'

  import { platforms } from '@/constants'
  import { getTodayWeather } from '@/api'

  const router = useRouter()
  const sizeStore = useSizeStore()

  const state = reactive({
    weather: {},
    timer: null,
    time: undefined,
    reservoirSummary: {},
    isBack: false,
  })

  onMounted(() => {
    getTime()
    getTankWaterLevel().then(res => {
      state.reservoirSummary = res?.data
    })
  })

  const getTime = () => {
    state.timer = setInterval(() => {
      state.time = dayjs().format('HH:mm:ss')
    }, 1000)
  }
  const getWeek = e => {
    var week = ['一', '二', '三', '四', '五', '六', '日']
    return week[e - 1]
  }
  function getToday() {
    let date = new Date()
    let year = date.getFullYear()
    let month: any = date.getMonth() + 1
    let day: any = date.getDate()
    month = month > 9 ? month : '0' + month
    day = day < 10 ? '0' + day : day
    return year + '-' + month + '-' + day
  }

  const getInfo = async () => {
    const res: any = await getTodayWeather({ locationCode: platforms.weatherCode })
    state.weather = res?.data

    const sunsetTime = `${getToday()} ${state.weather.sunset}`
    const sunriseTime = `${getToday()} ${state.weather.sunrise}`
    if (+new Date() < dayjs(sunsetTime).valueOf() && +new Date() > dayjs(sunriseTime).valueOf()) {
      state.weather.iconType = 'iconDay'
    } else {
      state.weather.iconType = 'iconNight'
    }
  }
  getInfo()

  const onResize = () => {
    sizeStore.onWindowResize()
  }

  const back = () => {
    state.isBack = true
    router.go(-1)
  }
</script>
<style lang="scss" scoped>
  .user-select {
    user-select: none;
  }
  .mask {
    background:
      radial-gradient(54% 55% at 50% 50%, rgba(16, 31, 46, 0) 0%, rgba(14, 26, 39, 0) 82%, rgba(12, 22, 32, 0.62) 97%),
      rgba(9, 57, 65, 0.39);
    background-blend-mode: normal, exclusion;
  }
  .decorate-top,
  .decorate-bottom {
    width: calc(100% - 5.6rem);
    height: 0.18rem;
    z-index: 999;
  }
  .decorate-top {
    background-image: url('@/assets/images/twin-reservoir/decorate-top-center.png');
    background-repeat: round;
  }
  .decorate-bottom {
    background: url('@/assets/images/twin-reservoir/decorate-bottom-center.png');
    background-repeat: round;
  }
  .decorate-right-angle {
    width: 2.71rem;
    height: 0.96rem;
    z-index: 999;
    background: url('@/assets/images/twin-reservoir/decorate-angle.png') no-repeat center / 100% 100%;
    &.left-top {
      transform: scaleY(-1);
    }
  }
  .header {
    .title {
      font-family: KwangLiangWine;
      font-size: 0.4rem;
      font-weight: normal;
      line-height: 0.61rem;
      letter-spacing: 0.05em;
      text-shadow: 0px 4px 10px #229081;
    }
    .back-btn {
      width: 0.68rem;
      height: 0.28rem;
      border-radius: 0.08rem;
      background:
        radial-gradient(75% 65% at 47% 100%, #09fc4a 0%, rgba(9, 252, 220, 0) 94%),
        linear-gradient(180deg, #098ffc 0%, rgba(85, 130, 148, 0) 100%);
      &::before {
        content: '';
        position: absolute;
        left: 0.06rem;
        top: 0.05rem;
        width: 0.16rem;
        height: 0.16rem;
        background: url('@/assets/images/back-btn.png') no-repeat center / 100% 100%;
      }
    }
  }
</style>
